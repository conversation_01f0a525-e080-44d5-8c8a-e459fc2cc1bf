{"name": "insurance-crm", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.22.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.39.0", "autoprefixer": "^10.4.17", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "1.0.4", "csv-parser": "^3.2.0", "date-fns": "^3.6.0", "embla-carousel-react": "8.5.1", "exceljs": "^4.4.0", "form-data": "^4.0.3", "framer-motion": "^11.0.0", "googleapis": "^128.0.0", "input-otp": "1.4.1", "lucide-react": "^0.344.0", "next": "^14.2.30", "next-themes": "^0.2.1", "node-fetch": "^3.3.2", "nodemailer": "^6.10.0", "openai": "^5.8.2", "papaparse": "^5.5.3", "postcss": "^8.4.33", "react": "18.3.1", "react-day-picker": "8.10.1", "react-dom": "18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.49.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.12.0", "sonner": "^1.7.1", "string-similarity": "^4.0.4", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.4.1", "twilio": "^5.5.1", "uuid": "^11.1.0", "vaul": "^0.9.6", "zod": "^3.22.4", "zustand": "^5.0.3"}, "devDependencies": {"@babel/eslint-parser": "^7.26.10", "@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.22.0", "@types/dotenv": "^8.2.3", "@types/next": "^8.0.7", "@types/node": "^20.11.0", "@types/nodemailer": "^6.4.17", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@types/string-similarity": "^4.0.2", "@types/twilio": "^3.19.3", "@types/xlsx": "^0.0.35", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "autoprefixer": "^10.4.21", "dotenv": "^16.5.0", "eslint": "^8.56.0", "eslint-config-next": "14.2.5", "eslint-plugin-react": "^7.37.4", "globals": "^16.0.0", "postcss": "^8.5.3", "supabase": "^2.31.8", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}