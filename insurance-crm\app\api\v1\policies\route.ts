import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { withAPIAuth, APIContext } from '@/lib/api/auth';
// Note: Import commission calculation function if available
// import { calculateMonthlyCommission } from '@/lib/commission-config';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * GET /api/v1/policies
 * Retrieve all policies for the authenticated user
 */
async function handleGET(request: NextRequest, context: APIContext): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status');
    const carrier = searchParams.get('carrier');
    const state = searchParams.get('state');
    const type = searchParams.get('type');
    const clientId = searchParams.get('client_id');

    // Build query
    let query = supabase
      .from('policies')
      .select(`
        id,
        client_id,
        plan_name,
        carrier,
        type,
        status,
        premium,
        gross_premium,
        net_premium,
        start_date,
        end_date,
        effective_date,
        termination_date,
        policy_number,
        member_id,
        group_number,
        metal_level,
        deductible,
        out_of_pocket_max,
        state,
        member_count,
        is_renewal,
        is_off_exchange,
        monthly_commission,
        annual_commission,
        commission_rate,
        created_at,
        updated_at,
        client:active_clients (
          id,
          first_name,
          last_name,
          email,
          phone
        )
      `)
      .eq('clerk_user_id', context.userId)
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (carrier) {
      query = query.eq('carrier', carrier);
    }
    if (state) {
      query = query.eq('state', state);
    }
    if (type) {
      query = query.eq('type', type);
    }
    if (clientId) {
      query = query.eq('client_id', clientId);
    }

    const { data: policies, error } = await query;

    if (error) {
      console.error('Error fetching policies:', error);
      return NextResponse.json(
        { error: 'Failed to fetch policies', details: error.message },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('policies')
      .select('*', { count: 'exact', head: true })
      .eq('clerk_user_id', context.userId);

    if (status) countQuery = countQuery.eq('status', status);
    if (carrier) countQuery = countQuery.eq('carrier', carrier);
    if (state) countQuery = countQuery.eq('state', state);
    if (type) countQuery = countQuery.eq('type', type);
    if (clientId) countQuery = countQuery.eq('client_id', clientId);

    const { count } = await countQuery;

    return NextResponse.json({
      success: true,
      data: policies,
      pagination: {
        limit,
        offset,
        total: count || 0,
        hasMore: (offset + limit) < (count || 0)
      },
      filters: {
        status,
        carrier,
        state,
        type,
        client_id: clientId
      }
    });

  } catch (error) {
    console.error('Policies API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/policies
 * Create a new policy
 */
async function handlePOST(request: NextRequest, context: APIContext): Promise<NextResponse> {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['client_id', 'carrier', 'state'];
    const missingFields = requiredFields.filter(field => !body[field]);
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Verify client exists and belongs to user
    const { data: client, error: clientError } = await supabase
      .from('active_clients')
      .select('id')
      .eq('id', body.client_id)
      .single();

    if (clientError || !client) {
      return NextResponse.json(
        { error: 'Client not found or access denied' },
        { status: 404 }
      );
    }

    // Calculate commission if not provided (simplified calculation)
    let monthlyCommission = body.monthly_commission;
    let annualCommission = body.annual_commission;

    if (!monthlyCommission && body.carrier && body.state) {
      // Simple commission calculation - replace with actual logic
      const baseRate = 25; // Default $25 per member per month
      monthlyCommission = baseRate * (body.member_count || 1);
      annualCommission = monthlyCommission * 12;
    }

    // Prepare policy data
    const policyData = {
      clerk_user_id: context.userId,
      client_id: body.client_id,
      plan_name: body.plan_name,
      carrier: body.carrier,
      type: body.type || 'Health',
      status: body.status || 'Active',
      premium: body.premium,
      gross_premium: body.gross_premium,
      net_premium: body.net_premium,
      start_date: body.start_date,
      end_date: body.end_date,
      effective_date: body.effective_date,
      termination_date: body.termination_date,
      policy_number: body.policy_number,
      member_id: body.member_id,
      group_number: body.group_number,
      metal_level: body.metal_level,
      deductible: body.deductible,
      out_of_pocket_max: body.out_of_pocket_max,
      state: body.state,
      member_count: body.member_count || 1,
      is_renewal: body.is_renewal || false,
      is_off_exchange: body.is_off_exchange || false,
      monthly_commission: monthlyCommission,
      annual_commission: annualCommission,
      commission_rate: body.commission_rate
    };

    const { data: policy, error } = await supabase
      .from('policies')
      .insert(policyData)
      .select(`
        *,
        client:active_clients (
          id,
          first_name,
          last_name,
          email,
          phone
        )
      `)
      .single();

    if (error) {
      console.error('Error creating policy:', error);
      return NextResponse.json(
        { error: 'Failed to create policy', details: error.message },
        { status: 500 }
      );
    }

    // Create activity for policy creation
    await supabase
      .from('activities')
      .insert({
        clerk_user_id: context.userId,
        client_id: body.client_id,
        type: 'policy',
        title: 'Policy Created via API',
        description: `New ${body.carrier} policy created: ${body.plan_name || 'N/A'}`,
      });

    return NextResponse.json({
      success: true,
      data: policy,
      message: 'Policy created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create policy API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Export the wrapped handlers
export const GET = withAPIAuth(handleGET, 'read');
export const POST = withAPIAuth(handlePOST, 'write');
