"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@dnd-kit";
exports.ids = ["vendor-chunks/@dnd-kit"];
exports.modules = {

/***/ "(ssr)/./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HiddenText: () => (/* binding */ HiddenText),\n/* harmony export */   LiveRegion: () => (/* binding */ LiveRegion),\n/* harmony export */   useAnnouncement: () => (/* binding */ useAnnouncement)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst hiddenStyles = {\n  display: 'none'\n};\nfunction HiddenText(_ref) {\n  let {\n    id,\n    value\n  } = _ref;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: id,\n    style: hiddenStyles\n  }, value);\n}\n\nfunction LiveRegion(_ref) {\n  let {\n    id,\n    announcement,\n    ariaLiveType = \"assertive\"\n  } = _ref;\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap'\n  };\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: id,\n    style: visuallyHidden,\n    role: \"status\",\n    \"aria-live\": ariaLiveType,\n    \"aria-atomic\": true\n  }, announcement);\n}\n\nfunction useAnnouncement() {\n  const [announcement, setAnnouncement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n  const announce = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(value => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n  return {\n    announce,\n    announcement\n  };\n}\n\n\n//# sourceMappingURL=accessibility.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGRuZC1raXQvYWNjZXNzaWJpbGl0eS9kaXN0L2FjY2Vzc2liaWxpdHkuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFEOztBQUVyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixTQUFTLDBEQUFtQjtBQUM1QjtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUywwREFBbUI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBLDBDQUEwQywrQ0FBUTtBQUNsRCxtQkFBbUIsa0RBQVc7QUFDOUI7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW1EO0FBQ25EIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8uL25vZGVfbW9kdWxlcy9AZG5kLWtpdC9hY2Nlc3NpYmlsaXR5L2Rpc3QvYWNjZXNzaWJpbGl0eS5lc20uanM/MjY4NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuXG5jb25zdCBoaWRkZW5TdHlsZXMgPSB7XG4gIGRpc3BsYXk6ICdub25lJ1xufTtcbmZ1bmN0aW9uIEhpZGRlblRleHQoX3JlZikge1xuICBsZXQge1xuICAgIGlkLFxuICAgIHZhbHVlXG4gIH0gPSBfcmVmO1xuICByZXR1cm4gUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgaWQ6IGlkLFxuICAgIHN0eWxlOiBoaWRkZW5TdHlsZXNcbiAgfSwgdmFsdWUpO1xufVxuXG5mdW5jdGlvbiBMaXZlUmVnaW9uKF9yZWYpIHtcbiAgbGV0IHtcbiAgICBpZCxcbiAgICBhbm5vdW5jZW1lbnQsXG4gICAgYXJpYUxpdmVUeXBlID0gXCJhc3NlcnRpdmVcIlxuICB9ID0gX3JlZjtcbiAgLy8gSGlkZSBlbGVtZW50IHZpc3VhbGx5IGJ1dCBrZWVwIGl0IHJlYWRhYmxlIGJ5IHNjcmVlbiByZWFkZXJzXG4gIGNvbnN0IHZpc3VhbGx5SGlkZGVuID0ge1xuICAgIHBvc2l0aW9uOiAnZml4ZWQnLFxuICAgIHRvcDogMCxcbiAgICBsZWZ0OiAwLFxuICAgIHdpZHRoOiAxLFxuICAgIGhlaWdodDogMSxcbiAgICBtYXJnaW46IC0xLFxuICAgIGJvcmRlcjogMCxcbiAgICBwYWRkaW5nOiAwLFxuICAgIG92ZXJmbG93OiAnaGlkZGVuJyxcbiAgICBjbGlwOiAncmVjdCgwIDAgMCAwKScsXG4gICAgY2xpcFBhdGg6ICdpbnNldCgxMDAlKScsXG4gICAgd2hpdGVTcGFjZTogJ25vd3JhcCdcbiAgfTtcbiAgcmV0dXJuIFJlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgIGlkOiBpZCxcbiAgICBzdHlsZTogdmlzdWFsbHlIaWRkZW4sXG4gICAgcm9sZTogXCJzdGF0dXNcIixcbiAgICBcImFyaWEtbGl2ZVwiOiBhcmlhTGl2ZVR5cGUsXG4gICAgXCJhcmlhLWF0b21pY1wiOiB0cnVlXG4gIH0sIGFubm91bmNlbWVudCk7XG59XG5cbmZ1bmN0aW9uIHVzZUFubm91bmNlbWVudCgpIHtcbiAgY29uc3QgW2Fubm91bmNlbWVudCwgc2V0QW5ub3VuY2VtZW50XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgYW5ub3VuY2UgPSB1c2VDYWxsYmFjayh2YWx1ZSA9PiB7XG4gICAgaWYgKHZhbHVlICE9IG51bGwpIHtcbiAgICAgIHNldEFubm91bmNlbWVudCh2YWx1ZSk7XG4gICAgfVxuICB9LCBbXSk7XG4gIHJldHVybiB7XG4gICAgYW5ub3VuY2UsXG4gICAgYW5ub3VuY2VtZW50XG4gIH07XG59XG5cbmV4cG9ydCB7IEhpZGRlblRleHQsIExpdmVSZWdpb24sIHVzZUFubm91bmNlbWVudCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWNjZXNzaWJpbGl0eS5lc20uanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@dnd-kit/core/dist/core.esm.js":
/*!*****************************************************!*\
  !*** ./node_modules/@dnd-kit/core/dist/core.esm.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoScrollActivator: () => (/* binding */ AutoScrollActivator),\n/* harmony export */   DndContext: () => (/* binding */ DndContext),\n/* harmony export */   DragOverlay: () => (/* binding */ DragOverlay),\n/* harmony export */   KeyboardCode: () => (/* binding */ KeyboardCode),\n/* harmony export */   KeyboardSensor: () => (/* binding */ KeyboardSensor),\n/* harmony export */   MeasuringFrequency: () => (/* binding */ MeasuringFrequency),\n/* harmony export */   MeasuringStrategy: () => (/* binding */ MeasuringStrategy),\n/* harmony export */   MouseSensor: () => (/* binding */ MouseSensor),\n/* harmony export */   PointerSensor: () => (/* binding */ PointerSensor),\n/* harmony export */   TouchSensor: () => (/* binding */ TouchSensor),\n/* harmony export */   TraversalOrder: () => (/* binding */ TraversalOrder),\n/* harmony export */   applyModifiers: () => (/* binding */ applyModifiers),\n/* harmony export */   closestCenter: () => (/* binding */ closestCenter),\n/* harmony export */   closestCorners: () => (/* binding */ closestCorners),\n/* harmony export */   defaultAnnouncements: () => (/* binding */ defaultAnnouncements),\n/* harmony export */   defaultCoordinates: () => (/* binding */ defaultCoordinates),\n/* harmony export */   defaultDropAnimation: () => (/* binding */ defaultDropAnimationConfiguration),\n/* harmony export */   defaultDropAnimationSideEffects: () => (/* binding */ defaultDropAnimationSideEffects),\n/* harmony export */   defaultKeyboardCoordinateGetter: () => (/* binding */ defaultKeyboardCoordinateGetter),\n/* harmony export */   defaultScreenReaderInstructions: () => (/* binding */ defaultScreenReaderInstructions),\n/* harmony export */   getClientRect: () => (/* binding */ getClientRect),\n/* harmony export */   getFirstCollision: () => (/* binding */ getFirstCollision),\n/* harmony export */   getScrollableAncestors: () => (/* binding */ getScrollableAncestors),\n/* harmony export */   pointerWithin: () => (/* binding */ pointerWithin),\n/* harmony export */   rectIntersection: () => (/* binding */ rectIntersection),\n/* harmony export */   useDndContext: () => (/* binding */ useDndContext),\n/* harmony export */   useDndMonitor: () => (/* binding */ useDndMonitor),\n/* harmony export */   useDraggable: () => (/* binding */ useDraggable),\n/* harmony export */   useDroppable: () => (/* binding */ useDroppable),\n/* harmony export */   useSensor: () => (/* binding */ useSensor),\n/* harmony export */   useSensors: () => (/* binding */ useSensors)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/utilities */ \"(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n/* harmony import */ var _dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/accessibility */ \"(ssr)/./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js\");\n\n\n\n\n\nconst DndMonitorContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n\nfunction useDndMonitor(listener) {\n  const registerListener = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(DndMonitorContext);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!registerListener) {\n      throw new Error('useDndMonitor must be used within a children of <DndContext>');\n    }\n\n    const unsubscribe = registerListener(listener);\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n\nfunction useDndMonitorProvider() {\n  const [listeners] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new Set());\n  const registerListener = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(listener => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  }, [listeners]);\n  const dispatch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(_ref => {\n    let {\n      type,\n      event\n    } = _ref;\n    listeners.forEach(listener => {\n      var _listener$type;\n\n      return (_listener$type = listener[type]) == null ? void 0 : _listener$type.call(listener, event);\n    });\n  }, [listeners]);\n  return [dispatch, registerListener];\n}\n\nconst defaultScreenReaderInstructions = {\n  draggable: \"\\n    To pick up a draggable item, press the space bar.\\n    While dragging, use the arrow keys to move the item.\\n    Press space again to drop the item in its new position, or press escape to cancel.\\n  \"\n};\nconst defaultAnnouncements = {\n  onDragStart(_ref) {\n    let {\n      active\n    } = _ref;\n    return \"Picked up draggable item \" + active.id + \".\";\n  },\n\n  onDragOver(_ref2) {\n    let {\n      active,\n      over\n    } = _ref2;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was moved over droppable area \" + over.id + \".\";\n    }\n\n    return \"Draggable item \" + active.id + \" is no longer over a droppable area.\";\n  },\n\n  onDragEnd(_ref3) {\n    let {\n      active,\n      over\n    } = _ref3;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was dropped over droppable area \" + over.id;\n    }\n\n    return \"Draggable item \" + active.id + \" was dropped.\";\n  },\n\n  onDragCancel(_ref4) {\n    let {\n      active\n    } = _ref4;\n    return \"Dragging was cancelled. Draggable item \" + active.id + \" was dropped.\";\n  }\n\n};\n\nfunction Accessibility(_ref) {\n  let {\n    announcements = defaultAnnouncements,\n    container,\n    hiddenTextDescribedById,\n    screenReaderInstructions = defaultScreenReaderInstructions\n  } = _ref;\n  const {\n    announce,\n    announcement\n  } = (0,_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.useAnnouncement)();\n  const liveRegionId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(\"DndLiveRegion\");\n  const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setMounted(true);\n  }, []);\n  useDndMonitor((0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    onDragStart(_ref2) {\n      let {\n        active\n      } = _ref2;\n      announce(announcements.onDragStart({\n        active\n      }));\n    },\n\n    onDragMove(_ref3) {\n      let {\n        active,\n        over\n      } = _ref3;\n\n      if (announcements.onDragMove) {\n        announce(announcements.onDragMove({\n          active,\n          over\n        }));\n      }\n    },\n\n    onDragOver(_ref4) {\n      let {\n        active,\n        over\n      } = _ref4;\n      announce(announcements.onDragOver({\n        active,\n        over\n      }));\n    },\n\n    onDragEnd(_ref5) {\n      let {\n        active,\n        over\n      } = _ref5;\n      announce(announcements.onDragEnd({\n        active,\n        over\n      }));\n    },\n\n    onDragCancel(_ref6) {\n      let {\n        active,\n        over\n      } = _ref6;\n      announce(announcements.onDragCancel({\n        active,\n        over\n      }));\n    }\n\n  }), [announce, announcements]));\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.HiddenText, {\n    id: hiddenTextDescribedById,\n    value: screenReaderInstructions.draggable\n  }), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.LiveRegion, {\n    id: liveRegionId,\n    announcement: announcement\n  }));\n  return container ? (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(markup, container) : markup;\n}\n\nvar Action;\n\n(function (Action) {\n  Action[\"DragStart\"] = \"dragStart\";\n  Action[\"DragMove\"] = \"dragMove\";\n  Action[\"DragEnd\"] = \"dragEnd\";\n  Action[\"DragCancel\"] = \"dragCancel\";\n  Action[\"DragOver\"] = \"dragOver\";\n  Action[\"RegisterDroppable\"] = \"registerDroppable\";\n  Action[\"SetDroppableDisabled\"] = \"setDroppableDisabled\";\n  Action[\"UnregisterDroppable\"] = \"unregisterDroppable\";\n})(Action || (Action = {}));\n\nfunction noop() {}\n\nfunction useSensor(sensor, options) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    sensor,\n    options: options != null ? options : {}\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [sensor, options]);\n}\n\nfunction useSensors() {\n  for (var _len = arguments.length, sensors = new Array(_len), _key = 0; _key < _len; _key++) {\n    sensors[_key] = arguments[_key];\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => [...sensors].filter(sensor => sensor != null), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...sensors]);\n}\n\nconst defaultCoordinates = /*#__PURE__*/Object.freeze({\n  x: 0,\n  y: 0\n});\n\n/**\r\n * Returns the distance between two points\r\n */\nfunction distanceBetween(p1, p2) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n\nfunction getRelativeTransformOrigin(event, rect) {\n  const eventCoordinates = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: (eventCoordinates.x - rect.left) / rect.width * 100,\n    y: (eventCoordinates.y - rect.top) / rect.height * 100\n  };\n  return transformOrigin.x + \"% \" + transformOrigin.y + \"%\";\n}\n\n/**\r\n * Sort collisions from smallest to greatest value\r\n */\nfunction sortCollisionsAsc(_ref, _ref2) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref2;\n  return a - b;\n}\n/**\r\n * Sort collisions from greatest to smallest value\r\n */\n\nfunction sortCollisionsDesc(_ref3, _ref4) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref3;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref4;\n  return b - a;\n}\n/**\r\n * Returns the coordinates of the corners of a given rectangle:\r\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\r\n */\n\nfunction cornersOfRectangle(_ref5) {\n  let {\n    left,\n    top,\n    height,\n    width\n  } = _ref5;\n  return [{\n    x: left,\n    y: top\n  }, {\n    x: left + width,\n    y: top\n  }, {\n    x: left,\n    y: top + height\n  }, {\n    x: left + width,\n    y: top + height\n  }];\n}\nfunction getFirstCollision(collisions, property) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n  return property ? firstCollision[property] : firstCollision;\n}\n\n/**\r\n * Returns the coordinates of the center of a given ClientRect\r\n */\n\nfunction centerOfRectangle(rect, left, top) {\n  if (left === void 0) {\n    left = rect.left;\n  }\n\n  if (top === void 0) {\n    top = rect.top;\n  }\n\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5\n  };\n}\n/**\r\n * Returns the closest rectangles from an array of rectangles to the center of a given\r\n * rectangle.\r\n */\n\n\nconst closestCenter = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const centerRect = centerOfRectangle(collisionRect, collisionRect.left, collisionRect.top);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: distBetween\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the closest rectangles from an array of rectangles to the corners of\r\n * another rectangle.\r\n */\n\nconst closestCorners = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the intersecting rectangle area between two rectangles\r\n */\n\nfunction getIntersectionRatio(entry, target) {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio = intersectionArea / (targetArea + entryArea - intersectionArea);\n    return Number(intersectionRatio.toFixed(4));\n  } // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n\n\n  return 0;\n}\n/**\r\n * Returns the rectangles that has the greatest intersection area with a given\r\n * rectangle in an array of rectangles.\r\n */\n\nconst rectIntersection = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {\n            droppableContainer,\n            value: intersectionRatio\n          }\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n\n/**\r\n * Check if a given point is contained within a bounding rectangle\r\n */\n\nfunction isPointWithinRect(point, rect) {\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = rect;\n  return top <= point.y && point.y <= bottom && left <= point.x && point.x <= right;\n}\n/**\r\n * Returns the rectangles that the pointer is hovering over\r\n */\n\n\nconst pointerWithin = _ref => {\n  let {\n    droppableContainers,\n    droppableRects,\n    pointerCoordinates\n  } = _ref;\n\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\r\n       * with the pointer coordinates. In order to sort the\r\n       * colliding rectangles, we measure the distance between\r\n       * the pointer and the corners of the intersecting rectangle\r\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\nfunction adjustScale(transform, rect1, rect2) {\n  return { ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1\n  };\n}\n\nfunction getRectDelta(rect1, rect2) {\n  return rect1 && rect2 ? {\n    x: rect1.left - rect2.left,\n    y: rect1.top - rect2.top\n  } : defaultCoordinates;\n}\n\nfunction createRectAdjustmentFn(modifier) {\n  return function adjustClientRect(rect) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((acc, adjustment) => ({ ...acc,\n      top: acc.top + modifier * adjustment.y,\n      bottom: acc.bottom + modifier * adjustment.y,\n      left: acc.left + modifier * adjustment.x,\n      right: acc.right + modifier * adjustment.x\n    }), { ...rect\n    });\n  };\n}\nconst getAdjustedRect = /*#__PURE__*/createRectAdjustmentFn(1);\n\nfunction parseTransform(transform) {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5]\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3]\n    };\n  }\n\n  return null;\n}\n\nfunction inverseTransform(rect, transform, transformOrigin) {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {\n    scaleX,\n    scaleY,\n    x: translateX,\n    y: translateY\n  } = parsedTransform;\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y = rect.top - translateY - (1 - scaleY) * parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x\n  };\n}\n\nconst defaultOptions = {\n  ignoreTransform: false\n};\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n */\n\nfunction getClientRect(element, options) {\n  if (options === void 0) {\n    options = defaultOptions;\n  }\n\n  let rect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {\n      transform,\n      transformOrigin\n    } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  } = rect;\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  };\n}\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n *\r\n * @remarks\r\n * The ClientRect returned by this method does not take into account transforms\r\n * applied to the element it measures.\r\n *\r\n */\n\nfunction getTransformAgnosticClientRect(element) {\n  return getClientRect(element, {\n    ignoreTransform: true\n  });\n}\n\nfunction getWindowClientRect(element) {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height\n  };\n}\n\nfunction isFixed(node, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(node).getComputedStyle(node);\n  }\n\n  return computedStyle.position === 'fixed';\n}\n\nfunction isScrollable(element, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(element);\n  }\n\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n  return properties.some(property => {\n    const value = computedStyle[property];\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n\nfunction getScrollableAncestors(element, limit) {\n  const scrollParents = [];\n\n  function findScrollableAncestors(node) {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isDocument)(node) && node.scrollingElement != null && !scrollParents.includes(node.scrollingElement)) {\n      scrollParents.push(node.scrollingElement);\n      return scrollParents;\n    }\n\n    if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(node) || (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isSVGElement)(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\nfunction getFirstScrollableAncestor(node) {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n  return firstScrollableAncestor != null ? firstScrollableAncestor : null;\n}\n\nfunction getScrollableElement(element) {\n  if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM || !element) {\n    return null;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element;\n  }\n\n  if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isNode)(element)) {\n    return null;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isDocument)(element) || element === (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(element).scrollingElement) {\n    return window;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(element)) {\n    return element;\n  }\n\n  return null;\n}\n\nfunction getScrollXCoordinate(element) {\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\nfunction getScrollYCoordinate(element) {\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\nfunction getScrollCoordinates(element) {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element)\n  };\n}\n\nvar Direction;\n\n(function (Direction) {\n  Direction[Direction[\"Forward\"] = 1] = \"Forward\";\n  Direction[Direction[\"Backward\"] = -1] = \"Backward\";\n})(Direction || (Direction = {}));\n\nfunction isDocumentScrollingElement(element) {\n  if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n\nfunction getScrollPosition(scrollingContainer) {\n  const minScroll = {\n    x: 0,\n    y: 0\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer) ? {\n    height: window.innerHeight,\n    width: window.innerWidth\n  } : {\n    height: scrollingContainer.clientHeight,\n    width: scrollingContainer.clientWidth\n  };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height\n  };\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll\n  };\n}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2\n};\nfunction getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, _ref, acceleration, thresholdPercentage) {\n  let {\n    top,\n    left,\n    right,\n    bottom\n  } = _ref;\n\n  if (acceleration === void 0) {\n    acceleration = 10;\n  }\n\n  if (thresholdPercentage === void 0) {\n    thresholdPercentage = defaultThreshold;\n  }\n\n  const {\n    isTop,\n    isBottom,\n    isLeft,\n    isRight\n  } = getScrollPosition(scrollContainer);\n  const direction = {\n    x: 0,\n    y: 0\n  };\n  const speed = {\n    x: 0,\n    y: 0\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.top + threshold.height - top) / threshold.height);\n  } else if (!isBottom && bottom >= scrollContainerRect.bottom - threshold.height) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.bottom - threshold.height - bottom) / threshold.height);\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.right - threshold.width - right) / threshold.width);\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.left + threshold.width - left) / threshold.width);\n  }\n\n  return {\n    direction,\n    speed\n  };\n}\n\nfunction getScrollElementRect(element) {\n  if (element === document.scrollingElement) {\n    const {\n      innerWidth,\n      innerHeight\n    } = window;\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight\n    };\n  }\n\n  const {\n    top,\n    left,\n    right,\n    bottom\n  } = element.getBoundingClientRect();\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight\n  };\n}\n\nfunction getScrollOffsets(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\nfunction getScrollXOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\nfunction getScrollYOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n\nfunction scrollIntoViewIfNeeded(element, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  if (!element) {\n    return;\n  }\n\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (bottom <= 0 || right <= 0 || top >= window.innerHeight || left >= window.innerWidth) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center'\n    });\n  }\n}\n\nconst properties = [['x', ['left', 'right'], getScrollXOffset], ['y', ['top', 'bottom'], getScrollYOffset]];\nclass Rect {\n  constructor(rect, element) {\n    this.rect = void 0;\n    this.width = void 0;\n    this.height = void 0;\n    this.top = void 0;\n    this.bottom = void 0;\n    this.right = void 0;\n    this.left = void 0;\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n    this.rect = { ...rect\n    };\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {\n      enumerable: false\n    });\n  }\n\n}\n\nclass Listeners {\n  constructor(target) {\n    this.target = void 0;\n    this.listeners = [];\n\n    this.removeAll = () => {\n      this.listeners.forEach(listener => {\n        var _this$target;\n\n        return (_this$target = this.target) == null ? void 0 : _this$target.removeEventListener(...listener);\n      });\n    };\n\n    this.target = target;\n  }\n\n  add(eventName, handler, options) {\n    var _this$target2;\n\n    (_this$target2 = this.target) == null ? void 0 : _this$target2.addEventListener(eventName, handler, options);\n    this.listeners.push([eventName, handler, options]);\n  }\n\n}\n\nfunction getEventListenerTarget(target) {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n  const {\n    EventTarget\n  } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target);\n  return target instanceof EventTarget ? target : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target);\n}\n\nfunction hasExceededDistance(delta, measurement) {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n\nvar EventName;\n\n(function (EventName) {\n  EventName[\"Click\"] = \"click\";\n  EventName[\"DragStart\"] = \"dragstart\";\n  EventName[\"Keydown\"] = \"keydown\";\n  EventName[\"ContextMenu\"] = \"contextmenu\";\n  EventName[\"Resize\"] = \"resize\";\n  EventName[\"SelectionChange\"] = \"selectionchange\";\n  EventName[\"VisibilityChange\"] = \"visibilitychange\";\n})(EventName || (EventName = {}));\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction stopPropagation(event) {\n  event.stopPropagation();\n}\n\nvar KeyboardCode;\n\n(function (KeyboardCode) {\n  KeyboardCode[\"Space\"] = \"Space\";\n  KeyboardCode[\"Down\"] = \"ArrowDown\";\n  KeyboardCode[\"Right\"] = \"ArrowRight\";\n  KeyboardCode[\"Left\"] = \"ArrowLeft\";\n  KeyboardCode[\"Up\"] = \"ArrowUp\";\n  KeyboardCode[\"Esc\"] = \"Escape\";\n  KeyboardCode[\"Enter\"] = \"Enter\";\n  KeyboardCode[\"Tab\"] = \"Tab\";\n})(KeyboardCode || (KeyboardCode = {}));\n\nconst defaultKeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab]\n};\nconst defaultKeyboardCoordinateGetter = (event, _ref) => {\n  let {\n    currentCoordinates\n  } = _ref;\n\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x + 25\n      };\n\n    case KeyboardCode.Left:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x - 25\n      };\n\n    case KeyboardCode.Down:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y + 25\n      };\n\n    case KeyboardCode.Up:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y - 25\n      };\n  }\n\n  return undefined;\n};\n\nclass KeyboardSensor {\n  constructor(props) {\n    this.props = void 0;\n    this.autoScrollEnabled = false;\n    this.referenceCoordinates = void 0;\n    this.listeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    const {\n      event: {\n        target\n      }\n    } = props;\n    this.props = props;\n    this.listeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target));\n    this.windowListeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    this.handleStart();\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  handleStart() {\n    const {\n      activeNode,\n      onStart\n    } = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  handleKeyDown(event) {\n    if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(event)) {\n      const {\n        active,\n        context,\n        options\n      } = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth'\n      } = options;\n      const {\n        code\n      } = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {\n        collisionRect\n      } = context.current;\n      const currentCoordinates = collisionRect ? {\n        x: collisionRect.left,\n        y: collisionRect.top\n      } : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(newCoordinates, currentCoordinates);\n        const scrollDelta = {\n          x: 0,\n          y: 0\n        };\n        const {\n          scrollableAncestors\n        } = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {\n            isTop,\n            isRight,\n            isLeft,\n            isBottom,\n            maxScroll,\n            minScroll\n          } = getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n          const clampedCoordinates = {\n            x: Math.min(direction === KeyboardCode.Right ? scrollElementRect.right - scrollElementRect.width / 2 : scrollElementRect.right, Math.max(direction === KeyboardCode.Right ? scrollElementRect.left : scrollElementRect.left + scrollElementRect.width / 2, newCoordinates.x)),\n            y: Math.min(direction === KeyboardCode.Down ? scrollElementRect.bottom - scrollElementRect.height / 2 : scrollElementRect.bottom, Math.max(direction === KeyboardCode.Down ? scrollElementRect.top : scrollElementRect.top + scrollElementRect.height / 2, newCoordinates.y))\n          };\n          const canScrollX = direction === KeyboardCode.Right && !isRight || direction === KeyboardCode.Left && !isLeft;\n          const canScrollY = direction === KeyboardCode.Down && !isBottom || direction === KeyboardCode.Up && !isTop;\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates = scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Right && newScrollCoordinates <= maxScroll.x || direction === KeyboardCode.Left && newScrollCoordinates >= minScroll.x;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x = direction === KeyboardCode.Right ? scrollContainer.scrollLeft - maxScroll.x : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates = scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Down && newScrollCoordinates <= maxScroll.y || direction === KeyboardCode.Up && newScrollCoordinates >= minScroll.y;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y = direction === KeyboardCode.Down ? scrollContainer.scrollTop - maxScroll.y : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(event, (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(newCoordinates, this.referenceCoordinates), scrollDelta));\n      }\n    }\n  }\n\n  handleMove(event, coordinates) {\n    const {\n      onMove\n    } = this.props;\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  handleEnd(event) {\n    const {\n      onEnd\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  handleCancel(event) {\n    const {\n      onCancel\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n}\nKeyboardSensor.activators = [{\n  eventName: 'onKeyDown',\n  handler: (event, _ref, _ref2) => {\n    let {\n      keyboardCodes = defaultKeyboardCodes,\n      onActivation\n    } = _ref;\n    let {\n      active\n    } = _ref2;\n    const {\n      code\n    } = event.nativeEvent;\n\n    if (keyboardCodes.start.includes(code)) {\n      const activator = active.activatorNode.current;\n\n      if (activator && event.target !== activator) {\n        return false;\n      }\n\n      event.preventDefault();\n      onActivation == null ? void 0 : onActivation({\n        event: event.nativeEvent\n      });\n      return true;\n    }\n\n    return false;\n  }\n}];\n\nfunction isDistanceConstraint(constraint) {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(constraint) {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nclass AbstractPointerSensor {\n  constructor(props, events, listenerTarget) {\n    var _getEventCoordinates;\n\n    if (listenerTarget === void 0) {\n      listenerTarget = getEventListenerTarget(props.event.target);\n    }\n\n    this.props = void 0;\n    this.events = void 0;\n    this.autoScrollEnabled = true;\n    this.document = void 0;\n    this.activated = false;\n    this.initialCoordinates = void 0;\n    this.timeoutId = null;\n    this.listeners = void 0;\n    this.documentListeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    this.events = events;\n    const {\n      event\n    } = props;\n    const {\n      target\n    } = event;\n    this.props = props;\n    this.events = events;\n    this.document = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target));\n    this.initialCoordinates = (_getEventCoordinates = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event)) != null ? _getEventCoordinates : defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    const {\n      events,\n      props: {\n        options: {\n          activationConstraint,\n          bypassActivationConstraint\n        }\n      }\n    } = this;\n    this.listeners.add(events.move.name, this.handleMove, {\n      passive: false\n    });\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (bypassActivationConstraint != null && bypassActivationConstraint({\n        event: this.props.event,\n        activeNode: this.props.activeNode,\n        options: this.props.options\n      })) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(this.handleStart, activationConstraint.delay);\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll(); // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  handlePending(constraint, offset) {\n    const {\n      active,\n      onPending\n    } = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  handleStart() {\n    const {\n      initialCoordinates\n    } = this;\n    const {\n      onStart\n    } = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true; // Stop propagation of click events once activation constraints are met\n\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true\n      }); // Remove any text selection from the document\n\n      this.removeTextSelection(); // Prevent further text selection while dragging\n\n      this.documentListeners.add(EventName.SelectionChange, this.removeTextSelection);\n      onStart(initialCoordinates);\n    }\n  }\n\n  handleMove(event) {\n    var _getEventCoordinates2;\n\n    const {\n      activated,\n      initialCoordinates,\n      props\n    } = this;\n    const {\n      onMove,\n      options: {\n        activationConstraint\n      }\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = (_getEventCoordinates2 = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event)) != null ? _getEventCoordinates2 : defaultCoordinates;\n    const delta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(initialCoordinates, coordinates); // Constraint validation\n\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (activationConstraint.tolerance != null && hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  handleEnd() {\n    const {\n      onAbort,\n      onEnd\n    } = this.props;\n    this.detach();\n\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n\n    onEnd();\n  }\n\n  handleCancel() {\n    const {\n      onAbort,\n      onCancel\n    } = this.props;\n    this.detach();\n\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n\n    onCancel();\n  }\n\n  handleKeydown(event) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  removeTextSelection() {\n    var _this$document$getSel;\n\n    (_this$document$getSel = this.document.getSelection()) == null ? void 0 : _this$document$getSel.removeAllRanges();\n  }\n\n}\n\nconst events = {\n  cancel: {\n    name: 'pointercancel'\n  },\n  move: {\n    name: 'pointermove'\n  },\n  end: {\n    name: 'pointerup'\n  }\n};\nclass PointerSensor extends AbstractPointerSensor {\n  constructor(props) {\n    const {\n      event\n    } = props; // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n\n    const listenerTarget = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(event.target);\n    super(props, events, listenerTarget);\n  }\n\n}\nPointerSensor.activators = [{\n  eventName: 'onPointerDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (!event.isPrimary || event.button !== 0) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$1 = {\n  move: {\n    name: 'mousemove'\n  },\n  end: {\n    name: 'mouseup'\n  }\n};\nvar MouseButton;\n\n(function (MouseButton) {\n  MouseButton[MouseButton[\"RightClick\"] = 2] = \"RightClick\";\n})(MouseButton || (MouseButton = {}));\n\nclass MouseSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$1, (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(props.event.target));\n  }\n\n}\nMouseSensor.activators = [{\n  eventName: 'onMouseDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (event.button === MouseButton.RightClick) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$2 = {\n  cancel: {\n    name: 'touchcancel'\n  },\n  move: {\n    name: 'touchmove'\n  },\n  end: {\n    name: 'touchend'\n  }\n};\nclass TouchSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$2);\n  }\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events$2.move.name, noop, {\n      capture: false,\n      passive: false\n    });\n    return function teardown() {\n      window.removeEventListener(events$2.move.name, noop);\n    }; // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n\n    function noop() {}\n  }\n\n}\nTouchSensor.activators = [{\n  eventName: 'onTouchStart',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n    const {\n      touches\n    } = event;\n\n    if (touches.length > 1) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nvar AutoScrollActivator;\n\n(function (AutoScrollActivator) {\n  AutoScrollActivator[AutoScrollActivator[\"Pointer\"] = 0] = \"Pointer\";\n  AutoScrollActivator[AutoScrollActivator[\"DraggableRect\"] = 1] = \"DraggableRect\";\n})(AutoScrollActivator || (AutoScrollActivator = {}));\n\nvar TraversalOrder;\n\n(function (TraversalOrder) {\n  TraversalOrder[TraversalOrder[\"TreeOrder\"] = 0] = \"TreeOrder\";\n  TraversalOrder[TraversalOrder[\"ReversedTreeOrder\"] = 1] = \"ReversedTreeOrder\";\n})(TraversalOrder || (TraversalOrder = {}));\n\nfunction useAutoScroller(_ref) {\n  let {\n    acceleration,\n    activator = AutoScrollActivator.Pointer,\n    canScroll,\n    draggingRect,\n    enabled,\n    interval = 5,\n    order = TraversalOrder.TreeOrder,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    delta,\n    threshold\n  } = _ref;\n  const scrollIntent = useScrollIntent({\n    delta,\n    disabled: !enabled\n  });\n  const [setAutoScrollInterval, clearAutoScrollInterval] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useInterval)();\n  const scrollSpeed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    x: 0,\n    y: 0\n  });\n  const scrollDirection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    x: 0,\n    y: 0\n  });\n  const rect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates ? {\n          top: pointerCoordinates.y,\n          bottom: pointerCoordinates.y,\n          left: pointerCoordinates.x,\n          right: pointerCoordinates.x\n        } : null;\n\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const autoScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => order === TraversalOrder.TreeOrder ? [...scrollableAncestors].reverse() : scrollableAncestors, [order, scrollableAncestors]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!enabled || !scrollableAncestors.length || !rect) {\n      clearAutoScrollInterval();\n      return;\n    }\n\n    for (const scrollContainer of sortedScrollableAncestors) {\n      if ((canScroll == null ? void 0 : canScroll(scrollContainer)) === false) {\n        continue;\n      }\n\n      const index = scrollableAncestors.indexOf(scrollContainer);\n      const scrollContainerRect = scrollableAncestorRects[index];\n\n      if (!scrollContainerRect) {\n        continue;\n      }\n\n      const {\n        direction,\n        speed\n      } = getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, rect, acceleration, threshold);\n\n      for (const axis of ['x', 'y']) {\n        if (!scrollIntent[axis][direction[axis]]) {\n          speed[axis] = 0;\n          direction[axis] = 0;\n        }\n      }\n\n      if (speed.x > 0 || speed.y > 0) {\n        clearAutoScrollInterval();\n        scrollContainerRef.current = scrollContainer;\n        setAutoScrollInterval(autoScroll, interval);\n        scrollSpeed.current = speed;\n        scrollDirection.current = direction;\n        return;\n      }\n    }\n\n    scrollSpeed.current = {\n      x: 0,\n      y: 0\n    };\n    scrollDirection.current = {\n      x: 0,\n      y: 0\n    };\n    clearAutoScrollInterval();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [acceleration, autoScroll, canScroll, clearAutoScrollInterval, enabled, interval, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(rect), // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(scrollIntent), setAutoScrollInterval, scrollableAncestors, sortedScrollableAncestors, scrollableAncestorRects, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(threshold)]);\n}\nconst defaultScrollIntent = {\n  x: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  },\n  y: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  }\n};\n\nfunction useScrollIntent(_ref2) {\n  let {\n    delta,\n    disabled\n  } = _ref2;\n  const previousDelta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(delta);\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousIntent => {\n    if (disabled || !previousDelta || !previousIntent) {\n      // Reset scroll intent tracking when auto-scrolling is disabled\n      return defaultScrollIntent;\n    }\n\n    const direction = {\n      x: Math.sign(delta.x - previousDelta.x),\n      y: Math.sign(delta.y - previousDelta.y)\n    }; // Keep track of the user intent to scroll in each direction for both axis\n\n    return {\n      x: {\n        [Direction.Backward]: previousIntent.x[Direction.Backward] || direction.x === -1,\n        [Direction.Forward]: previousIntent.x[Direction.Forward] || direction.x === 1\n      },\n      y: {\n        [Direction.Backward]: previousIntent.y[Direction.Backward] || direction.y === -1,\n        [Direction.Forward]: previousIntent.y[Direction.Forward] || direction.y === 1\n      }\n    };\n  }, [disabled, delta, previousDelta]);\n}\n\nfunction useCachedNode(draggableNodes, id) {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(cachedNode => {\n    var _ref;\n\n    if (id == null) {\n      return null;\n    } // In some cases, the draggable node can unmount while dragging\n    // This is the case for virtualized lists. In those situations,\n    // we fall back to the last known value for that node.\n\n\n    return (_ref = node != null ? node : cachedNode) != null ? _ref : null;\n  }, [node, id]);\n}\n\nfunction useCombineActivators(sensors, getSyntheticHandler) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => sensors.reduce((accumulator, sensor) => {\n    const {\n      sensor: Sensor\n    } = sensor;\n    const sensorActivators = Sensor.activators.map(activator => ({\n      eventName: activator.eventName,\n      handler: getSyntheticHandler(activator.handler, sensor)\n    }));\n    return [...accumulator, ...sensorActivators];\n  }, []), [sensors, getSyntheticHandler]);\n}\n\nvar MeasuringStrategy;\n\n(function (MeasuringStrategy) {\n  MeasuringStrategy[MeasuringStrategy[\"Always\"] = 0] = \"Always\";\n  MeasuringStrategy[MeasuringStrategy[\"BeforeDragging\"] = 1] = \"BeforeDragging\";\n  MeasuringStrategy[MeasuringStrategy[\"WhileDragging\"] = 2] = \"WhileDragging\";\n})(MeasuringStrategy || (MeasuringStrategy = {}));\n\nvar MeasuringFrequency;\n\n(function (MeasuringFrequency) {\n  MeasuringFrequency[\"Optimized\"] = \"optimized\";\n})(MeasuringFrequency || (MeasuringFrequency = {}));\n\nconst defaultValue = /*#__PURE__*/new Map();\nfunction useDroppableMeasuring(containers, _ref) {\n  let {\n    dragging,\n    dependencies,\n    config\n  } = _ref;\n  const [queue, setQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const {\n    frequency,\n    measure,\n    strategy\n  } = config;\n  const containersRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(containers);\n  const disabled = isDisabled();\n  const disabledRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(disabled);\n  const measureDroppableContainers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (ids) {\n    if (ids === void 0) {\n      ids = [];\n    }\n\n    if (disabledRef.current) {\n      return;\n    }\n\n    setQueue(value => {\n      if (value === null) {\n        return ids;\n      }\n\n      return value.concat(ids.filter(id => !value.includes(id)));\n    });\n  }, [disabledRef]);\n  const timeoutId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const droppableRects = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (disabled && !dragging) {\n      return defaultValue;\n    }\n\n    if (!previousValue || previousValue === defaultValue || containersRef.current !== containers || queue != null) {\n      const map = new Map();\n\n      for (let container of containers) {\n        if (!container) {\n          continue;\n        }\n\n        if (queue && queue.length > 0 && !queue.includes(container.id) && container.rect.current) {\n          // This container does not need to be re-measured\n          map.set(container.id, container.rect.current);\n          continue;\n        }\n\n        const node = container.node.current;\n        const rect = node ? new Rect(measure(node), node) : null;\n        container.rect.current = rect;\n\n        if (rect) {\n          map.set(container.id, rect);\n        }\n      }\n\n      return map;\n    }\n\n    return previousValue;\n  }, [containers, queue, dragging, disabled, measure]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    containersRef.current = containers;\n  }, [containers]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      return;\n    }\n\n    measureDroppableContainers();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [dragging, disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (queue && queue.length > 0) {\n      setQueue(null);\n    }\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [JSON.stringify(queue)]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || typeof frequency !== 'number' || timeoutId.current !== null) {\n      return;\n    }\n\n    timeoutId.current = setTimeout(() => {\n      measureDroppableContainers();\n      timeoutId.current = null;\n    }, frequency);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [frequency, disabled, measureDroppableContainers, ...dependencies]);\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n\n      default:\n        return !dragging;\n    }\n  }\n}\n\nfunction useInitialValue(value, computeFn) {\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (!value) {\n      return null;\n    }\n\n    if (previousValue) {\n      return previousValue;\n    }\n\n    return typeof computeFn === 'function' ? computeFn(value) : value;\n  }, [computeFn, value]);\n}\n\nfunction useInitialRect(node, measure) {\n  return useInitialValue(node, measure);\n}\n\n/**\r\n * Returns a new MutationObserver instance.\r\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useMutationObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleMutations = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)(callback);\n  const mutationObserver = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.MutationObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      MutationObserver\n    } = window;\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return () => mutationObserver == null ? void 0 : mutationObserver.disconnect();\n  }, [mutationObserver]);\n  return mutationObserver;\n}\n\n/**\r\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\r\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useResizeObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleResize = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)(callback);\n  const resizeObserver = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.ResizeObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      ResizeObserver\n    } = window;\n    return new ResizeObserver(handleResize);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return () => resizeObserver == null ? void 0 : resizeObserver.disconnect();\n  }, [resizeObserver]);\n  return resizeObserver;\n}\n\nfunction defaultMeasure(element) {\n  return new Rect(getClientRect(element), element);\n}\n\nfunction useRect(element, measure, fallbackRect) {\n  if (measure === void 0) {\n    measure = defaultMeasure;\n  }\n\n  const [rect, setRect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n\n  function measureRect() {\n    setRect(currentRect => {\n      if (!element) {\n        return null;\n      }\n\n      if (element.isConnected === false) {\n        var _ref;\n\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return (_ref = currentRect != null ? currentRect : fallbackRect) != null ? _ref : null;\n      }\n\n      const newRect = measure(element);\n\n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n\n      return newRect;\n    });\n  }\n\n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {\n          type,\n          target\n        } = record;\n\n        if (type === 'childList' && target instanceof HTMLElement && target.contains(element)) {\n          measureRect();\n          break;\n        }\n      }\n    }\n\n  });\n  const resizeObserver = useResizeObserver({\n    callback: measureRect\n  });\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(element);\n      mutationObserver == null ? void 0 : mutationObserver.observe(document.body, {\n        childList: true,\n        subtree: true\n      });\n    } else {\n      resizeObserver == null ? void 0 : resizeObserver.disconnect();\n      mutationObserver == null ? void 0 : mutationObserver.disconnect();\n    }\n  }, [element]);\n  return rect;\n}\n\nfunction useRectDelta(rect) {\n  const initialRect = useInitialValue(rect);\n  return getRectDelta(rect, initialRect);\n}\n\nconst defaultValue$1 = [];\nfunction useScrollableAncestors(node) {\n  const previousNode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(node);\n  const ancestors = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (!node) {\n      return defaultValue$1;\n    }\n\n    if (previousValue && previousValue !== defaultValue$1 && node && previousNode.current && node.parentNode === previousNode.current.parentNode) {\n      return previousValue;\n    }\n\n    return getScrollableAncestors(node);\n  }, [node]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    previousNode.current = node;\n  }, [node]);\n  return ancestors;\n}\n\nfunction useScrollOffsets(elements) {\n  const [scrollCoordinates, setScrollCoordinates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const prevElements = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(elements); // To-do: Throttle the handleScroll callback\n\n  const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates(scrollCoordinates => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(scrollingElement, getScrollCoordinates(scrollingElement));\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n      const entries = elements.map(element => {\n        const scrollableElement = getScrollableElement(element);\n\n        if (scrollableElement) {\n          scrollableElement.addEventListener('scroll', handleScroll, {\n            passive: true\n          });\n          return [scrollableElement, getScrollCoordinates(scrollableElement)];\n        }\n\n        return null;\n      }).filter(entry => entry != null);\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements) {\n      elements.forEach(element => {\n        const scrollableElement = getScrollableElement(element);\n        scrollableElement == null ? void 0 : scrollableElement.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (elements.length) {\n      return scrollCoordinates ? Array.from(scrollCoordinates.values()).reduce((acc, coordinates) => (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(acc, coordinates), defaultCoordinates) : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n\nfunction useScrollOffsetsDelta(scrollOffsets, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [];\n  }\n\n  const initialScrollOffsets = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    initialScrollOffsets.current = null;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  dependencies);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n  return initialScrollOffsets.current ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(scrollOffsets, initialScrollOffsets.current) : defaultCoordinates;\n}\n\nfunction useSensorSetup(sensors) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM) {\n      return;\n    }\n\n    const teardownFns = sensors.map(_ref => {\n      let {\n        sensor\n      } = _ref;\n      return sensor.setup == null ? void 0 : sensor.setup();\n    });\n    return () => {\n      for (const teardown of teardownFns) {\n        teardown == null ? void 0 : teardown();\n      }\n    };\n  }, // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  sensors.map(_ref2 => {\n    let {\n      sensor\n    } = _ref2;\n    return sensor;\n  }));\n}\n\nfunction useSyntheticListeners(listeners, id) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return listeners.reduce((acc, _ref) => {\n      let {\n        eventName,\n        handler\n      } = _ref;\n\n      acc[eventName] = event => {\n        handler(event, id);\n      };\n\n      return acc;\n    }, {});\n  }, [listeners, id]);\n}\n\nfunction useWindowRect(element) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => element ? getWindowClientRect(element) : null, [element]);\n}\n\nconst defaultValue$2 = [];\nfunction useRects(elements, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(firstElement ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(firstElement) : null);\n  const [rects, setRects] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(defaultValue$2);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue$2;\n      }\n\n      return elements.map(element => isDocumentScrollingElement(element) ? windowRect : new Rect(measure(element), element));\n    });\n  }\n\n  const resizeObserver = useResizeObserver({\n    callback: measureRects\n  });\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n    measureRects();\n    elements.forEach(element => resizeObserver == null ? void 0 : resizeObserver.observe(element));\n  }, [elements]);\n  return rects;\n}\n\nfunction getMeasurableNode(node) {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n\n  const firstChild = node.children[0];\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(firstChild) ? firstChild : node;\n}\n\nfunction useDragOverlayMeasuring(_ref) {\n  let {\n    measure\n  } = _ref;\n  const [rect, setRect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const handleResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(entries => {\n    for (const {\n      target\n    } of entries) {\n      if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(target)) {\n        setRect(rect => {\n          const newRect = measure(target);\n          return rect ? { ...rect,\n            width: newRect.width,\n            height: newRect.height\n          } : newRect;\n        });\n        break;\n      }\n    }\n  }, [measure]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize\n  });\n  const handleNodeChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(element => {\n    const node = getMeasurableNode(element);\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n\n    if (node) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(node);\n    }\n\n    setRect(node ? measure(node) : null);\n  }, [measure, resizeObserver]);\n  const [nodeRef, setRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)(handleNodeChange);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    nodeRef,\n    rect,\n    setRef\n  }), [rect, nodeRef, setRef]);\n}\n\nconst defaultSensors = [{\n  sensor: PointerSensor,\n  options: {}\n}, {\n  sensor: KeyboardSensor,\n  options: {}\n}];\nconst defaultData = {\n  current: {}\n};\nconst defaultMeasuringConfiguration = {\n  draggable: {\n    measure: getTransformAgnosticClientRect\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized\n  },\n  dragOverlay: {\n    measure: getClientRect\n  }\n};\n\nclass DroppableContainersMap extends Map {\n  get(id) {\n    var _super$get;\n\n    return id != null ? (_super$get = super.get(id)) != null ? _super$get : undefined : undefined;\n  }\n\n  toArray() {\n    return Array.from(this.values());\n  }\n\n  getEnabled() {\n    return this.toArray().filter(_ref => {\n      let {\n        disabled\n      } = _ref;\n      return !disabled;\n    });\n  }\n\n  getNodeFor(id) {\n    var _this$get$node$curren, _this$get;\n\n    return (_this$get$node$curren = (_this$get = this.get(id)) == null ? void 0 : _this$get.node.current) != null ? _this$get$node$curren : undefined;\n  }\n\n}\n\nconst defaultPublicContext = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: /*#__PURE__*/new Map(),\n  droppableRects: /*#__PURE__*/new Map(),\n  droppableContainers: /*#__PURE__*/new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null\n    },\n    rect: null,\n    setRef: noop\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false\n};\nconst defaultInternalContext = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: ''\n  },\n  dispatch: noop,\n  draggableNodes: /*#__PURE__*/new Map(),\n  over: null,\n  measureDroppableContainers: noop\n};\nconst InternalContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultInternalContext);\nconst PublicContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultPublicContext);\n\nfunction getInitialState() {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {\n        x: 0,\n        y: 0\n      },\n      nodes: new Map(),\n      translate: {\n        x: 0,\n        y: 0\n      }\n    },\n    droppable: {\n      containers: new DroppableContainersMap()\n    }\n  };\n}\nfunction reducer(state, action) {\n  switch (action.type) {\n    case Action.DragStart:\n      return { ...state,\n        draggable: { ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active\n        }\n      };\n\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return { ...state,\n        draggable: { ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y\n          }\n        }\n      };\n\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return { ...state,\n        draggable: { ...state.draggable,\n          active: null,\n          initialCoordinates: {\n            x: 0,\n            y: 0\n          },\n          translate: {\n            x: 0,\n            y: 0\n          }\n        }\n      };\n\n    case Action.RegisterDroppable:\n      {\n        const {\n          element\n        } = action;\n        const {\n          id\n        } = element;\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, element);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.SetDroppableDisabled:\n      {\n        const {\n          id,\n          key,\n          disabled\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, { ...element,\n          disabled\n        });\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.UnregisterDroppable:\n      {\n        const {\n          id,\n          key\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.delete(id);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    default:\n      {\n        return state;\n      }\n  }\n}\n\nfunction RestoreFocus(_ref) {\n  let {\n    disabled\n  } = _ref;\n  const {\n    active,\n    activatorEvent,\n    draggableNodes\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const previousActivatorEvent = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(activatorEvent);\n  const previousActiveId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(active == null ? void 0 : active.id); // Restore keyboard focus on the activator node\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {\n        activatorNode,\n        node\n      } = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.findFirstFocusableNode)(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [activatorEvent, disabled, draggableNodes, previousActiveId, previousActivatorEvent]);\n  return null;\n}\n\nfunction applyModifiers(modifiers, _ref) {\n  let {\n    transform,\n    ...args\n  } = _ref;\n  return modifiers != null && modifiers.length ? modifiers.reduce((accumulator, modifier) => {\n    return modifier({\n      transform: accumulator,\n      ...args\n    });\n  }, transform) : transform;\n}\n\nfunction useMeasuringConfiguration(config) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    draggable: { ...defaultMeasuringConfiguration.draggable,\n      ...(config == null ? void 0 : config.draggable)\n    },\n    droppable: { ...defaultMeasuringConfiguration.droppable,\n      ...(config == null ? void 0 : config.droppable)\n    },\n    dragOverlay: { ...defaultMeasuringConfiguration.dragOverlay,\n      ...(config == null ? void 0 : config.dragOverlay)\n    }\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [config == null ? void 0 : config.draggable, config == null ? void 0 : config.droppable, config == null ? void 0 : config.dragOverlay]);\n}\n\nfunction useLayoutShiftScrollCompensation(_ref) {\n  let {\n    activeNode,\n    measure,\n    initialRect,\n    config = true\n  } = _ref;\n  const initialized = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const {\n    x,\n    y\n  } = typeof config === 'boolean' ? {\n    x: config,\n    y: config\n  } : config;\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    } // Get the most up to date node ref for the active draggable\n\n\n    const node = activeNode == null ? void 0 : activeNode.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    } // Only perform layout shift scroll compensation once\n\n\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n\nconst ActiveDraggableContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({ ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1\n});\nvar Status;\n\n(function (Status) {\n  Status[Status[\"Uninitialized\"] = 0] = \"Uninitialized\";\n  Status[Status[\"Initializing\"] = 1] = \"Initializing\";\n  Status[Status[\"Initialized\"] = 2] = \"Initialized\";\n})(Status || (Status = {}));\n\nconst DndContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(function DndContext(_ref) {\n  var _sensorContext$curren, _dragOverlay$nodeRef$, _dragOverlay$rect, _over$rect;\n\n  let {\n    id,\n    accessibility,\n    autoScroll = true,\n    children,\n    sensors = defaultSensors,\n    collisionDetection = rectIntersection,\n    measuring,\n    modifiers,\n    ...props\n  } = _ref;\n  const store = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] = useDndMonitorProvider();\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {\n      active: activeId,\n      nodes: draggableNodes,\n      translate\n    },\n    droppable: {\n      containers: droppableContainers\n    }\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    initial: null,\n    translated: null\n  });\n  const active = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    var _node$data;\n\n    return activeId != null ? {\n      id: activeId,\n      // It's possible for the active node to unmount while dragging\n      data: (_node$data = node == null ? void 0 : node.data) != null ? _node$data : defaultData,\n      rect: activeRects\n    } : null;\n  }, [activeId, node]);\n  const activeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [activeSensor, setActiveSensor] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [activatorEvent, setActivatorEvent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const latestProps = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(props, Object.values(props));\n  const draggableDescribedById = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(\"DndDescribedBy\", id);\n  const enabledDroppableContainers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => droppableContainers.getEnabled(), [droppableContainers]);\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled\n  } = useDroppableMeasuring(enabledDroppableContainers, {\n    dragging: isInitialized,\n    dependencies: [translate.x, translate.y],\n    config: measuringConfiguration.droppable\n  });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => activatorEvent ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(activatorEvent) : null, [activatorEvent]);\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(activeNode, measuringConfiguration.draggable.measure);\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure\n  });\n  const activeNodeRect = useRect(activeNode, measuringConfiguration.draggable.measure, initialActiveNodeRect);\n  const containerNodeRect = useRect(activeNode ? activeNode.parentElement : null);\n  const sensorContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null\n  });\n  const overNode = droppableContainers.getNodeFor((_sensorContext$curren = sensorContext.current.over) == null ? void 0 : _sensorContext$curren.id);\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure\n  }); // Use the rect of the drag overlay if it is mounted\n\n  const draggingNode = (_dragOverlay$nodeRef$ = dragOverlay.nodeRef.current) != null ? _dragOverlay$nodeRef$ : activeNode;\n  const draggingNodeRect = isInitialized ? (_dragOverlay$rect = dragOverlay.rect) != null ? _dragOverlay$rect : activeNodeRect : null;\n  const usesDragOverlay = Boolean(dragOverlay.nodeRef.current && dragOverlay.rect); // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect); // Get the window rect of the dragging node\n\n  const windowRect = useWindowRect(draggingNode ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(draggingNode) : null); // Get scrollable ancestors of the dragging node\n\n  const scrollableAncestors = useScrollableAncestors(isInitialized ? overNode != null ? overNode : activeNode : null);\n  const scrollableAncestorRects = useRects(scrollableAncestors); // Apply modifiers\n\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  });\n  const pointerCoordinates = activationCoordinates ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(activationCoordinates, translate) : null;\n  const scrollOffsets = useScrollOffsets(scrollableAncestors); // Represents the scroll delta since dragging was initiated\n\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets); // Represents the scroll delta since the last time the active node rect was measured\n\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [activeNodeRect]);\n  const scrollAdjustedTranslate = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(modifiedTranslate, scrollAdjustment);\n  const collisionRect = draggingNodeRect ? getAdjustedRect(draggingNodeRect, modifiedTranslate) : null;\n  const collisions = active && collisionRect ? collisionDetection({\n    active,\n    collisionRect,\n    droppableRects,\n    droppableContainers: enabledDroppableContainers,\n    pointerCoordinates\n  }) : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null); // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n\n  const appliedTranslate = usesDragOverlay ? modifiedTranslate : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(modifiedTranslate, activeNodeScrollDelta);\n  const transform = adjustScale(appliedTranslate, (_over$rect = over == null ? void 0 : over.rect) != null ? _over$rect : null, activeNodeRect);\n  const activeSensorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const instantiateSensor = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((event, _ref2) => {\n    let {\n      sensor: Sensor,\n      options\n    } = _ref2;\n\n    if (activeRef.current == null) {\n      return;\n    }\n\n    const activeNode = draggableNodes.get(activeRef.current);\n\n    if (!activeNode) {\n      return;\n    }\n\n    const activatorEvent = event.nativeEvent;\n    const sensorInstance = new Sensor({\n      active: activeRef.current,\n      activeNode,\n      event: activatorEvent,\n      options,\n      // Sensors need to be instantiated with refs for arguments that change over time\n      // otherwise they are frozen in time with the stale arguments\n      context: sensorContext,\n\n      onAbort(id) {\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragAbort\n        } = latestProps.current;\n        const event = {\n          id\n        };\n        onDragAbort == null ? void 0 : onDragAbort(event);\n        dispatchMonitorEvent({\n          type: 'onDragAbort',\n          event\n        });\n      },\n\n      onPending(id, constraint, initialCoordinates, offset) {\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragPending\n        } = latestProps.current;\n        const event = {\n          id,\n          constraint,\n          initialCoordinates,\n          offset\n        };\n        onDragPending == null ? void 0 : onDragPending(event);\n        dispatchMonitorEvent({\n          type: 'onDragPending',\n          event\n        });\n      },\n\n      onStart(initialCoordinates) {\n        const id = activeRef.current;\n\n        if (id == null) {\n          return;\n        }\n\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragStart\n        } = latestProps.current;\n        const event = {\n          activatorEvent,\n          active: {\n            id,\n            data: draggableNode.data,\n            rect: activeRects\n          }\n        };\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n          onDragStart == null ? void 0 : onDragStart(event);\n          setStatus(Status.Initializing);\n          dispatch({\n            type: Action.DragStart,\n            initialCoordinates,\n            active: id\n          });\n          dispatchMonitorEvent({\n            type: 'onDragStart',\n            event\n          });\n          setActiveSensor(activeSensorRef.current);\n          setActivatorEvent(activatorEvent);\n        });\n      },\n\n      onMove(coordinates) {\n        dispatch({\n          type: Action.DragMove,\n          coordinates\n        });\n      },\n\n      onEnd: createHandler(Action.DragEnd),\n      onCancel: createHandler(Action.DragCancel)\n    });\n    activeSensorRef.current = sensorInstance;\n\n    function createHandler(type) {\n      return async function handler() {\n        const {\n          active,\n          collisions,\n          over,\n          scrollAdjustedTranslate\n        } = sensorContext.current;\n        let event = null;\n\n        if (active && scrollAdjustedTranslate) {\n          const {\n            cancelDrop\n          } = latestProps.current;\n          event = {\n            activatorEvent,\n            active: active,\n            collisions,\n            delta: scrollAdjustedTranslate,\n            over\n          };\n\n          if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n            const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n            if (shouldCancel) {\n              type = Action.DragCancel;\n            }\n          }\n        }\n\n        activeRef.current = null;\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n          dispatch({\n            type\n          });\n          setStatus(Status.Uninitialized);\n          setOver(null);\n          setActiveSensor(null);\n          setActivatorEvent(null);\n          activeSensorRef.current = null;\n          const eventName = type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n          if (event) {\n            const handler = latestProps.current[eventName];\n            handler == null ? void 0 : handler(event);\n            dispatchMonitorEvent({\n              type: eventName,\n              event\n            });\n          }\n        });\n      };\n    }\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes]);\n  const bindActivatorToSensorInstantiator = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((handler, sensor) => {\n    return (event, active) => {\n      const nativeEvent = event.nativeEvent;\n      const activeDraggableNode = draggableNodes.get(active);\n\n      if ( // Another sensor is already instantiating\n      activeRef.current !== null || // No active draggable\n      !activeDraggableNode || // Event has already been captured\n      nativeEvent.dndKit || nativeEvent.defaultPrevented) {\n        return;\n      }\n\n      const activationContext = {\n        active: activeDraggableNode\n      };\n      const shouldActivate = handler(event, sensor.options, activationContext);\n\n      if (shouldActivate === true) {\n        nativeEvent.dndKit = {\n          capturedBy: sensor.sensor\n        };\n        activeRef.current = active;\n        instantiateSensor(event, sensor);\n      }\n    };\n  }, [draggableNodes, instantiateSensor]);\n  const activators = useCombineActivators(sensors, bindActivatorToSensorInstantiator);\n  useSensorSetup(sensors);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      onDragMove\n    } = latestProps.current;\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      over\n    } = sensorContext.current;\n\n    if (!active || !activatorEvent) {\n      return;\n    }\n\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n      onDragMove == null ? void 0 : onDragMove(event);\n      dispatchMonitorEvent({\n        type: 'onDragMove',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      droppableContainers,\n      scrollAdjustedTranslate\n    } = sensorContext.current;\n\n    if (!active || activeRef.current == null || !activatorEvent || !scrollAdjustedTranslate) {\n      return;\n    }\n\n    const {\n      onDragOver\n    } = latestProps.current;\n    const overContainer = droppableContainers.get(overId);\n    const over = overContainer && overContainer.rect.current ? {\n      id: overContainer.id,\n      rect: overContainer.rect.current,\n      data: overContainer.data,\n      disabled: overContainer.disabled\n    } : null;\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n      setOver(over);\n      onDragOver == null ? void 0 : onDragOver(event);\n      dispatchMonitorEvent({\n        type: 'onDragOver',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [overId]);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate\n    };\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect\n    };\n  }, [active, activeNode, collisions, collisionRect, draggableNodes, draggingNode, draggingNodeRect, droppableRects, droppableContainers, over, scrollableAncestors, scrollAdjustedTranslate]);\n  useAutoScroller({ ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects\n  });\n  const publicContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const context = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect\n    };\n    return context;\n  }, [active, activeNode, activeNodeRect, activatorEvent, collisions, containerNodeRect, dragOverlay, draggableNodes, droppableContainers, droppableRects, over, measureDroppableContainers, scrollableAncestors, scrollableAncestorRects, measuringConfiguration, measuringScheduled, windowRect]);\n  const internalContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const context = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers\n    };\n    return context;\n  }, [activatorEvent, activators, active, activeNodeRect, dispatch, draggableDescribedById, draggableNodes, over, measureDroppableContainers]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(DndMonitorContext.Provider, {\n    value: registerMonitorListener\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(InternalContext.Provider, {\n    value: internalContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(PublicContext.Provider, {\n    value: publicContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ActiveDraggableContext.Provider, {\n    value: transform\n  }, children)), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(RestoreFocus, {\n    disabled: (accessibility == null ? void 0 : accessibility.restoreFocus) === false\n  })), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Accessibility, { ...accessibility,\n    hiddenTextDescribedById: draggableDescribedById\n  }));\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll = (activeSensor == null ? void 0 : activeSensor.autoScrollEnabled) === false;\n    const autoScrollGloballyDisabled = typeof autoScroll === 'object' ? autoScroll.enabled === false : autoScroll === false;\n    const enabled = isInitialized && !activeSensorDisablesAutoscroll && !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return { ...autoScroll,\n        enabled\n      };\n    }\n\n    return {\n      enabled\n    };\n  }\n});\n\nconst NullContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nconst defaultRole = 'button';\nconst ID_PREFIX = 'Draggable';\nfunction useDraggable(_ref) {\n  let {\n    id,\n    data,\n    disabled = false,\n    attributes\n  } = _ref;\n  const key = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0\n  } = attributes != null ? attributes : {};\n  const isDragging = (active == null ? void 0 : active.id) === id;\n  const transform = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(isDragging ? ActiveDraggableContext : NullContext);\n  const [node, setNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)();\n  const [activatorNode, setActivatorNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(data);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    draggableNodes.set(id, {\n      id,\n      key,\n      node,\n      activatorNode,\n      data: dataRef\n    });\n    return () => {\n      const node = draggableNodes.get(id);\n\n      if (node && node.key === key) {\n        draggableNodes.delete(id);\n      }\n    };\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes, id]);\n  const memoizedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    role,\n    tabIndex,\n    'aria-disabled': disabled,\n    'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n    'aria-roledescription': roleDescription,\n    'aria-describedby': ariaDescribedById.draggable\n  }), [disabled, role, tabIndex, isDragging, roleDescription, ariaDescribedById.draggable]);\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform\n  };\n}\n\nfunction useDndContext() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PublicContext);\n}\n\nconst ID_PREFIX$1 = 'Droppable';\nconst defaultResizeObserverConfig = {\n  timeout: 25\n};\nfunction useDroppable(_ref) {\n  let {\n    data,\n    disabled = false,\n    id,\n    resizeObserverConfig\n  } = _ref;\n  const key = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX$1);\n  const {\n    active,\n    dispatch,\n    over,\n    measureDroppableContainers\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const previous = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    disabled\n  });\n  const resizeObserverConnected = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const callbackId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout\n  } = { ...defaultResizeObserverConfig,\n    ...resizeObserverConfig\n  };\n  const ids = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(updateMeasurementsFor != null ? updateMeasurementsFor : id);\n  const handleResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!resizeObserverConnected.current) {\n      // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n      // assuming the element is rendered and displayed.\n      resizeObserverConnected.current = true;\n      return;\n    }\n\n    if (callbackId.current != null) {\n      clearTimeout(callbackId.current);\n    }\n\n    callbackId.current = setTimeout(() => {\n      measureDroppableContainers(Array.isArray(ids.current) ? ids.current : [ids.current]);\n      callbackId.current = null;\n    }, resizeObserverTimeout);\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [resizeObserverTimeout]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active\n  });\n  const handleNodeChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((newElement, previousElement) => {\n    if (!resizeObserver) {\n      return;\n    }\n\n    if (previousElement) {\n      resizeObserver.unobserve(previousElement);\n      resizeObserverConnected.current = false;\n    }\n\n    if (newElement) {\n      resizeObserver.observe(newElement);\n    }\n  }, [resizeObserver]);\n  const [nodeRef, setNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)(handleNodeChange);\n  const dataRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(data);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    dispatch({\n      type: Action.RegisterDroppable,\n      element: {\n        id,\n        key,\n        disabled,\n        node: nodeRef,\n        rect,\n        data: dataRef\n      }\n    });\n    return () => dispatch({\n      type: Action.UnregisterDroppable,\n      key,\n      id\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [id]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled\n      });\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n  return {\n    active,\n    rect,\n    isOver: (over == null ? void 0 : over.id) === id,\n    node: nodeRef,\n    over,\n    setNodeRef\n  };\n}\n\nfunction AnimationManager(_ref) {\n  let {\n    animation,\n    children\n  } = _ref;\n  const [clonedChildren, setClonedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [element, setElement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const previousChildren = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren == null ? void 0 : clonedChildren.key;\n    const id = clonedChildren == null ? void 0 : clonedChildren.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, children, clonedChildren ? (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(clonedChildren, {\n    ref: setElement\n  }) : null);\n}\n\nconst defaultTransform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1\n};\nfunction NullifiedContextProvider(_ref) {\n  let {\n    children\n  } = _ref;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(InternalContext.Provider, {\n    value: defaultInternalContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ActiveDraggableContext.Provider, {\n    value: defaultTransform\n  }, children));\n}\n\nconst baseStyles = {\n  position: 'fixed',\n  touchAction: 'none'\n};\n\nconst defaultTransition = activatorEvent => {\n  const isKeyboardActivator = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(activatorEvent);\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nconst PositionedOverlay = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((_ref, ref) => {\n  let {\n    as,\n    activatorEvent,\n    adjustScale,\n    children,\n    className,\n    rect,\n    style,\n    transform,\n    transition = defaultTransition\n  } = _ref;\n\n  if (!rect) {\n    return null;\n  }\n\n  const scaleAdjustedTransform = adjustScale ? transform : { ...transform,\n    scaleX: 1,\n    scaleY: 1\n  };\n  const styles = { ...baseStyles,\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    left: rect.left,\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(scaleAdjustedTransform),\n    transformOrigin: adjustScale && activatorEvent ? getRelativeTransformOrigin(activatorEvent, rect) : undefined,\n    transition: typeof transition === 'function' ? transition(activatorEvent) : transition,\n    ...style\n  };\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(as, {\n    className,\n    style: styles,\n    ref\n  }, children);\n});\n\nconst defaultDropAnimationSideEffects = options => _ref => {\n  let {\n    active,\n    dragOverlay\n  } = _ref;\n  const originalStyles = {};\n  const {\n    styles,\n    className\n  } = options;\n\n  if (styles != null && styles.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles != null && styles.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className != null && className.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className != null && className.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className != null && className.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver = _ref2 => {\n  let {\n    transform: {\n      initial,\n      final\n    }\n  } = _ref2;\n  return [{\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(initial)\n  }, {\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(final)\n  }];\n};\n\nconst defaultDropAnimationConfiguration = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: /*#__PURE__*/defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0'\n      }\n    }\n  })\n};\nfunction useDropAnimation(_ref3) {\n  let {\n    config,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  } = _ref3;\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n\n    const {\n      transform\n    } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation = typeof config === 'function' ? config : createDefaultDropAnimation(config);\n    scrollIntoViewIfNeeded(activeNode, measuringConfiguration.draggable.measure);\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode)\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode)\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(options) {\n  const {\n    duration,\n    easing,\n    sideEffects,\n    keyframes\n  } = { ...defaultDropAnimationConfiguration,\n    ...options\n  };\n  return _ref4 => {\n    let {\n      active,\n      dragOverlay,\n      transform,\n      ...rest\n    } = _ref4;\n\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top\n    };\n    const scale = {\n      scaleX: transform.scaleX !== 1 ? active.rect.width * transform.scaleX / dragOverlay.rect.width : 1,\n      scaleY: transform.scaleY !== 1 ? active.rect.height * transform.scaleY / dragOverlay.rect.height : 1\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale\n    };\n    const animationKeyframes = keyframes({ ...rest,\n      active,\n      dragOverlay,\n      transform: {\n        initial: transform,\n        final: finalTransform\n      }\n    });\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects == null ? void 0 : sideEffects({\n      active,\n      dragOverlay,\n      ...rest\n    });\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards'\n    });\n    return new Promise(resolve => {\n      animation.onfinish = () => {\n        cleanup == null ? void 0 : cleanup();\n        resolve();\n      };\n    });\n  };\n}\n\nlet key = 0;\nfunction useKey(id) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n\nconst DragOverlay = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().memo(_ref => {\n  let {\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999\n  } = _ref;\n  const {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggableNodes,\n    droppableContainers,\n    dragOverlay,\n    over,\n    measuringConfiguration,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  } = useDndContext();\n  const transform = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ActiveDraggableContext);\n  const key = useKey(active == null ? void 0 : active.id);\n  const modifiedTransform = applyModifiers(modifiers, {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect: dragOverlay.rect,\n    over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    transform,\n    windowRect\n  });\n  const initialRect = useInitialValue(activeNodeRect);\n  const dropAnimation = useDropAnimation({\n    config: dropAnimationConfig,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  }); // We need to wait for the active node to be measured before connecting the drag overlay ref\n  // otherwise collisions can be computed against a mispositioned drag overlay\n\n  const ref = initialRect ? dragOverlay.setRef : undefined;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NullifiedContextProvider, null, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(AnimationManager, {\n    animation: dropAnimation\n  }, active && key ? react__WEBPACK_IMPORTED_MODULE_0___default().createElement(PositionedOverlay, {\n    key: key,\n    id: active.id,\n    ref: ref,\n    as: wrapperElement,\n    activatorEvent: activatorEvent,\n    adjustScale: adjustScale,\n    className: className,\n    transition: transition,\n    rect: initialRect,\n    style: {\n      zIndex,\n      ...style\n    },\n    transform: modifiedTransform\n  }, children) : null));\n});\n\n\n//# sourceMappingURL=core.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/core/dist/core.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@dnd-kit/sortable/dist/sortable.esm.js":
/*!*************************************************************!*\
  !*** ./node_modules/@dnd-kit/sortable/dist/sortable.esm.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SortableContext: () => (/* binding */ SortableContext),\n/* harmony export */   arrayMove: () => (/* binding */ arrayMove),\n/* harmony export */   arraySwap: () => (/* binding */ arraySwap),\n/* harmony export */   defaultAnimateLayoutChanges: () => (/* binding */ defaultAnimateLayoutChanges),\n/* harmony export */   defaultNewIndexGetter: () => (/* binding */ defaultNewIndexGetter),\n/* harmony export */   hasSortableData: () => (/* binding */ hasSortableData),\n/* harmony export */   horizontalListSortingStrategy: () => (/* binding */ horizontalListSortingStrategy),\n/* harmony export */   rectSortingStrategy: () => (/* binding */ rectSortingStrategy),\n/* harmony export */   rectSwappingStrategy: () => (/* binding */ rectSwappingStrategy),\n/* harmony export */   sortableKeyboardCoordinates: () => (/* binding */ sortableKeyboardCoordinates),\n/* harmony export */   useSortable: () => (/* binding */ useSortable),\n/* harmony export */   verticalListSortingStrategy: () => (/* binding */ verticalListSortingStrategy)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @dnd-kit/core */ \"(ssr)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/utilities */ \"(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n\n\n\n\n/**\r\n * Move an array item to a different position. Returns a new array with the item moved to the new position.\r\n */\nfunction arrayMove(array, from, to) {\n  const newArray = array.slice();\n  newArray.splice(to < 0 ? newArray.length + to : to, 0, newArray.splice(from, 1)[0]);\n  return newArray;\n}\n\n/**\r\n * Swap an array item to a different position. Returns a new array with the item swapped to the new position.\r\n */\nfunction arraySwap(array, from, to) {\n  const newArray = array.slice();\n  newArray[from] = array[to];\n  newArray[to] = array[from];\n  return newArray;\n}\n\nfunction getSortedRects(items, rects) {\n  return items.reduce((accumulator, id, index) => {\n    const rect = rects.get(id);\n\n    if (rect) {\n      accumulator[index] = rect;\n    }\n\n    return accumulator;\n  }, Array(items.length));\n}\n\nfunction isValidIndex(index) {\n  return index !== null && index >= 0;\n}\n\nfunction itemsEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction normalizeDisabled(disabled) {\n  if (typeof disabled === 'boolean') {\n    return {\n      draggable: disabled,\n      droppable: disabled\n    };\n  }\n\n  return disabled;\n}\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1\n};\nconst horizontalListSortingStrategy = _ref => {\n  var _rects$activeIndex;\n\n  let {\n    rects,\n    activeNodeRect: fallbackActiveRect,\n    activeIndex,\n    overIndex,\n    index\n  } = _ref;\n  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index === activeIndex) {\n    const newIndexRect = rects[overIndex];\n\n    if (!newIndexRect) {\n      return null;\n    }\n\n    return {\n      x: activeIndex < overIndex ? newIndexRect.left + newIndexRect.width - (activeNodeRect.left + activeNodeRect.width) : newIndexRect.left - activeNodeRect.left,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: -activeNodeRect.width - itemGap,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: activeNodeRect.width + itemGap,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale\n  };\n};\n\nfunction getItemGap(rects, index, activeIndex) {\n  const currentRect = rects[index];\n  const previousRect = rects[index - 1];\n  const nextRect = rects[index + 1];\n\n  if (!currentRect || !previousRect && !nextRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect ? currentRect.left - (previousRect.left + previousRect.width) : nextRect.left - (currentRect.left + currentRect.width);\n  }\n\n  return nextRect ? nextRect.left - (currentRect.left + currentRect.width) : currentRect.left - (previousRect.left + previousRect.width);\n}\n\nconst rectSortingStrategy = _ref => {\n  let {\n    rects,\n    activeIndex,\n    overIndex,\n    index\n  } = _ref;\n  const newRects = arrayMove(rects, overIndex, activeIndex);\n  const oldRect = rects[index];\n  const newRect = newRects[index];\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height\n  };\n};\n\nconst rectSwappingStrategy = _ref => {\n  let {\n    activeIndex,\n    index,\n    rects,\n    overIndex\n  } = _ref;\n  let oldRect;\n  let newRect;\n\n  if (index === activeIndex) {\n    oldRect = rects[index];\n    newRect = rects[overIndex];\n  }\n\n  if (index === overIndex) {\n    oldRect = rects[index];\n    newRect = rects[activeIndex];\n  }\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height\n  };\n};\n\n// To-do: We should be calculating scale transformation\nconst defaultScale$1 = {\n  scaleX: 1,\n  scaleY: 1\n};\nconst verticalListSortingStrategy = _ref => {\n  var _rects$activeIndex;\n\n  let {\n    activeIndex,\n    activeNodeRect: fallbackActiveRect,\n    index,\n    rects,\n    overIndex\n  } = _ref;\n  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  if (index === activeIndex) {\n    const overIndexRect = rects[overIndex];\n\n    if (!overIndexRect) {\n      return null;\n    }\n\n    return {\n      x: 0,\n      y: activeIndex < overIndex ? overIndexRect.top + overIndexRect.height - (activeNodeRect.top + activeNodeRect.height) : overIndexRect.top - activeNodeRect.top,\n      ...defaultScale$1\n    };\n  }\n\n  const itemGap = getItemGap$1(rects, index, activeIndex);\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: 0,\n      y: -activeNodeRect.height - itemGap,\n      ...defaultScale$1\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: 0,\n      y: activeNodeRect.height + itemGap,\n      ...defaultScale$1\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale$1\n  };\n};\n\nfunction getItemGap$1(clientRects, index, activeIndex) {\n  const currentRect = clientRects[index];\n  const previousRect = clientRects[index - 1];\n  const nextRect = clientRects[index + 1];\n\n  if (!currentRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect ? currentRect.top - (previousRect.top + previousRect.height) : nextRect ? nextRect.top - (currentRect.top + currentRect.height) : 0;\n  }\n\n  return nextRect ? nextRect.top - (currentRect.top + currentRect.height) : previousRect ? currentRect.top - (previousRect.top + previousRect.height) : 0;\n}\n\nconst ID_PREFIX = 'Sortable';\nconst Context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext({\n  activeIndex: -1,\n  containerId: ID_PREFIX,\n  disableTransforms: false,\n  items: [],\n  overIndex: -1,\n  useDragOverlay: false,\n  sortedRects: [],\n  strategy: rectSortingStrategy,\n  disabled: {\n    draggable: false,\n    droppable: false\n  }\n});\nfunction SortableContext(_ref) {\n  let {\n    children,\n    id,\n    items: userDefinedItems,\n    strategy = rectSortingStrategy,\n    disabled: disabledProp = false\n  } = _ref;\n  const {\n    active,\n    dragOverlay,\n    droppableRects,\n    over,\n    measureDroppableContainers\n  } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDndContext)();\n  const containerId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX, id);\n  const useDragOverlay = Boolean(dragOverlay.rect !== null);\n  const items = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => userDefinedItems.map(item => typeof item === 'object' && 'id' in item ? item.id : item), [userDefinedItems]);\n  const isDragging = active != null;\n  const activeIndex = active ? items.indexOf(active.id) : -1;\n  const overIndex = over ? items.indexOf(over.id) : -1;\n  const previousItemsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(items);\n  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);\n  const disableTransforms = overIndex !== -1 && activeIndex === -1 || itemsHaveChanged;\n  const disabled = normalizeDisabled(disabledProp);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (itemsHaveChanged && isDragging) {\n      measureDroppableContainers(items);\n    }\n  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    previousItemsRef.current = items;\n  }, [items]);\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    activeIndex,\n    containerId,\n    disabled,\n    disableTransforms,\n    items,\n    overIndex,\n    useDragOverlay,\n    sortedRects: getSortedRects(items, droppableRects),\n    strategy\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [activeIndex, containerId, disabled.draggable, disabled.droppable, disableTransforms, items, overIndex, droppableRects, useDragOverlay, strategy]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\n\nconst defaultNewIndexGetter = _ref => {\n  let {\n    id,\n    items,\n    activeIndex,\n    overIndex\n  } = _ref;\n  return arrayMove(items, activeIndex, overIndex).indexOf(id);\n};\nconst defaultAnimateLayoutChanges = _ref2 => {\n  let {\n    containerId,\n    isSorting,\n    wasDragging,\n    index,\n    items,\n    newIndex,\n    previousItems,\n    previousContainerId,\n    transition\n  } = _ref2;\n\n  if (!transition || !wasDragging) {\n    return false;\n  }\n\n  if (previousItems !== items && index === newIndex) {\n    return false;\n  }\n\n  if (isSorting) {\n    return true;\n  }\n\n  return newIndex !== index && containerId === previousContainerId;\n};\nconst defaultTransition = {\n  duration: 200,\n  easing: 'ease'\n};\nconst transitionProperty = 'transform';\nconst disabledTransition = /*#__PURE__*/_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transition.toString({\n  property: transitionProperty,\n  duration: 0,\n  easing: 'linear'\n});\nconst defaultAttributes = {\n  roleDescription: 'sortable'\n};\n\n/*\r\n * When the index of an item changes while sorting,\r\n * we need to temporarily disable the transforms\r\n */\n\nfunction useDerivedTransform(_ref) {\n  let {\n    disabled,\n    index,\n    node,\n    rect\n  } = _ref;\n  const [derivedTransform, setDerivedtransform] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const previousIndex = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(index);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (!disabled && index !== previousIndex.current && node.current) {\n      const initial = rect.current;\n\n      if (initial) {\n        const current = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.getClientRect)(node.current, {\n          ignoreTransform: true\n        });\n        const delta = {\n          x: initial.left - current.left,\n          y: initial.top - current.top,\n          scaleX: initial.width / current.width,\n          scaleY: initial.height / current.height\n        };\n\n        if (delta.x || delta.y) {\n          setDerivedtransform(delta);\n        }\n      }\n    }\n\n    if (index !== previousIndex.current) {\n      previousIndex.current = index;\n    }\n  }, [disabled, index, node, rect]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (derivedTransform) {\n      setDerivedtransform(null);\n    }\n  }, [derivedTransform]);\n  return derivedTransform;\n}\n\nfunction useSortable(_ref) {\n  let {\n    animateLayoutChanges = defaultAnimateLayoutChanges,\n    attributes: userDefinedAttributes,\n    disabled: localDisabled,\n    data: customData,\n    getNewIndex = defaultNewIndexGetter,\n    id,\n    strategy: localStrategy,\n    resizeObserverConfig,\n    transition = defaultTransition\n  } = _ref;\n  const {\n    items,\n    containerId,\n    activeIndex,\n    disabled: globalDisabled,\n    disableTransforms,\n    sortedRects,\n    overIndex,\n    useDragOverlay,\n    strategy: globalStrategy\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context);\n  const disabled = normalizeLocalDisabled(localDisabled, globalDisabled);\n  const index = items.indexOf(id);\n  const data = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    sortable: {\n      containerId,\n      index,\n      items\n    },\n    ...customData\n  }), [containerId, customData, index, items]);\n  const itemsAfterCurrentSortable = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => items.slice(items.indexOf(id)), [items, id]);\n  const {\n    rect,\n    node,\n    isOver,\n    setNodeRef: setDroppableNodeRef\n  } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDroppable)({\n    id,\n    data,\n    disabled: disabled.droppable,\n    resizeObserverConfig: {\n      updateMeasurementsFor: itemsAfterCurrentSortable,\n      ...resizeObserverConfig\n    }\n  });\n  const {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes,\n    setNodeRef: setDraggableNodeRef,\n    listeners,\n    isDragging,\n    over,\n    setActivatorNodeRef,\n    transform\n  } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDraggable)({\n    id,\n    data,\n    attributes: { ...defaultAttributes,\n      ...userDefinedAttributes\n    },\n    disabled: disabled.draggable\n  });\n  const setNodeRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useCombinedRefs)(setDroppableNodeRef, setDraggableNodeRef);\n  const isSorting = Boolean(active);\n  const displaceItem = isSorting && !disableTransforms && isValidIndex(activeIndex) && isValidIndex(overIndex);\n  const shouldDisplaceDragSource = !useDragOverlay && isDragging;\n  const dragSourceDisplacement = shouldDisplaceDragSource && displaceItem ? transform : null;\n  const strategy = localStrategy != null ? localStrategy : globalStrategy;\n  const finalTransform = displaceItem ? dragSourceDisplacement != null ? dragSourceDisplacement : strategy({\n    rects: sortedRects,\n    activeNodeRect,\n    activeIndex,\n    overIndex,\n    index\n  }) : null;\n  const newIndex = isValidIndex(activeIndex) && isValidIndex(overIndex) ? getNewIndex({\n    id,\n    items,\n    activeIndex,\n    overIndex\n  }) : index;\n  const activeId = active == null ? void 0 : active.id;\n  const previous = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    activeId,\n    items,\n    newIndex,\n    containerId\n  });\n  const itemsHaveChanged = items !== previous.current.items;\n  const shouldAnimateLayoutChanges = animateLayoutChanges({\n    active,\n    containerId,\n    isDragging,\n    isSorting,\n    id,\n    index,\n    items,\n    newIndex: previous.current.newIndex,\n    previousItems: previous.current.items,\n    previousContainerId: previous.current.containerId,\n    transition,\n    wasDragging: previous.current.activeId != null\n  });\n  const derivedTransform = useDerivedTransform({\n    disabled: !shouldAnimateLayoutChanges,\n    index,\n    node,\n    rect\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (isSorting && previous.current.newIndex !== newIndex) {\n      previous.current.newIndex = newIndex;\n    }\n\n    if (containerId !== previous.current.containerId) {\n      previous.current.containerId = containerId;\n    }\n\n    if (items !== previous.current.items) {\n      previous.current.items = items;\n    }\n  }, [isSorting, newIndex, containerId, items]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (activeId === previous.current.activeId) {\n      return;\n    }\n\n    if (activeId != null && previous.current.activeId == null) {\n      previous.current.activeId = activeId;\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      previous.current.activeId = activeId;\n    }, 50);\n    return () => clearTimeout(timeoutId);\n  }, [activeId]);\n  return {\n    active,\n    activeIndex,\n    attributes,\n    data,\n    rect,\n    index,\n    newIndex,\n    items,\n    isOver,\n    isSorting,\n    isDragging,\n    listeners,\n    node,\n    overIndex,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    setDroppableNodeRef,\n    setDraggableNodeRef,\n    transform: derivedTransform != null ? derivedTransform : finalTransform,\n    transition: getTransition()\n  };\n\n  function getTransition() {\n    if ( // Temporarily disable transitions for a single frame to set up derived transforms\n    derivedTransform || // Or to prevent items jumping to back to their \"new\" position when items change\n    itemsHaveChanged && previous.current.newIndex === index) {\n      return disabledTransition;\n    }\n\n    if (shouldDisplaceDragSource && !(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(activatorEvent) || !transition) {\n      return undefined;\n    }\n\n    if (isSorting || shouldAnimateLayoutChanges) {\n      return _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transition.toString({ ...transition,\n        property: transitionProperty\n      });\n    }\n\n    return undefined;\n  }\n}\n\nfunction normalizeLocalDisabled(localDisabled, globalDisabled) {\n  var _localDisabled$dragga, _localDisabled$droppa;\n\n  if (typeof localDisabled === 'boolean') {\n    return {\n      draggable: localDisabled,\n      // Backwards compatibility\n      droppable: false\n    };\n  }\n\n  return {\n    draggable: (_localDisabled$dragga = localDisabled == null ? void 0 : localDisabled.draggable) != null ? _localDisabled$dragga : globalDisabled.draggable,\n    droppable: (_localDisabled$droppa = localDisabled == null ? void 0 : localDisabled.droppable) != null ? _localDisabled$droppa : globalDisabled.droppable\n  };\n}\n\nfunction hasSortableData(entry) {\n  if (!entry) {\n    return false;\n  }\n\n  const data = entry.data.current;\n\n  if (data && 'sortable' in data && typeof data.sortable === 'object' && 'containerId' in data.sortable && 'items' in data.sortable && 'index' in data.sortable) {\n    return true;\n  }\n\n  return false;\n}\n\nconst directions = [_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Down, _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Right, _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Up, _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Left];\nconst sortableKeyboardCoordinates = (event, _ref) => {\n  let {\n    context: {\n      active,\n      collisionRect,\n      droppableRects,\n      droppableContainers,\n      over,\n      scrollableAncestors\n    }\n  } = _ref;\n\n  if (directions.includes(event.code)) {\n    event.preventDefault();\n\n    if (!active || !collisionRect) {\n      return;\n    }\n\n    const filteredContainers = [];\n    droppableContainers.getEnabled().forEach(entry => {\n      if (!entry || entry != null && entry.disabled) {\n        return;\n      }\n\n      const rect = droppableRects.get(entry.id);\n\n      if (!rect) {\n        return;\n      }\n\n      switch (event.code) {\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Left:\n          if (collisionRect.left > rect.left) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Right:\n          if (collisionRect.left < rect.left) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n      }\n    });\n    const collisions = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.closestCorners)({\n      active,\n      collisionRect: collisionRect,\n      droppableRects,\n      droppableContainers: filteredContainers,\n      pointerCoordinates: null\n    });\n    let closestId = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.getFirstCollision)(collisions, 'id');\n\n    if (closestId === (over == null ? void 0 : over.id) && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n\n    if (closestId != null) {\n      const activeDroppable = droppableContainers.get(active.id);\n      const newDroppable = droppableContainers.get(closestId);\n      const newRect = newDroppable ? droppableRects.get(newDroppable.id) : null;\n      const newNode = newDroppable == null ? void 0 : newDroppable.node.current;\n\n      if (newNode && newRect && activeDroppable && newDroppable) {\n        const newScrollAncestors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.getScrollableAncestors)(newNode);\n        const hasDifferentScrollAncestors = newScrollAncestors.some((element, index) => scrollableAncestors[index] !== element);\n        const hasSameContainer = isSameContainer(activeDroppable, newDroppable);\n        const isAfterActive = isAfter(activeDroppable, newDroppable);\n        const offset = hasDifferentScrollAncestors || !hasSameContainer ? {\n          x: 0,\n          y: 0\n        } : {\n          x: isAfterActive ? collisionRect.width - newRect.width : 0,\n          y: isAfterActive ? collisionRect.height - newRect.height : 0\n        };\n        const rectCoordinates = {\n          x: newRect.left,\n          y: newRect.top\n        };\n        const newCoordinates = offset.x && offset.y ? rectCoordinates : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(rectCoordinates, offset);\n        return newCoordinates;\n      }\n    }\n  }\n\n  return undefined;\n};\n\nfunction isSameContainer(a, b) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.containerId === b.data.current.sortable.containerId;\n}\n\nfunction isAfter(a, b) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  if (!isSameContainer(a, b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.index < b.data.current.sortable.index;\n}\n\n\n//# sourceMappingURL=sortable.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/sortable/dist/sortable.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js":
/*!***************************************************************!*\
  !*** ./node_modules/@dnd-kit/utilities/dist/utilities.esm.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSS: () => (/* binding */ CSS),\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   canUseDOM: () => (/* binding */ canUseDOM),\n/* harmony export */   findFirstFocusableNode: () => (/* binding */ findFirstFocusableNode),\n/* harmony export */   getEventCoordinates: () => (/* binding */ getEventCoordinates),\n/* harmony export */   getOwnerDocument: () => (/* binding */ getOwnerDocument),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   hasViewportRelativeCoordinates: () => (/* binding */ hasViewportRelativeCoordinates),\n/* harmony export */   isDocument: () => (/* binding */ isDocument),\n/* harmony export */   isHTMLElement: () => (/* binding */ isHTMLElement),\n/* harmony export */   isKeyboardEvent: () => (/* binding */ isKeyboardEvent),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isSVGElement: () => (/* binding */ isSVGElement),\n/* harmony export */   isTouchEvent: () => (/* binding */ isTouchEvent),\n/* harmony export */   isWindow: () => (/* binding */ isWindow),\n/* harmony export */   subtract: () => (/* binding */ subtract),\n/* harmony export */   useCombinedRefs: () => (/* binding */ useCombinedRefs),\n/* harmony export */   useEvent: () => (/* binding */ useEvent),\n/* harmony export */   useInterval: () => (/* binding */ useInterval),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useLatestValue: () => (/* binding */ useLatestValue),\n/* harmony export */   useLazyMemo: () => (/* binding */ useLazyMemo),\n/* harmony export */   useNodeRef: () => (/* binding */ useNodeRef),\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious),\n/* harmony export */   useUniqueId: () => (/* binding */ useUniqueId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nfunction useCombinedRefs() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => node => {\n    refs.forEach(ref => ref(node));\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  refs);\n}\n\n// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nconst canUseDOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\nfunction isWindow(element) {\n  const elementString = Object.prototype.toString.call(element);\n  return elementString === '[object Window]' || // In Electron context the Window object serializes to [object global]\n  elementString === '[object global]';\n}\n\nfunction isNode(node) {\n  return 'nodeType' in node;\n}\n\nfunction getWindow(target) {\n  var _target$ownerDocument, _target$ownerDocument2;\n\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return (_target$ownerDocument = (_target$ownerDocument2 = target.ownerDocument) == null ? void 0 : _target$ownerDocument2.defaultView) != null ? _target$ownerDocument : window;\n}\n\nfunction isDocument(node) {\n  const {\n    Document\n  } = getWindow(node);\n  return node instanceof Document;\n}\n\nfunction isHTMLElement(node) {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n\nfunction isSVGElement(node) {\n  return node instanceof getWindow(node).SVGElement;\n}\n\nfunction getOwnerDocument(target) {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n\n/**\r\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\r\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\r\n */\n\nconst useIsomorphicLayoutEffect = canUseDOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\nfunction useEvent(handler) {\n  const handlerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(handler);\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return handlerRef.current == null ? void 0 : handlerRef.current(...args);\n  }, []);\n}\n\nfunction useInterval() {\n  const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const set = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((listener, duration) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n  const clear = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n  return [set, clear];\n}\n\nfunction useLatestValue(value, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [value];\n  }\n\n  const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n  return valueRef;\n}\n\nfunction useLazyMemo(callback, dependencies) {\n  const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const newValue = callback(valueRef.current);\n    valueRef.current = newValue;\n    return newValue;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...dependencies]);\n}\n\nfunction useNodeRef(onChange) {\n  const onChangeHandler = useEvent(onChange);\n  const node = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const setNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(element => {\n    if (element !== node.current) {\n      onChangeHandler == null ? void 0 : onChangeHandler(element, node.current);\n    }\n\n    node.current = element;\n  }, //eslint-disable-next-line\n  []);\n  return [node, setNodeRef];\n}\n\nfunction usePrevious(value) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n}\n\nlet ids = {};\nfunction useUniqueId(prefix, value) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n    return prefix + \"-\" + id;\n  }, [prefix, value]);\n}\n\nfunction createAdjustmentFn(modifier) {\n  return function (object) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((accumulator, adjustment) => {\n      const entries = Object.entries(adjustment);\n\n      for (const [key, valueAdjustment] of entries) {\n        const value = accumulator[key];\n\n        if (value != null) {\n          accumulator[key] = value + modifier * valueAdjustment;\n        }\n      }\n\n      return accumulator;\n    }, { ...object\n    });\n  };\n}\n\nconst add = /*#__PURE__*/createAdjustmentFn(1);\nconst subtract = /*#__PURE__*/createAdjustmentFn(-1);\n\nfunction hasViewportRelativeCoordinates(event) {\n  return 'clientX' in event && 'clientY' in event;\n}\n\nfunction isKeyboardEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    KeyboardEvent\n  } = getWindow(event.target);\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n\nfunction isTouchEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    TouchEvent\n  } = getWindow(event.target);\n  return TouchEvent && event instanceof TouchEvent;\n}\n\n/**\r\n * Returns the normalized x and y coordinates for mouse and touch events.\r\n */\n\nfunction getEventCoordinates(event) {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.touches[0];\n      return {\n        x,\n        y\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.changedTouches[0];\n      return {\n        x,\n        y\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n\n  return null;\n}\n\nconst CSS = /*#__PURE__*/Object.freeze({\n  Translate: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        x,\n        y\n      } = transform;\n      return \"translate3d(\" + (x ? Math.round(x) : 0) + \"px, \" + (y ? Math.round(y) : 0) + \"px, 0)\";\n    }\n\n  },\n  Scale: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        scaleX,\n        scaleY\n      } = transform;\n      return \"scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\";\n    }\n\n  },\n  Transform: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      return [CSS.Translate.toString(transform), CSS.Scale.toString(transform)].join(' ');\n    }\n\n  },\n  Transition: {\n    toString(_ref) {\n      let {\n        property,\n        duration,\n        easing\n      } = _ref;\n      return property + \" \" + duration + \"ms \" + easing;\n    }\n\n  }\n});\n\nconst SELECTOR = 'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\nfunction findFirstFocusableNode(element) {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n\n\n//# sourceMappingURL=utilities.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGRuZC1raXQvdXRpbGl0aWVzL2Rpc3QvdXRpbGl0aWVzLmVzbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBaUY7O0FBRWpGO0FBQ0Esc0VBQXNFLGFBQWE7QUFDbkY7QUFDQTs7QUFFQSxTQUFTLDhDQUFPO0FBQ2hCO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLG9CQUFvQixVQUFVO0FBQzlCOztBQUVBLDhDQUE4QyxrREFBZSxHQUFHLDRDQUFTOztBQUV6RTtBQUNBLHFCQUFxQiw2Q0FBTTtBQUMzQjtBQUNBO0FBQ0EsR0FBRztBQUNILFNBQVMsa0RBQVc7QUFDcEIsd0VBQXdFLGFBQWE7QUFDckY7QUFDQTs7QUFFQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBLHNCQUFzQiw2Q0FBTTtBQUM1QixjQUFjLGtEQUFXO0FBQ3pCO0FBQ0EsR0FBRztBQUNILGdCQUFnQixrREFBVztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG1CQUFtQiw2Q0FBTTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0EsbUJBQW1CLDZDQUFNO0FBQ3pCLFNBQVMsOENBQU87QUFDaEI7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGVBQWUsNkNBQU07QUFDckIscUJBQXFCLGtEQUFXO0FBQ2hDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxjQUFjLDZDQUFNO0FBQ3BCLEVBQUUsZ0RBQVM7QUFDWDtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0E7QUFDQSxTQUFTLDhDQUFPO0FBQ2hCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBLGtHQUFrRyxhQUFhO0FBQy9HO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsS0FBSyxJQUFJO0FBQ1QsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRW1YO0FBQ25YIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8uL25vZGVfbW9kdWxlcy9AZG5kLWtpdC91dGlsaXRpZXMvZGlzdC91dGlsaXRpZXMuZXNtLmpzP2EwOWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlTWVtbywgdXNlTGF5b3V0RWZmZWN0LCB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5cbmZ1bmN0aW9uIHVzZUNvbWJpbmVkUmVmcygpIHtcbiAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIHJlZnMgPSBuZXcgQXJyYXkoX2xlbiksIF9rZXkgPSAwOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgcmVmc1tfa2V5XSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgfVxuXG4gIHJldHVybiB1c2VNZW1vKCgpID0+IG5vZGUgPT4ge1xuICAgIHJlZnMuZm9yRWFjaChyZWYgPT4gcmVmKG5vZGUpKTtcbiAgfSwgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuICByZWZzKTtcbn1cblxuLy8gaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L2Jsb2IvbWFzdGVyL3BhY2thZ2VzL3NoYXJlZC9FeGVjdXRpb25FbnZpcm9ubWVudC5qc1xuY29uc3QgY2FuVXNlRE9NID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgdHlwZW9mIHdpbmRvdy5kb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCcgJiYgdHlwZW9mIHdpbmRvdy5kb2N1bWVudC5jcmVhdGVFbGVtZW50ICE9PSAndW5kZWZpbmVkJztcblxuZnVuY3Rpb24gaXNXaW5kb3coZWxlbWVudCkge1xuICBjb25zdCBlbGVtZW50U3RyaW5nID0gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKGVsZW1lbnQpO1xuICByZXR1cm4gZWxlbWVudFN0cmluZyA9PT0gJ1tvYmplY3QgV2luZG93XScgfHwgLy8gSW4gRWxlY3Ryb24gY29udGV4dCB0aGUgV2luZG93IG9iamVjdCBzZXJpYWxpemVzIHRvIFtvYmplY3QgZ2xvYmFsXVxuICBlbGVtZW50U3RyaW5nID09PSAnW29iamVjdCBnbG9iYWxdJztcbn1cblxuZnVuY3Rpb24gaXNOb2RlKG5vZGUpIHtcbiAgcmV0dXJuICdub2RlVHlwZScgaW4gbm9kZTtcbn1cblxuZnVuY3Rpb24gZ2V0V2luZG93KHRhcmdldCkge1xuICB2YXIgX3RhcmdldCRvd25lckRvY3VtZW50LCBfdGFyZ2V0JG93bmVyRG9jdW1lbnQyO1xuXG4gIGlmICghdGFyZ2V0KSB7XG4gICAgcmV0dXJuIHdpbmRvdztcbiAgfVxuXG4gIGlmIChpc1dpbmRvdyh0YXJnZXQpKSB7XG4gICAgcmV0dXJuIHRhcmdldDtcbiAgfVxuXG4gIGlmICghaXNOb2RlKHRhcmdldCkpIHtcbiAgICByZXR1cm4gd2luZG93O1xuICB9XG5cbiAgcmV0dXJuIChfdGFyZ2V0JG93bmVyRG9jdW1lbnQgPSAoX3RhcmdldCRvd25lckRvY3VtZW50MiA9IHRhcmdldC5vd25lckRvY3VtZW50KSA9PSBudWxsID8gdm9pZCAwIDogX3RhcmdldCRvd25lckRvY3VtZW50Mi5kZWZhdWx0VmlldykgIT0gbnVsbCA/IF90YXJnZXQkb3duZXJEb2N1bWVudCA6IHdpbmRvdztcbn1cblxuZnVuY3Rpb24gaXNEb2N1bWVudChub2RlKSB7XG4gIGNvbnN0IHtcbiAgICBEb2N1bWVudFxuICB9ID0gZ2V0V2luZG93KG5vZGUpO1xuICByZXR1cm4gbm9kZSBpbnN0YW5jZW9mIERvY3VtZW50O1xufVxuXG5mdW5jdGlvbiBpc0hUTUxFbGVtZW50KG5vZGUpIHtcbiAgaWYgKGlzV2luZG93KG5vZGUpKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG5cbiAgcmV0dXJuIG5vZGUgaW5zdGFuY2VvZiBnZXRXaW5kb3cobm9kZSkuSFRNTEVsZW1lbnQ7XG59XG5cbmZ1bmN0aW9uIGlzU1ZHRWxlbWVudChub2RlKSB7XG4gIHJldHVybiBub2RlIGluc3RhbmNlb2YgZ2V0V2luZG93KG5vZGUpLlNWR0VsZW1lbnQ7XG59XG5cbmZ1bmN0aW9uIGdldE93bmVyRG9jdW1lbnQodGFyZ2V0KSB7XG4gIGlmICghdGFyZ2V0KSB7XG4gICAgcmV0dXJuIGRvY3VtZW50O1xuICB9XG5cbiAgaWYgKGlzV2luZG93KHRhcmdldCkpIHtcbiAgICByZXR1cm4gdGFyZ2V0LmRvY3VtZW50O1xuICB9XG5cbiAgaWYgKCFpc05vZGUodGFyZ2V0KSkge1xuICAgIHJldHVybiBkb2N1bWVudDtcbiAgfVxuXG4gIGlmIChpc0RvY3VtZW50KHRhcmdldCkpIHtcbiAgICByZXR1cm4gdGFyZ2V0O1xuICB9XG5cbiAgaWYgKGlzSFRNTEVsZW1lbnQodGFyZ2V0KSB8fCBpc1NWR0VsZW1lbnQodGFyZ2V0KSkge1xuICAgIHJldHVybiB0YXJnZXQub3duZXJEb2N1bWVudDtcbiAgfVxuXG4gIHJldHVybiBkb2N1bWVudDtcbn1cblxuLyoqXHJcbiAqIEEgaG9vayB0aGF0IHJlc29sdmVzIHRvIHVzZUVmZmVjdCBvbiB0aGUgc2VydmVyIGFuZCB1c2VMYXlvdXRFZmZlY3Qgb24gdGhlIGNsaWVudFxyXG4gKiBAcGFyYW0gY2FsbGJhY2sge2Z1bmN0aW9ufSBDYWxsYmFjayBmdW5jdGlvbiB0aGF0IGlzIGludm9rZWQgd2hlbiB0aGUgZGVwZW5kZW5jaWVzIG9mIHRoZSBob29rIGNoYW5nZVxyXG4gKi9cblxuY29uc3QgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCA9IGNhblVzZURPTSA/IHVzZUxheW91dEVmZmVjdCA6IHVzZUVmZmVjdDtcblxuZnVuY3Rpb24gdXNlRXZlbnQoaGFuZGxlcikge1xuICBjb25zdCBoYW5kbGVyUmVmID0gdXNlUmVmKGhhbmRsZXIpO1xuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBoYW5kbGVyUmVmLmN1cnJlbnQgPSBoYW5kbGVyO1xuICB9KTtcbiAgcmV0dXJuIHVzZUNhbGxiYWNrKGZ1bmN0aW9uICgpIHtcbiAgICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShfbGVuKSwgX2tleSA9IDA7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgIGFyZ3NbX2tleV0gPSBhcmd1bWVudHNbX2tleV07XG4gICAgfVxuXG4gICAgcmV0dXJuIGhhbmRsZXJSZWYuY3VycmVudCA9PSBudWxsID8gdm9pZCAwIDogaGFuZGxlclJlZi5jdXJyZW50KC4uLmFyZ3MpO1xuICB9LCBbXSk7XG59XG5cbmZ1bmN0aW9uIHVzZUludGVydmFsKCkge1xuICBjb25zdCBpbnRlcnZhbFJlZiA9IHVzZVJlZihudWxsKTtcbiAgY29uc3Qgc2V0ID0gdXNlQ2FsbGJhY2soKGxpc3RlbmVyLCBkdXJhdGlvbikgPT4ge1xuICAgIGludGVydmFsUmVmLmN1cnJlbnQgPSBzZXRJbnRlcnZhbChsaXN0ZW5lciwgZHVyYXRpb24pO1xuICB9LCBbXSk7XG4gIGNvbnN0IGNsZWFyID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmIChpbnRlcnZhbFJlZi5jdXJyZW50ICE9PSBudWxsKSB7XG4gICAgICBjbGVhckludGVydmFsKGludGVydmFsUmVmLmN1cnJlbnQpO1xuICAgICAgaW50ZXJ2YWxSZWYuY3VycmVudCA9IG51bGw7XG4gICAgfVxuICB9LCBbXSk7XG4gIHJldHVybiBbc2V0LCBjbGVhcl07XG59XG5cbmZ1bmN0aW9uIHVzZUxhdGVzdFZhbHVlKHZhbHVlLCBkZXBlbmRlbmNpZXMpIHtcbiAgaWYgKGRlcGVuZGVuY2llcyA9PT0gdm9pZCAwKSB7XG4gICAgZGVwZW5kZW5jaWVzID0gW3ZhbHVlXTtcbiAgfVxuXG4gIGNvbnN0IHZhbHVlUmVmID0gdXNlUmVmKHZhbHVlKTtcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHZhbHVlUmVmLmN1cnJlbnQgIT09IHZhbHVlKSB7XG4gICAgICB2YWx1ZVJlZi5jdXJyZW50ID0gdmFsdWU7XG4gICAgfVxuICB9LCBkZXBlbmRlbmNpZXMpO1xuICByZXR1cm4gdmFsdWVSZWY7XG59XG5cbmZ1bmN0aW9uIHVzZUxhenlNZW1vKGNhbGxiYWNrLCBkZXBlbmRlbmNpZXMpIHtcbiAgY29uc3QgdmFsdWVSZWYgPSB1c2VSZWYoKTtcbiAgcmV0dXJuIHVzZU1lbW8oKCkgPT4ge1xuICAgIGNvbnN0IG5ld1ZhbHVlID0gY2FsbGJhY2sodmFsdWVSZWYuY3VycmVudCk7XG4gICAgdmFsdWVSZWYuY3VycmVudCA9IG5ld1ZhbHVlO1xuICAgIHJldHVybiBuZXdWYWx1ZTtcbiAgfSwgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuICBbLi4uZGVwZW5kZW5jaWVzXSk7XG59XG5cbmZ1bmN0aW9uIHVzZU5vZGVSZWYob25DaGFuZ2UpIHtcbiAgY29uc3Qgb25DaGFuZ2VIYW5kbGVyID0gdXNlRXZlbnQob25DaGFuZ2UpO1xuICBjb25zdCBub2RlID0gdXNlUmVmKG51bGwpO1xuICBjb25zdCBzZXROb2RlUmVmID0gdXNlQ2FsbGJhY2soZWxlbWVudCA9PiB7XG4gICAgaWYgKGVsZW1lbnQgIT09IG5vZGUuY3VycmVudCkge1xuICAgICAgb25DaGFuZ2VIYW5kbGVyID09IG51bGwgPyB2b2lkIDAgOiBvbkNoYW5nZUhhbmRsZXIoZWxlbWVudCwgbm9kZS5jdXJyZW50KTtcbiAgICB9XG5cbiAgICBub2RlLmN1cnJlbnQgPSBlbGVtZW50O1xuICB9LCAvL2VzbGludC1kaXNhYmxlLW5leHQtbGluZVxuICBbXSk7XG4gIHJldHVybiBbbm9kZSwgc2V0Tm9kZVJlZl07XG59XG5cbmZ1bmN0aW9uIHVzZVByZXZpb3VzKHZhbHVlKSB7XG4gIGNvbnN0IHJlZiA9IHVzZVJlZigpO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHJlZi5jdXJyZW50ID0gdmFsdWU7XG4gIH0sIFt2YWx1ZV0pO1xuICByZXR1cm4gcmVmLmN1cnJlbnQ7XG59XG5cbmxldCBpZHMgPSB7fTtcbmZ1bmN0aW9uIHVzZVVuaXF1ZUlkKHByZWZpeCwgdmFsdWUpIHtcbiAgcmV0dXJuIHVzZU1lbW8oKCkgPT4ge1xuICAgIGlmICh2YWx1ZSkge1xuICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cblxuICAgIGNvbnN0IGlkID0gaWRzW3ByZWZpeF0gPT0gbnVsbCA/IDAgOiBpZHNbcHJlZml4XSArIDE7XG4gICAgaWRzW3ByZWZpeF0gPSBpZDtcbiAgICByZXR1cm4gcHJlZml4ICsgXCItXCIgKyBpZDtcbiAgfSwgW3ByZWZpeCwgdmFsdWVdKTtcbn1cblxuZnVuY3Rpb24gY3JlYXRlQWRqdXN0bWVudEZuKG1vZGlmaWVyKSB7XG4gIHJldHVybiBmdW5jdGlvbiAob2JqZWN0KSB7XG4gICAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIGFkanVzdG1lbnRzID0gbmV3IEFycmF5KF9sZW4gPiAxID8gX2xlbiAtIDEgOiAwKSwgX2tleSA9IDE7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgIGFkanVzdG1lbnRzW19rZXkgLSAxXSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgICB9XG5cbiAgICByZXR1cm4gYWRqdXN0bWVudHMucmVkdWNlKChhY2N1bXVsYXRvciwgYWRqdXN0bWVudCkgPT4ge1xuICAgICAgY29uc3QgZW50cmllcyA9IE9iamVjdC5lbnRyaWVzKGFkanVzdG1lbnQpO1xuXG4gICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlQWRqdXN0bWVudF0gb2YgZW50cmllcykge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IGFjY3VtdWxhdG9yW2tleV07XG5cbiAgICAgICAgaWYgKHZhbHVlICE9IG51bGwpIHtcbiAgICAgICAgICBhY2N1bXVsYXRvcltrZXldID0gdmFsdWUgKyBtb2RpZmllciAqIHZhbHVlQWRqdXN0bWVudDtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gYWNjdW11bGF0b3I7XG4gICAgfSwgeyAuLi5vYmplY3RcbiAgICB9KTtcbiAgfTtcbn1cblxuY29uc3QgYWRkID0gLyojX19QVVJFX18qL2NyZWF0ZUFkanVzdG1lbnRGbigxKTtcbmNvbnN0IHN1YnRyYWN0ID0gLyojX19QVVJFX18qL2NyZWF0ZUFkanVzdG1lbnRGbigtMSk7XG5cbmZ1bmN0aW9uIGhhc1ZpZXdwb3J0UmVsYXRpdmVDb29yZGluYXRlcyhldmVudCkge1xuICByZXR1cm4gJ2NsaWVudFgnIGluIGV2ZW50ICYmICdjbGllbnRZJyBpbiBldmVudDtcbn1cblxuZnVuY3Rpb24gaXNLZXlib2FyZEV2ZW50KGV2ZW50KSB7XG4gIGlmICghZXZlbnQpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cblxuICBjb25zdCB7XG4gICAgS2V5Ym9hcmRFdmVudFxuICB9ID0gZ2V0V2luZG93KGV2ZW50LnRhcmdldCk7XG4gIHJldHVybiBLZXlib2FyZEV2ZW50ICYmIGV2ZW50IGluc3RhbmNlb2YgS2V5Ym9hcmRFdmVudDtcbn1cblxuZnVuY3Rpb24gaXNUb3VjaEV2ZW50KGV2ZW50KSB7XG4gIGlmICghZXZlbnQpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cblxuICBjb25zdCB7XG4gICAgVG91Y2hFdmVudFxuICB9ID0gZ2V0V2luZG93KGV2ZW50LnRhcmdldCk7XG4gIHJldHVybiBUb3VjaEV2ZW50ICYmIGV2ZW50IGluc3RhbmNlb2YgVG91Y2hFdmVudDtcbn1cblxuLyoqXHJcbiAqIFJldHVybnMgdGhlIG5vcm1hbGl6ZWQgeCBhbmQgeSBjb29yZGluYXRlcyBmb3IgbW91c2UgYW5kIHRvdWNoIGV2ZW50cy5cclxuICovXG5cbmZ1bmN0aW9uIGdldEV2ZW50Q29vcmRpbmF0ZXMoZXZlbnQpIHtcbiAgaWYgKGlzVG91Y2hFdmVudChldmVudCkpIHtcbiAgICBpZiAoZXZlbnQudG91Y2hlcyAmJiBldmVudC50b3VjaGVzLmxlbmd0aCkge1xuICAgICAgY29uc3Qge1xuICAgICAgICBjbGllbnRYOiB4LFxuICAgICAgICBjbGllbnRZOiB5XG4gICAgICB9ID0gZXZlbnQudG91Y2hlc1swXTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHgsXG4gICAgICAgIHlcbiAgICAgIH07XG4gICAgfSBlbHNlIGlmIChldmVudC5jaGFuZ2VkVG91Y2hlcyAmJiBldmVudC5jaGFuZ2VkVG91Y2hlcy5sZW5ndGgpIHtcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgY2xpZW50WDogeCxcbiAgICAgICAgY2xpZW50WTogeVxuICAgICAgfSA9IGV2ZW50LmNoYW5nZWRUb3VjaGVzWzBdO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgeCxcbiAgICAgICAgeVxuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICBpZiAoaGFzVmlld3BvcnRSZWxhdGl2ZUNvb3JkaW5hdGVzKGV2ZW50KSkge1xuICAgIHJldHVybiB7XG4gICAgICB4OiBldmVudC5jbGllbnRYLFxuICAgICAgeTogZXZlbnQuY2xpZW50WVxuICAgIH07XG4gIH1cblxuICByZXR1cm4gbnVsbDtcbn1cblxuY29uc3QgQ1NTID0gLyojX19QVVJFX18qL09iamVjdC5mcmVlemUoe1xuICBUcmFuc2xhdGU6IHtcbiAgICB0b1N0cmluZyh0cmFuc2Zvcm0pIHtcbiAgICAgIGlmICghdHJhbnNmb3JtKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc3Qge1xuICAgICAgICB4LFxuICAgICAgICB5XG4gICAgICB9ID0gdHJhbnNmb3JtO1xuICAgICAgcmV0dXJuIFwidHJhbnNsYXRlM2QoXCIgKyAoeCA/IE1hdGgucm91bmQoeCkgOiAwKSArIFwicHgsIFwiICsgKHkgPyBNYXRoLnJvdW5kKHkpIDogMCkgKyBcInB4LCAwKVwiO1xuICAgIH1cblxuICB9LFxuICBTY2FsZToge1xuICAgIHRvU3RyaW5nKHRyYW5zZm9ybSkge1xuICAgICAgaWYgKCF0cmFuc2Zvcm0pIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCB7XG4gICAgICAgIHNjYWxlWCxcbiAgICAgICAgc2NhbGVZXG4gICAgICB9ID0gdHJhbnNmb3JtO1xuICAgICAgcmV0dXJuIFwic2NhbGVYKFwiICsgc2NhbGVYICsgXCIpIHNjYWxlWShcIiArIHNjYWxlWSArIFwiKVwiO1xuICAgIH1cblxuICB9LFxuICBUcmFuc2Zvcm06IHtcbiAgICB0b1N0cmluZyh0cmFuc2Zvcm0pIHtcbiAgICAgIGlmICghdHJhbnNmb3JtKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIFtDU1MuVHJhbnNsYXRlLnRvU3RyaW5nKHRyYW5zZm9ybSksIENTUy5TY2FsZS50b1N0cmluZyh0cmFuc2Zvcm0pXS5qb2luKCcgJyk7XG4gICAgfVxuXG4gIH0sXG4gIFRyYW5zaXRpb246IHtcbiAgICB0b1N0cmluZyhfcmVmKSB7XG4gICAgICBsZXQge1xuICAgICAgICBwcm9wZXJ0eSxcbiAgICAgICAgZHVyYXRpb24sXG4gICAgICAgIGVhc2luZ1xuICAgICAgfSA9IF9yZWY7XG4gICAgICByZXR1cm4gcHJvcGVydHkgKyBcIiBcIiArIGR1cmF0aW9uICsgXCJtcyBcIiArIGVhc2luZztcbiAgICB9XG5cbiAgfVxufSk7XG5cbmNvbnN0IFNFTEVDVE9SID0gJ2EsZnJhbWUsaWZyYW1lLGlucHV0Om5vdChbdHlwZT1oaWRkZW5dKTpub3QoOmRpc2FibGVkKSxzZWxlY3Q6bm90KDpkaXNhYmxlZCksdGV4dGFyZWE6bm90KDpkaXNhYmxlZCksYnV0dG9uOm5vdCg6ZGlzYWJsZWQpLCpbdGFiaW5kZXhdJztcbmZ1bmN0aW9uIGZpbmRGaXJzdEZvY3VzYWJsZU5vZGUoZWxlbWVudCkge1xuICBpZiAoZWxlbWVudC5tYXRjaGVzKFNFTEVDVE9SKSkge1xuICAgIHJldHVybiBlbGVtZW50O1xuICB9XG5cbiAgcmV0dXJuIGVsZW1lbnQucXVlcnlTZWxlY3RvcihTRUxFQ1RPUik7XG59XG5cbmV4cG9ydCB7IENTUywgYWRkLCBjYW5Vc2VET00sIGZpbmRGaXJzdEZvY3VzYWJsZU5vZGUsIGdldEV2ZW50Q29vcmRpbmF0ZXMsIGdldE93bmVyRG9jdW1lbnQsIGdldFdpbmRvdywgaGFzVmlld3BvcnRSZWxhdGl2ZUNvb3JkaW5hdGVzLCBpc0RvY3VtZW50LCBpc0hUTUxFbGVtZW50LCBpc0tleWJvYXJkRXZlbnQsIGlzTm9kZSwgaXNTVkdFbGVtZW50LCBpc1RvdWNoRXZlbnQsIGlzV2luZG93LCBzdWJ0cmFjdCwgdXNlQ29tYmluZWRSZWZzLCB1c2VFdmVudCwgdXNlSW50ZXJ2YWwsIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QsIHVzZUxhdGVzdFZhbHVlLCB1c2VMYXp5TWVtbywgdXNlTm9kZVJlZiwgdXNlUHJldmlvdXMsIHVzZVVuaXF1ZUlkIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlsaXRpZXMuZXNtLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\n");

/***/ })

};
;