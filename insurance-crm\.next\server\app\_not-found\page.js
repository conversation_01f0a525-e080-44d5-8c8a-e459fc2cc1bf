/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\crm\\\\insurance-crm\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%22syncKeylessConfigAction%22%2C%22deleteKeylessAction%22%2C%22createOrReadKeylessAction%22%5D%5D%2C%5B%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%22invalidateCacheAction%22%5D%5D%5D&__client_imported__=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%22syncKeylessConfigAction%22%2C%22deleteKeylessAction%22%2C%22createOrReadKeylessAction%22%5D%5D%2C%5B%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%22invalidateCacheAction%22%5D%5D%5D&__client_imported__=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst actions = {\n'6335cfa5b3d4f8469ff9ce2743c0bebf155a8c29': () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\")).then(mod => mod[\"syncKeylessConfigAction\"]),\n'6da3f807e2d62eb8cf1cd6f85870a5f2514d6511': () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\")).then(mod => mod[\"deleteKeylessAction\"]),\n'95fdd5e85e5705dc3c856766e6730e6bac583c8e': () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\")).then(mod => mod[\"createOrReadKeylessAction\"]),\n'f0f9c59350cb991b195a5706211162f5c3088bab': () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\")).then(mod => mod[\"invalidateCacheAction\"]),\n}\n\nasync function endpoint(id, ...args) {\n  const action = await actions[id]()\n  return action.apply(null, args)\n}\n\n// Using CJS to avoid this to be tree-shaken away due to unused exports.\nmodule.exports = {\n  '6335cfa5b3d4f8469ff9ce2743c0bebf155a8c29': endpoint.bind(null, '6335cfa5b3d4f8469ff9ce2743c0bebf155a8c29'),\n  '6da3f807e2d62eb8cf1cd6f85870a5f2514d6511': endpoint.bind(null, '6da3f807e2d62eb8cf1cd6f85870a5f2514d6511'),\n  '95fdd5e85e5705dc3c856766e6730e6bac583c8e': endpoint.bind(null, '95fdd5e85e5705dc3c856766e6730e6bac583c8e'),\n  'f0f9c59350cb991b195a5706211162f5c3088bab': endpoint.bind(null, 'f0f9c59350cb991b195a5706211162f5c3088bab'),\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%22syncKeylessConfigAction%22%2C%22deleteKeylessAction%22%2C%22createOrReadKeylessAction%22%5D%5D%2C%5B%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%22invalidateCacheAction%22%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjcm0lNUMlNUNpbnN1cmFuY2UtY3JtJTVDJTVDY29tcG9uZW50cyU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q2NybSU1QyU1Q2luc3VyYW5jZS1jcm0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q2NybSU1QyU1Q2luc3VyYW5jZS1jcm0lNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQXFIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8/ZTZkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb3ZpZGVyc1wiXSAqLyBcIkM6XFxcXGNybVxcXFxpbnN1cmFuY2UtY3JtXFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjcm0lNUMlNUNpbnN1cmFuY2UtY3JtJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDYXBwLXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDY3JtJTVDJTVDaW5zdXJhbmNlLWNybSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjcm0lNUMlNUNpbnN1cmFuY2UtY3JtJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q2NybSU1QyU1Q2luc3VyYW5jZS1jcm0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjcm0lNUMlNUNpbnN1cmFuY2UtY3JtJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbm90LWZvdW5kLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjcm0lNUMlNUNpbnN1cmFuY2UtY3JtJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQXdIO0FBQ3hIO0FBQ0Esb09BQXlIO0FBQ3pIO0FBQ0EsME9BQTRIO0FBQzVIO0FBQ0Esd09BQTJIO0FBQzNIO0FBQ0Esa1BBQWdJO0FBQ2hJO0FBQ0Esc1FBQTBJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8/ZGQ5MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGNybVxcXFxpbnN1cmFuY2UtY3JtXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcY3JtXFxcXGluc3VyYW5jZS1jcm1cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcY3JtXFxcXGluc3VyYW5jZS1jcm1cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcY3JtXFxcXGluc3VyYW5jZS1jcm1cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxjcm1cXFxcaW5zdXJhbmNlLWNybVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG5vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcY3JtXFxcXGluc3VyYW5jZS1jcm1cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _lib_AgentContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/AgentContext */ \"(ssr)/./lib/AgentContext.tsx\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_5__.ClerkProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n            attribute: \"class\",\n            defaultTheme: \"dark\",\n            enableSystem: false,\n            disableTransitionOnChange: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_AgentContext__WEBPACK_IMPORTED_MODULE_3__.AgentProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_4__.ToastContainer, {}, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\providers.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\providers.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\providers.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\providers.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUUrQjtBQUNlO0FBQ29CO0FBQ2Y7QUFDSTtBQUVoRCxTQUFTTSxVQUFVLEVBQUVDLFFBQVEsRUFBaUM7SUFDbkUscUJBQ0UsOERBQUNOLHdEQUFhQTtrQkFDWiw0RUFBQ0Usc0RBQWtCQTtZQUNqQkssV0FBVTtZQUNWQyxjQUFhO1lBQ2JDLGNBQWM7WUFDZEMseUJBQXlCO3NCQUV6Qiw0RUFBQ1AsNERBQWFBOztvQkFDWEc7a0NBQ0QsOERBQUNGLGdFQUFjQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8uL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeD9jNTYyIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IENsZXJrUHJvdmlkZXIgfSBmcm9tIFwiQGNsZXJrL25leHRqc1wiO1xyXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlciB9IGZyb20gXCJuZXh0LXRoZW1lc1wiO1xyXG5pbXBvcnQgeyBBZ2VudFByb3ZpZGVyIH0gZnJvbSBcIkAvbGliL0FnZW50Q29udGV4dFwiO1xyXG5pbXBvcnQgeyBUb2FzdENvbnRhaW5lciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdG9hc3RcIjtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8Q2xlcmtQcm92aWRlcj5cclxuICAgICAgPE5leHRUaGVtZXNQcm92aWRlclxyXG4gICAgICAgIGF0dHJpYnV0ZT1cImNsYXNzXCJcclxuICAgICAgICBkZWZhdWx0VGhlbWU9XCJkYXJrXCJcclxuICAgICAgICBlbmFibGVTeXN0ZW09e2ZhbHNlfVxyXG4gICAgICAgIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2VcclxuICAgICAgPlxyXG4gICAgICAgIDxBZ2VudFByb3ZpZGVyPlxyXG4gICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgICAgPFRvYXN0Q29udGFpbmVyIC8+XHJcbiAgICAgICAgPC9BZ2VudFByb3ZpZGVyPlxyXG4gICAgICA8L05leHRUaGVtZXNQcm92aWRlcj5cclxuICAgIDwvQ2xlcmtQcm92aWRlcj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbIlJlYWN0IiwiQ2xlcmtQcm92aWRlciIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJBZ2VudFByb3ZpZGVyIiwiVG9hc3RDb250YWluZXIiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiIsImF0dHJpYnV0ZSIsImRlZmF1bHRUaGVtZSIsImVuYWJsZVN5c3RlbSIsImRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast-store.ts":
/*!**************************************!*\
  !*** ./components/ui/toast-store.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useToastStore: () => (/* binding */ useToastStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n\nconst useToastStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        toasts: [],\n        addToast: (toast)=>set((state)=>({\n                    toasts: [\n                        ...state.toasts,\n                        {\n                            ...toast,\n                            id: toast.id || Date.now().toString()\n                        }\n                    ]\n                })),\n        removeToast: (id)=>set((state)=>({\n                    toasts: state.toasts.filter((toast)=>toast.id !== id)\n                })),\n        clearToasts: ()=>set({\n                toasts: []\n            })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RvYXN0LXN0b3JlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlDO0FBVTFCLE1BQU1DLGdCQUFnQkQsK0NBQU1BLENBQWEsQ0FBQ0UsTUFBUztRQUN4REMsUUFBUSxFQUFFO1FBQ1ZDLFVBQVUsQ0FBQ0MsUUFDVEgsSUFBSSxDQUFDSSxRQUFXO29CQUNkSCxRQUFROzJCQUFJRyxNQUFNSCxNQUFNO3dCQUFFOzRCQUFFLEdBQUdFLEtBQUs7NEJBQUVFLElBQUlGLE1BQU1FLEVBQUUsSUFBSUMsS0FBS0MsR0FBRyxHQUFHQyxRQUFRO3dCQUFHO3FCQUFFO2dCQUNoRjtRQUNGQyxhQUFhLENBQUNKLEtBQ1pMLElBQUksQ0FBQ0ksUUFBVztvQkFDZEgsUUFBUUcsTUFBTUgsTUFBTSxDQUFDUyxNQUFNLENBQUMsQ0FBQ1AsUUFBVUEsTUFBTUUsRUFBRSxLQUFLQTtnQkFDdEQ7UUFDRk0sYUFBYSxJQUFNWCxJQUFJO2dCQUFFQyxRQUFRLEVBQUU7WUFBQztJQUN0QyxJQUFJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8uL2NvbXBvbmVudHMvdWkvdG9hc3Qtc3RvcmUudHM/OTRlNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJztcclxuaW1wb3J0IHsgVG9hc3QgfSBmcm9tICcuL3RvYXN0JztcclxuXHJcbmludGVyZmFjZSBUb2FzdFN0b3JlIHtcclxuICB0b2FzdHM6IFRvYXN0W107XHJcbiAgYWRkVG9hc3Q6ICh0b2FzdDogVG9hc3QpID0+IHZvaWQ7XHJcbiAgcmVtb3ZlVG9hc3Q6IChpZDogc3RyaW5nKSA9PiB2b2lkO1xyXG4gIGNsZWFyVG9hc3RzOiAoKSA9PiB2b2lkO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgdXNlVG9hc3RTdG9yZSA9IGNyZWF0ZTxUb2FzdFN0b3JlPigoc2V0KSA9PiAoe1xyXG4gIHRvYXN0czogW10sXHJcbiAgYWRkVG9hc3Q6ICh0b2FzdCkgPT5cclxuICAgIHNldCgoc3RhdGUpID0+ICh7XHJcbiAgICAgIHRvYXN0czogWy4uLnN0YXRlLnRvYXN0cywgeyAuLi50b2FzdCwgaWQ6IHRvYXN0LmlkIHx8IERhdGUubm93KCkudG9TdHJpbmcoKSB9XSxcclxuICAgIH0pKSxcclxuICByZW1vdmVUb2FzdDogKGlkKSA9PlxyXG4gICAgc2V0KChzdGF0ZSkgPT4gKHtcclxuICAgICAgdG9hc3RzOiBzdGF0ZS50b2FzdHMuZmlsdGVyKCh0b2FzdCkgPT4gdG9hc3QuaWQgIT09IGlkKSxcclxuICAgIH0pKSxcclxuICBjbGVhclRvYXN0czogKCkgPT4gc2V0KHsgdG9hc3RzOiBbXSB9KSxcclxufSkpOyJdLCJuYW1lcyI6WyJjcmVhdGUiLCJ1c2VUb2FzdFN0b3JlIiwic2V0IiwidG9hc3RzIiwiYWRkVG9hc3QiLCJ0b2FzdCIsInN0YXRlIiwiaWQiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJyZW1vdmVUb2FzdCIsImZpbHRlciIsImNsZWFyVG9hc3RzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast-store.ts\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastContainer: () => (/* binding */ ToastContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _toast_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./toast-store */ \"(ssr)/./components/ui/toast-store.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastContainer auto */ \n\n\n\n\nconst getIcon = (variant)=>{\n    switch(variant){\n        case \"success\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-5 h-5 text-green-500 animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 20,\n                columnNumber: 14\n            }, undefined);\n        case \"error\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-5 h-5 text-red-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 22,\n                columnNumber: 14\n            }, undefined);\n        case \"destructive\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-5 h-5 text-red-500 animate-bounce\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 24,\n                columnNumber: 14\n            }, undefined);\n        case \"info\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-5 h-5 text-blue-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 26,\n                columnNumber: 14\n            }, undefined);\n        case \"winner\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"w-5 h-5 text-yellow-500 animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 28,\n                columnNumber: 14\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"w-5 h-5 text-purple-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 30,\n                columnNumber: 14\n            }, undefined);\n    }\n};\nconst ToastContainer = ()=>{\n    const { toasts, removeToast } = (0,_toast_store__WEBPACK_IMPORTED_MODULE_3__.useToastStore)();\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        const timers = toasts.map((toast)=>{\n            return setTimeout(()=>{\n                removeToast(toast.id);\n            }, 5000);\n        });\n        return ()=>{\n            timers.forEach(clearTimeout);\n        };\n    }, [\n        toasts,\n        removeToast\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 space-y-2 pointer-events-none\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"pointer-events-auto flex items-start gap-4 rounded-md border p-4 shadow-2xl transition-all animate-in slide-in-from-right-5 duration-300\", \"backdrop-blur-sm bg-opacity-95\", toast.variant === \"success\" ? \"bg-green-950/90 border-green-500/50 text-green-50 shadow-green-500/20\" : toast.variant === \"error\" || toast.variant === \"destructive\" ? \"bg-red-950/90 border-red-500/50 text-red-50 shadow-red-500/20\" : toast.variant === \"info\" ? \"bg-blue-950/90 border-blue-500/50 text-blue-50 shadow-blue-500/20\" : toast.variant === \"winner\" ? \"bg-gradient-to-r from-yellow-900/90 to-amber-900/90 border-yellow-500/50 text-yellow-50 shadow-yellow-500/30\" : \"bg-gray-950/90 border-gray-500/50 text-gray-50 shadow-purple-500/20\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: getIcon(toast.variant)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-1 flex-grow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-bold text-sm uppercase tracking-wide\",\n                                    children: toast.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, undefined),\n                            toast.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm opacity-90 leading-tight\",\n                                children: toast.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, undefined),\n                            toast.action\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>removeToast(toast.id),\n                        className: \"flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, toast.id, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/AgentContext.tsx":
/*!******************************!*\
  !*** ./lib/AgentContext.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgentProvider: () => (/* binding */ AgentProvider),\n/* harmony export */   useAgent: () => (/* binding */ useAgent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabaseClient */ \"(ssr)/./lib/supabaseClient.ts\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* __next_internal_client_entry_do_not_use__ AgentProvider,useAgent auto */ \n\n\n\n// Context default\nconst AgentContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    agent: null,\n    setAgent: ()=>{}\n});\n// Provider component\nconst AgentProvider = ({ children })=>{\n    const [agent, setAgent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchAgentSettings = async ()=>{\n            const { data, error } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"agent_settings\").select(\"*\").single();\n            if (data && !error) {\n                setAgent(data);\n                if (data.theme) setTheme(data.theme); // Apply preferred theme\n            } else {\n                setTheme(\"dark\"); // Default fallback\n            }\n        };\n        fetchAgentSettings();\n    }, [\n        setTheme\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AgentContext.Provider, {\n        value: {\n            agent,\n            setAgent\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\lib\\\\AgentContext.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook to access agent anywhere\nconst useAgent = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AgentContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/AgentContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/supabaseClient.ts":
/*!*******************************!*\
  !*** ./lib/supabaseClient.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://ufguunkzmqlkrtlfascw.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.f5ivY0tOZ82p6xyUeR7yoz4rVq41y4wOzBVHVv3lls0\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        persistSession: true,\n        autoRefreshToken: true,\n        detectSessionInUrl: true,\n        storage:  false ? 0 : undefined\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvc3VwYWJhc2VDbGllbnQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUQ7QUFFckQsTUFBTUMsY0FBY0MsMENBQW9DO0FBQ3hELE1BQU1HLGtCQUFrQkgsNk5BQXlDO0FBRTFELE1BQU1LLFdBQVdQLG1FQUFZQSxDQUFDQyxhQUFhSSxpQkFBaUI7SUFDakVHLE1BQU07UUFDSkMsZ0JBQWdCO1FBQ2hCQyxrQkFBa0I7UUFDbEJDLG9CQUFvQjtRQUNwQkMsU0FBUyxNQUFrQixHQUFjQyxDQUFtQixHQUFHRTtJQUNqRTtBQUNGLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnN1cmFuY2UtY3JtLy4vbGliL3N1cGFiYXNlQ2xpZW50LnRzPzNhN2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcclxuXHJcbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMITtcclxuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkhO1xyXG5cclxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXksIHtcclxuICBhdXRoOiB7XHJcbiAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSxcclxuICAgIGF1dG9SZWZyZXNoVG9rZW46IHRydWUsXHJcbiAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUsXHJcbiAgICBzdG9yYWdlOiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IHdpbmRvdy5sb2NhbFN0b3JhZ2UgOiB1bmRlZmluZWQsXHJcbiAgfSxcclxufSk7Il0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwic3VwYWJhc2UiLCJhdXRoIiwicGVyc2lzdFNlc3Npb24iLCJhdXRvUmVmcmVzaFRva2VuIiwiZGV0ZWN0U2Vzc2lvbkluVXJsIiwic3RvcmFnZSIsIndpbmRvdyIsImxvY2FsU3RvcmFnZSIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabaseClient.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBQ0o7QUFFbEMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8uL2xpYi91dGlscy50cz9mNzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCI7XHJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIjtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xyXG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSk7XHJcbn1cclxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1839c07a2566\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnN1cmFuY2UtY3JtLy4vYXBwL2dsb2JhbHMuY3NzPzU3YjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxODM5YzA3YTI1NjZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"CSM - Chris Shannahan's Machine\",\n    description: \"A Bad Ass Machine for Winners - The Ultimate Insurance CRM\",\n    keywords: [\n        \"CRM\",\n        \"insurance\",\n        \"Chris Shannahan\",\n        \"sales machine\",\n        \"winners\"\n    ],\n    authors: [\n        {\n            name: \"Chris Shannahan\"\n        }\n    ],\n    openGraph: {\n        title: \"CSM - Chris Shannahan's Machine\",\n        description: \"A Bad Ass Machine for Winners - The Ultimate Insurance CRM\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    src: \"https://assets.calendly.com/assets/external/widget.js\",\n                    async: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} min-h-screen bg-background antialiased`,\n                suppressHydrationWarning: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\crm\insurance-crm\components\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@clerk","vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/tslib","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/snakecase-keys","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/swr","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/use-sync-external-store","vendor-chunks/next-themes","vendor-chunks/dequal","vendor-chunks/@swc","vendor-chunks/zustand","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();