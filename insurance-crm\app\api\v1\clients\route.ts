import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { withAPIAuth, APIContext } from '@/lib/api/auth';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * GET /api/v1/clients
 * Retrieve all clients for the authenticated user
 */
async function handleGET(request: NextRequest, context: APIContext): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status');
    const state = searchParams.get('state');
    const carrier = searchParams.get('carrier');
    const search = searchParams.get('search');

    // Build query
    let query = supabase
      .from('active_clients')
      .select(`
        id,
        first_name,
        last_name,
        email,
        phone,
        dob,
        birth_date,
        gender,
        address,
        city,
        state,
        zip,
        county,
        status,
        source,
        agent_name,
        agent_npn,
        plan_name,
        carrier,
        metal_level,
        premium,
        gross_premium,
        policy_number,
        created_at,
        updated_at,
        policies (
          id,
          plan_name,
          carrier,
          type,
          status,
          premium,
          gross_premium,
          start_date,
          end_date,
          policy_number,
          member_count,
          monthly_commission,
          annual_commission
        )
      `)
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (state) {
      query = query.eq('state', state);
    }
    if (carrier) {
      query = query.eq('carrier', carrier);
    }
    if (search) {
      query = query.or(`first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%,phone.like.%${search.replace(/\D/g, '')}%`);
    }

    const { data: clients, error } = await query;

    if (error) {
      console.error('Error fetching clients:', error);
      return NextResponse.json(
        { error: 'Failed to fetch clients', details: error.message },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('active_clients')
      .select('*', { count: 'exact', head: true });

    if (status) countQuery = countQuery.eq('status', status);
    if (state) countQuery = countQuery.eq('state', state);
    if (carrier) countQuery = countQuery.eq('carrier', carrier);
    if (search) {
      countQuery = countQuery.or(`first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%,phone.like.%${search.replace(/\D/g, '')}%`);
    }

    const { count } = await countQuery;

    return NextResponse.json({
      success: true,
      data: clients,
      pagination: {
        limit,
        offset,
        total: count || 0,
        hasMore: (offset + limit) < (count || 0)
      },
      filters: {
        status,
        state,
        carrier,
        search
      }
    });

  } catch (error) {
    console.error('Clients API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/clients
 * Create a new client
 */
async function handlePOST(request: NextRequest, context: APIContext): Promise<NextResponse> {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['first_name', 'last_name'];
    const missingFields = requiredFields.filter(field => !body[field]);
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Prepare client data
    const clientData = {
      clerk_user_id: context.userId,
      first_name: body.first_name,
      last_name: body.last_name,
      email: body.email,
      phone: body.phone,
      dob: body.dob,
      birth_date: body.birth_date,
      gender: body.gender,
      address: body.address,
      city: body.city,
      state: body.state,
      zip: body.zip,
      county: body.county,
      status: body.status || 'Active',
      source: body.source || 'API',
      agent_name: body.agent_name,
      agent_npn: body.agent_npn,
      plan_name: body.plan_name,
      carrier: body.carrier,
      metal_level: body.metal_level,
      premium: body.premium,
      gross_premium: body.gross_premium,
      policy_number: body.policy_number,
      notes: body.notes
    };

    const { data: client, error } = await supabase
      .from('active_clients')
      .insert(clientData)
      .select()
      .single();

    if (error) {
      console.error('Error creating client:', error);
      return NextResponse.json(
        { error: 'Failed to create client', details: error.message },
        { status: 500 }
      );
    }

    // If policy data is provided, create a policy
    if (body.policy) {
      const policyData = {
        client_id: client.id,
        clerk_user_id: context.userId,
        ...body.policy
      };

      const { error: policyError } = await supabase
        .from('policies')
        .insert(policyData);

      if (policyError) {
        console.error('Error creating policy:', policyError);
        // Don't fail the client creation, just log the error
      }
    }

    return NextResponse.json({
      success: true,
      data: client,
      message: 'Client created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create client API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Export the wrapped handlers
export const GET = withAPIAuth(handleGET, 'read');
export const POST = withAPIAuth(handlePOST, 'write');
