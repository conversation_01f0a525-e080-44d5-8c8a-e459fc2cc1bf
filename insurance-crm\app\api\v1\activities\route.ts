import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { withAPIAuth, APIContext } from '@/lib/api/auth';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * GET /api/v1/activities
 * Retrieve all activities for the authenticated user
 */
async function handleGET(request: NextRequest, context: APIContext): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');
    const type = searchParams.get('type');
    const clientId = searchParams.get('client_id');
    const leadId = searchParams.get('lead_id');
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');

    // Build query
    let query = supabase
      .from('activities')
      .select(`
        id,
        type,
        title,
        description,
        phone_number,
        email,
        metadata,
        created_at,
        updated_at,
        client:active_clients (
          id,
          first_name,
          last_name,
          email,
          phone
        ),
        lead:leads (
          id,
          full_name,
          email,
          phone
        )
      `)
      .eq('clerk_user_id', context.userId)
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    // Apply filters
    if (type) {
      query = query.eq('type', type);
    }
    if (clientId) {
      query = query.eq('client_id', clientId);
    }
    if (leadId) {
      query = query.eq('lead_id', leadId);
    }
    if (startDate) {
      query = query.gte('created_at', startDate);
    }
    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    const { data: activities, error } = await query;

    if (error) {
      console.error('Error fetching activities:', error);
      return NextResponse.json(
        { error: 'Failed to fetch activities', details: error.message },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('activities')
      .select('*', { count: 'exact', head: true })
      .eq('clerk_user_id', context.userId);

    if (type) countQuery = countQuery.eq('type', type);
    if (clientId) countQuery = countQuery.eq('client_id', clientId);
    if (leadId) countQuery = countQuery.eq('lead_id', leadId);
    if (startDate) countQuery = countQuery.gte('created_at', startDate);
    if (endDate) countQuery = countQuery.lte('created_at', endDate);

    const { count } = await countQuery;

    return NextResponse.json({
      success: true,
      data: activities,
      pagination: {
        limit,
        offset,
        total: count || 0,
        hasMore: (offset + limit) < (count || 0)
      },
      filters: {
        type,
        client_id: clientId,
        lead_id: leadId,
        start_date: startDate,
        end_date: endDate
      }
    });

  } catch (error) {
    console.error('Activities API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/activities
 * Create a new activity
 */
async function handlePOST(request: NextRequest, context: APIContext): Promise<NextResponse> {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['type', 'title'];
    const missingFields = requiredFields.filter(field => !body[field]);
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate activity type
    const validTypes = ['call', 'email', 'sms', 'meeting', 'note', 'task', 'appointment'];
    if (!validTypes.includes(body.type)) {
      return NextResponse.json(
        { error: `Invalid activity type. Must be one of: ${validTypes.join(', ')}` },
        { status: 400 }
      );
    }

    // Verify client/lead exists if provided
    if (body.client_id) {
      const { data: client, error: clientError } = await supabase
        .from('active_clients')
        .select('id')
        .eq('id', body.client_id)
        .single();

      if (clientError || !client) {
        return NextResponse.json(
          { error: 'Client not found or access denied' },
          { status: 404 }
        );
      }
    }

    if (body.lead_id) {
      const { data: lead, error: leadError } = await supabase
        .from('leads')
        .select('id')
        .eq('id', body.lead_id)
        .eq('clerk_user_id', context.userId)
        .single();

      if (leadError || !lead) {
        return NextResponse.json(
          { error: 'Lead not found or access denied' },
          { status: 404 }
        );
      }
    }

    // Prepare activity data
    const activityData = {
      clerk_user_id: context.userId,
      client_id: body.client_id || null,
      lead_id: body.lead_id || null,
      type: body.type,
      title: body.title,
      description: body.description,
      phone_number: body.phone_number,
      email: body.email,
      metadata: body.metadata || {}
    };

    const { data: activity, error } = await supabase
      .from('activities')
      .insert(activityData)
      .select(`
        *,
        client:active_clients (
          id,
          first_name,
          last_name,
          email,
          phone
        ),
        lead:leads (
          id,
          full_name,
          email,
          phone
        )
      `)
      .single();

    if (error) {
      console.error('Error creating activity:', error);
      return NextResponse.json(
        { error: 'Failed to create activity', details: error.message },
        { status: 500 }
      );
    }

    // Update last contact date for lead if applicable
    if (body.lead_id && ['call', 'email', 'sms', 'meeting'].includes(body.type)) {
      await supabase
        .from('leads')
        .update({ last_contact_date: new Date().toISOString() })
        .eq('id', body.lead_id);
    }

    return NextResponse.json({
      success: true,
      data: activity,
      message: 'Activity created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create activity API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Export the wrapped handlers
export const GET = withAPIAuth(handleGET, 'read');
export const POST = withAPIAuth(handlePOST, 'write');
