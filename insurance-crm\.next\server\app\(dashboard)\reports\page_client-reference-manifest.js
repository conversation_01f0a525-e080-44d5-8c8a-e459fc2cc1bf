globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(dashboard)/reports/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers.tsx":{"*":{"id":"(ssr)/./components/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/layout.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/tasks/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/tasks/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/leads/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/leads/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/clients/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/clients/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/page.tsx":{"*":{"id":"(ssr)/./app/auth/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/leads/[id]/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/leads/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/settings/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/commissions/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/commissions/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/calendar/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/calendar/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/reports/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/goals/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/goals/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(dashboard)/activities/page.tsx":{"*":{"id":"(ssr)/./app/(dashboard)/activities/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\crm\\insurance-crm\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\ClerkProvider.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\keyless-cookie-sync.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\PromisifiedAuthProvider.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\components\\providers.tsx":{"id":"(app-pages-browser)/./components/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\crm\\insurance-crm\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\crm\\insurance-crm\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\crm\\insurance-crm\\app\\(dashboard)\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\app\\(dashboard)\\layout.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/layout.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"C:\\crm\\insurance-crm\\app\\(dashboard)\\tasks\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/tasks/page.tsx","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\app\\(dashboard)\\leads\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/leads/page.tsx","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\app\\(dashboard)\\clients\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/clients/page.tsx","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\app\\auth\\page.tsx":{"id":"(app-pages-browser)/./app/auth/page.tsx","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\app\\(dashboard)\\leads\\[id]\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/leads/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\app\\(dashboard)\\settings\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/settings/page.tsx","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\app\\(dashboard)\\commissions\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/commissions/page.tsx","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\app\\(dashboard)\\calendar\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/calendar/page.tsx","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\app\\(dashboard)\\reports\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/reports/page.tsx","name":"*","chunks":["app/(dashboard)/reports/page","static/chunks/app/(dashboard)/reports/page.js"],"async":false},"C:\\crm\\insurance-crm\\app\\(dashboard)\\goals\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/goals/page.tsx","name":"*","chunks":[],"async":false},"C:\\crm\\insurance-crm\\app\\(dashboard)\\activities\\page.tsx":{"id":"(app-pages-browser)/./app/(dashboard)/activities/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\crm\\insurance-crm\\":[],"C:\\crm\\insurance-crm\\app\\layout":["static/css/app/layout.css"],"C:\\crm\\insurance-crm\\app\\(dashboard)\\layout":[],"C:\\crm\\insurance-crm\\app\\page":[],"C:\\crm\\insurance-crm\\app\\(dashboard)\\reports\\page":[]}}