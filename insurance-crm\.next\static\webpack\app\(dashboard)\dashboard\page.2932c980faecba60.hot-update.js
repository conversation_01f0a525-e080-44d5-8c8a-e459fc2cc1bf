"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./app/(dashboard)/dashboard/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/supabaseClient */ \"(app-pages-browser)/./lib/supabaseClient.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var _lib_commission_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/commission-config */ \"(app-pages-browser)/./lib/commission-config.ts\");\n/* harmony import */ var _components_dashboard_StartupMotivation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/StartupMotivation */ \"(app-pages-browser)/./components/dashboard/StartupMotivation.tsx\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,CheckCircle,Clock,DollarSign,Shield,TrendingUp,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,CheckCircle,Clock,DollarSign,Shield,TrendingUp,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,CheckCircle,Clock,DollarSign,Shield,TrendingUp,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,CheckCircle,Clock,DollarSign,Shield,TrendingUp,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,CheckCircle,Clock,DollarSign,Shield,TrendingUp,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,CheckCircle,Clock,DollarSign,Shield,TrendingUp,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,CheckCircle,Clock,DollarSign,Shield,TrendingUp,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,CheckCircle,Clock,DollarSign,Shield,TrendingUp,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,CheckCircle,Clock,DollarSign,Shield,TrendingUp,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,CheckCircle,Clock,DollarSign,Shield,TrendingUp,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,CheckCircle,Clock,DollarSign,Shield,TrendingUp,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,CheckCircle,Clock,DollarSign,Shield,TrendingUp,Upload,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _components_dashboard_CustomGoalsWidget__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/CustomGoalsWidget */ \"(app-pages-browser)/./components/dashboard/CustomGoalsWidget.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { userId, user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_11__.usePromisifiedAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showMotivation, setShowMotivation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalClients: 0,\n        activeClients: 0,\n        monthlyRevenue: 0,\n        revenueGrowth: 0,\n        activePolicies: 0,\n        newClientsThisMonth: 0,\n        pendingTasks: 0,\n        upcomingAppointments: 0\n    });\n    const [recentActivities, setRecentActivities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [revenueData, setRevenueData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topClients, setTopClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userId) {\n            fetchDashboardData();\n        }\n    }, [\n        userId\n    ]);\n    const fetchDashboardData = async ()=>{\n        if (!userId) return;\n        setLoading(true);\n        try {\n            // Fetch ALL clients (not filtered by clerk_user_id as per user requirements)\n            const { data: clients, error: clientsError } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_6__.supabase.from(\"active_clients\").select(\"*\").order(\"created_at\", {\n                ascending: false\n            });\n            if (clientsError) throw clientsError;\n            // Fetch policies for current user\n            const { data: policies, error: policiesError } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_6__.supabase.from(\"policies\").select(\"*\").eq(\"clerk_user_id\", userId);\n            if (policiesError) throw policiesError;\n            // Fetch tasks for current user\n            const { data: tasks } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_6__.supabase.from(\"tasks\").select(\"*\").eq(\"clerk_user_id\", userId).eq(\"status\", \"pending\");\n            // Fetch upcoming appointments (next 7 days)\n            const nextWeek = new Date();\n            nextWeek.setDate(nextWeek.getDate() + 7);\n            const { data: appointments } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_6__.supabase.from(\"calendar_events\").select(\"*\").eq(\"clerk_user_id\", userId).gte(\"start_time\", new Date().toISOString()).lte(\"start_time\", nextWeek.toISOString()).eq(\"event_type\", \"appointment\");\n            // Calculate metrics\n            const now = new Date();\n            const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n            const activeClientCount = (clients === null || clients === void 0 ? void 0 : clients.filter((c)=>c.status === \"Active\").length) || 0;\n            const activePolicies = (policies === null || policies === void 0 ? void 0 : policies.filter((p)=>p.status === \"Active\")) || [];\n            // Calculate real monthly commission revenue\n            const monthlyRevenue = activePolicies.reduce((sum, policy)=>{\n                if (!policy.carrier || !policy.state) return sum;\n                const commission = (0,_lib_commission_config__WEBPACK_IMPORTED_MODULE_8__.calculateMonthlyCommission)(policy.carrier, policy.state, policy.member_count || 1, policy.is_renewal || false, policy.is_off_exchange || false, policy.gross_premium || policy.premium);\n                return sum + commission;\n            }, 0);\n            const newClientsThisMonth = (clients === null || clients === void 0 ? void 0 : clients.filter((c)=>new Date(c.created_at) >= startOfMonth).length) || 0;\n            // Calculate real revenue growth by comparing with previous month policies\n            const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);\n            const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);\n            const lastMonthPolicies = (policies === null || policies === void 0 ? void 0 : policies.filter((p)=>{\n                const createdDate = new Date(p.created_at);\n                return createdDate >= lastMonthStart && createdDate <= lastMonthEnd && p.status === \"Active\";\n            })) || [];\n            const lastMonthRevenue = lastMonthPolicies.reduce((sum, policy)=>{\n                if (!policy.carrier || !policy.state) return sum;\n                const commission = (0,_lib_commission_config__WEBPACK_IMPORTED_MODULE_8__.calculateMonthlyCommission)(policy.carrier, policy.state, policy.member_count || 1, policy.is_renewal || false, policy.is_off_exchange || false, policy.gross_premium || policy.premium);\n                return sum + commission;\n            }, 0);\n            const revenueGrowth = lastMonthRevenue > 0 ? (monthlyRevenue - lastMonthRevenue) / lastMonthRevenue * 100 : 0;\n            setMetrics({\n                totalClients: (clients === null || clients === void 0 ? void 0 : clients.length) || 0,\n                activeClients: activeClientCount,\n                monthlyRevenue,\n                revenueGrowth,\n                activePolicies: activePolicies.length,\n                newClientsThisMonth,\n                pendingTasks: (tasks === null || tasks === void 0 ? void 0 : tasks.length) || 0,\n                upcomingAppointments: (appointments === null || appointments === void 0 ? void 0 : appointments.length) || 0\n            });\n            // Generate real revenue trend data (last 7 days based on policy creation)\n            const revenueTrend = [];\n            for(let i = 6; i >= 0; i--){\n                const date = new Date();\n                date.setDate(date.getDate() - i);\n                const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n                const dayEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);\n                const dayPolicies = (policies === null || policies === void 0 ? void 0 : policies.filter((p)=>{\n                    const createdDate = new Date(p.created_at);\n                    return createdDate >= dayStart && createdDate < dayEnd && p.status === \"Active\";\n                })) || [];\n                const dayRevenue = dayPolicies.reduce((sum, policy)=>{\n                    if (!policy.carrier || !policy.state) return sum;\n                    const commission = (0,_lib_commission_config__WEBPACK_IMPORTED_MODULE_8__.calculateMonthlyCommission)(policy.carrier, policy.state, policy.member_count || 1, policy.is_renewal || false, policy.is_off_exchange || false, policy.gross_premium || policy.premium);\n                    return sum + commission;\n                }, 0);\n                revenueTrend.push({\n                    date: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(date, \"MMM dd\"),\n                    revenue: dayRevenue\n                });\n            }\n            setRevenueData(revenueTrend);\n            // Get top clients by premium\n            const clientPremiums = new Map();\n            activePolicies.forEach((policy)=>{\n                const current = clientPremiums.get(policy.client_id) || 0;\n                clientPremiums.set(policy.client_id, current + (policy.premium || 0));\n            });\n            const topClientIds = Array.from(clientPremiums.entries()).sort((param, param1)=>{\n                let [, a] = param, [, b] = param1;\n                return b - a;\n            }).slice(0, 5).map((param)=>{\n                let [id] = param;\n                return id;\n            });\n            const topClientsData = (clients === null || clients === void 0 ? void 0 : clients.filter((c)=>topClientIds.includes(c.id)).map((client)=>({\n                    ...client,\n                    totalPremium: clientPremiums.get(client.id) || 0\n                })).sort((a, b)=>b.totalPremium - a.totalPremium)) || [];\n            setTopClients(topClientsData);\n            // Generate recent activities\n            const activities = [];\n            // Add recent clients\n            clients === null || clients === void 0 ? void 0 : clients.slice(0, 3).forEach((client)=>{\n                activities.push({\n                    id: client.id,\n                    type: \"client\",\n                    message: \"New client added: \".concat(client.first_name, \" \").concat(client.last_name),\n                    timestamp: client.created_at,\n                    status: \"success\"\n                });\n            });\n            // Add recent policies\n            policies === null || policies === void 0 ? void 0 : policies.slice(0, 2).forEach((policy)=>{\n                activities.push({\n                    id: policy.id,\n                    type: \"policy\",\n                    message: \"New \".concat(policy.carrier, \" policy activated\"),\n                    timestamp: policy.created_at,\n                    status: policy.status\n                });\n            });\n            setRecentActivities(activities.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 5));\n        } catch (error) {\n            console.error(\"Error fetching dashboard data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case \"client\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 16\n                }, this);\n            case \"policy\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 16\n                }, this);\n            case \"task\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"success\":\n            case \"Active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"default\",\n                    className: \"bg-green-500\",\n                    children: \"Active\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Pending\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"outline\",\n                    children: \"New\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n            lineNumber: 308,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            showMotivation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_StartupMotivation__WEBPACK_IMPORTED_MODULE_9__.StartupMotivation, {\n                userName: (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.fullName) || \"Champion\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-8 p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold tracking-tight\",\n                                        children: \"Agent Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Good morning! Here's your business overview for today.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/leads\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 13\n                                            }, this),\n                                            \"View Pipeline\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>router.push(\"/clients/unified-import\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 13\n                                            }, this),\n                                            \"Import Data\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Monthly Commissions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    \"$\",\n                                                    metrics.monthlyRevenue.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: metrics.revenueGrowth >= 0 ? \"text-green-600\" : \"text-red-600\",\n                                                        children: [\n                                                            metrics.revenueGrowth >= 0 ? \"+\" : \"\",\n                                                            metrics.revenueGrowth.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    \" \",\n                                                    \"vs last month\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Enrolled Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: metrics.activeClients\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    \"+\",\n                                                    metrics.newClientsThisMonth,\n                                                    \" enrolled this month\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Policies Sold\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: metrics.activePolicies\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"Life, health & supplements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Today's Tasks\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: metrics.pendingTasks\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    metrics.upcomingAppointments,\n                                                    \" appointments scheduled\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-7\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"col-span-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Revenue Trend\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Daily revenue over the last week\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_21__.ResponsiveContainer, {\n                                            width: \"100%\",\n                                            height: 350,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_22__.AreaChart, {\n                                                data: revenueData,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                            id: \"colorRevenue\",\n                                                            x1: \"0\",\n                                                            y1: \"0\",\n                                                            x2: \"0\",\n                                                            y2: \"1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                    offset: \"5%\",\n                                                                    stopColor: \"#8B5CF6\",\n                                                                    stopOpacity: 0.8\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                    offset: \"95%\",\n                                                                    stopColor: \"#8B5CF6\",\n                                                                    stopOpacity: 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                        contentStyle: {\n                                                            backgroundColor: \"hsl(var(--background))\",\n                                                            border: \"1px solid hsl(var(--border))\",\n                                                            borderRadius: \"6px\"\n                                                        },\n                                                        formatter: (value)=>[\n                                                                \"$\".concat(value.toFixed(2)),\n                                                                \"Revenue\"\n                                                            ]\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_recharts__WEBPACK_IMPORTED_MODULE_24__.Area, {\n                                                        type: \"monotone\",\n                                                        dataKey: \"revenue\",\n                                                        stroke: \"#8B5CF6\",\n                                                        fillOpacity: 1,\n                                                        fill: \"url(#colorRevenue)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"col-span-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Recent Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Latest updates from your CRM\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: recentActivities.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-muted rounded-full\",\n                                                            children: getActivityIcon(activity.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium leading-none\",\n                                                                    children: activity.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(activity.timestamp), \"MMM d, h:mm a\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        getStatusBadge(activity.status)\n                                                    ]\n                                                }, activity.id, true, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4 md:grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CustomGoalsWidget__WEBPACK_IMPORTED_MODULE_10__.CustomGoalsWidget, {\n                                userId: userId || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    \"Top Clients\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Clients with highest premium values\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: topClients.map((client, index)=>{\n                                                var _client_first_name, _client_last_name;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                        children: [\n                                                                            (_client_first_name = client.first_name) === null || _client_first_name === void 0 ? void 0 : _client_first_name[0],\n                                                                            (_client_last_name = client.last_name) === null || _client_last_name === void 0 ? void 0 : _client_last_name[0]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 491,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute -top-1 -right-1 h-3 w-3 bg-yellow-500 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium leading-none\",\n                                                                    children: [\n                                                                        client.first_name,\n                                                                        \" \",\n                                                                        client.last_name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        client.totalPremium.toLocaleString(),\n                                                                        \"/month\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>router.push(\"/clients/\".concat(client.id)),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, client.id, true, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 17\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 13\n                                            }, this),\n                                            \"Quick Actions\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Common tasks and shortcuts\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 md:grid-cols-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"justify-start\",\n                                            onClick: ()=>router.push(\"/clients/new\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"New Client\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"justify-start\",\n                                            onClick: ()=>router.push(\"/policies/new\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"New Policy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"justify-start\",\n                                            onClick: ()=>router.push(\"/tasks\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"View Tasks\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"justify-start\",\n                                            onClick: ()=>router.push(\"/reports\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_CheckCircle_Clock_DollarSign_Shield_TrendingUp_Upload_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"View Reports\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DashboardPage, \"feQ5/dvIGyrMhKHlZ7WuIIzF0nA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_11__.usePromisifiedAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});