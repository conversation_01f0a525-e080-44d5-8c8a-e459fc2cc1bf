// app/(dashboard)/activities/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { supabase } from '@/lib/supabaseClient';
import { useAuth } from '@clerk/nextjs';
import { toast } from 'sonner';
import { 
  Activity, 
  Phone, 
  Mail, 
  Calendar,
  FileText,
  UserPlus,
  DollarSign,
  MessageSquare,
  Search,
  CheckCircle,
  Clock,
  TrendingUp
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface ActivityItem {
  id: string;
  type: 'call' | 'email' | 'meeting' | 'note' | 'policy' | 'payment' | 'client' | 'task';
  title: string;
  description?: string;
  client?: {
    id: string;
    name: string;
  };
  user: string;
  timestamp: string;
  metadata?: any;
}

export default function ActivitiesPage() {
  const { userId } = useAuth();
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [filteredActivities, setFilteredActivities] = useState<ActivityItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedDateRange, setSelectedDateRange] = useState('week');

  useEffect(() => {
    if (userId) {
      fetchActivities();
    }
  }, [userId]);

  useEffect(() => {
    filterActivities();
  }, [activities, searchQuery, selectedType, selectedDateRange]);

  const fetchActivities = async () => {
    if (!userId) return;

    setLoading(true);
    try {
      // Fetch activities from database
      const { data: activitiesData, error } = await supabase
        .from('activities')
        .select(`
          *,
          leads (name),
          clients (name)
        `)
        .eq('clerk_user_id', userId)
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) {
        console.error('Error fetching activities:', error);
        toast.error('Failed to fetch activities');
        return;
      }

      // Transform data to match interface
      const transformedActivities: ActivityItem[] = (activitiesData || []).map(activity => ({
        id: activity.id,
        type: activity.type || 'note',
        title: activity.title || activity.description || 'Activity',
        description: activity.description,
        client: activity.leads?.name || activity.clients?.name ? {
          id: activity.lead_id || activity.client_id,
          name: activity.leads?.name || activity.clients?.name
        } : undefined,
        user: 'You',
        timestamp: activity.created_at,
        metadata: activity.metadata || {}
      }));

      setActivities(transformedActivities);
    } catch (error) {
      console.error('Error fetching activities:', error);
      toast.error('Failed to fetch activities');
    } finally {
      setLoading(false);
    }
  };

  const filterActivities = () => {
    let filtered = [...activities];

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(activity =>
        activity.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.client?.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by type
    if (selectedType !== 'all') {
      filtered = filtered.filter(activity => activity.type === selectedType);
    }

    // Filter by date range
    const now = new Date();
    let startDate: Date;

    switch (selectedDateRange) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'quarter':
        startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        break;
      default:
        startDate = new Date(0); // All time
    }

    filtered = filtered.filter(activity => new Date(activity.timestamp) >= startDate);

    setFilteredActivities(filtered);
  };

  // Smart insights calculations
  const getMostActiveHour = () => {
    if (activities.length === 0) return 'N/A';

    const hourCounts: Record<number, number> = {};
    activities.forEach(activity => {
      const hour = new Date(activity.timestamp).getHours();
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
    });

    const entries = Object.entries(hourCounts);
    if (entries.length === 0) return 'N/A';

    const mostActive = entries.reduce((a, b) =>
      hourCounts[Number(a[0])] > hourCounts[Number(b[0])] ? a : b
    );

    return mostActive ? `${mostActive[0]}:00` : 'N/A';
  };

  const getTopContactMethod = () => {
    if (activities.length === 0) return 'N/A';

    const methodCounts: Record<string, number> = {};
    activities.forEach(activity => {
      if (['call', 'email', 'meeting'].includes(activity.type)) {
        methodCounts[activity.type] = (methodCounts[activity.type] || 0) + 1;
      }
    });

    const entries = Object.entries(methodCounts);
    if (entries.length === 0) return 'N/A';

    const topMethod = entries.reduce((a, b) =>
      methodCounts[a[0]] > methodCounts[b[0]] ? a : b
    );

    return topMethod ? topMethod[0] : 'N/A';
  };

  const calculateResponseRate = () => {
    const totalOutbound = activities.filter(a => 
      ['call', 'email'].includes(a.type) && 
      a.description?.includes('outbound')
    ).length;
    
    const totalResponses = activities.filter(a => 
      a.type === 'call' && 
      a.description?.includes('answered')
    ).length;
    
    return totalOutbound > 0 ? ((totalResponses / totalOutbound) * 100).toFixed(1) : '0';
  };

  // Calculate real-time stats
  const calculateStats = () => {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const todayActivities = activities.filter(a => 
      new Date(a.timestamp) >= startOfDay
    ).length;
    
    const weekActivities = activities.filter(a => 
      new Date(a.timestamp) >= startOfWeek
    ).length;
    
    const clientInteractions = activities.filter(a => 
      ['call', 'email', 'meeting'].includes(a.type)
    ).length;
    
    const completedTasks = activities.filter(a => 
      a.type === 'task' && a.description?.includes('completed')
    ).length;
    
    return {
      todayActivities,
      weekActivities,
      clientInteractions,
      completedTasks
    };
  };

  const stats = calculateStats();

  // Get icon for activity type
  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'call': return Phone;
      case 'email': return Mail;
      case 'meeting': return Calendar;
      case 'note': return MessageSquare;
      case 'policy': return FileText;
      case 'payment': return DollarSign;
      case 'client': return UserPlus;
      case 'task': return CheckCircle;
      default: return Activity;
    }
  };

  // Get color for activity type (enhanced with dark mode support)
  const getActivityColor = (type: ActivityItem['type']) => {
    switch (type) {
      case 'call': return 'text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400';
      case 'email': return 'text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400';
      case 'meeting': return 'text-purple-600 bg-purple-50 dark:bg-purple-900/20 dark:text-purple-400';
      case 'note': return 'text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400';
      case 'policy': return 'text-orange-600 bg-orange-50 dark:bg-orange-900/20 dark:text-orange-400';
      case 'payment': return 'text-emerald-600 bg-emerald-50 dark:bg-emerald-900/20 dark:text-emerald-400';
      case 'client': return 'text-indigo-600 bg-indigo-50 dark:bg-indigo-900/20 dark:text-indigo-400';
      case 'task': return 'text-teal-600 bg-teal-50 dark:bg-teal-900/20 dark:text-teal-400';
      default: return 'text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  // Export functionality
  const exportActivities = () => {
    const exportData = {
      generated: new Date().toISOString(),
      dateRange: selectedDateRange,
      activityType: selectedType,
      totalActivities: filteredActivities.length,
      insights: {
        mostActiveHour: getMostActiveHour(),
        topContactMethod: getTopContactMethod(),
        responseRate: calculateResponseRate() + '%'
      },
      activities: filteredActivities.map(activity => ({
        date: format(new Date(activity.timestamp), 'yyyy-MM-dd'),
        time: format(new Date(activity.timestamp), 'HH:mm'),
        type: activity.type,
        title: activity.title,
        description: activity.description,
        client: activity.client?.name,
        user: activity.user
      }))
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `activities-${format(new Date(), 'yyyy-MM-dd')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    toast.success('Activities exported successfully!');
  };



  // Group activities by date
  const groupedActivities = filteredActivities.reduce((groups, activity) => {
    const date = format(new Date(activity.timestamp), 'yyyy-MM-dd');
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(activity);
    return groups;
  }, {} as Record<string, ActivityItem[]>);

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Activity Timeline</h1>
          <p className="text-muted-foreground mt-1">Track all actions and events</p>
        </div>
        <Button variant="outline" onClick={exportActivities}>
          <Activity className="h-4 w-4 mr-2" />
          Export Activities
        </Button>
      </div>

      {/* Real-time Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Today&apos;s Activities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.todayActivities}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {stats.todayActivities > 0 ? '+' : ''}{stats.todayActivities} from yesterday
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">This Week</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.weekActivities}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Across {activities.length} total activities
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Client Interactions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.clientInteractions}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Most active: {getMostActiveHour()}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Tasks Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedTasks}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Response rate: {calculateResponseRate()}%
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* Smart Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Smart Insights
          </CardTitle>
          <CardDescription>AI-powered activity analysis</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Peak Activity Hour</span>
              </div>
              <p className="text-2xl font-bold text-blue-600">{getMostActiveHour()}</p>
              <p className="text-xs text-muted-foreground">Most productive time</p>
            </div>
            <div className="p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <MessageSquare className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Top Contact Method</span>
              </div>
              <p className="text-2xl font-bold text-green-600 capitalize">{getTopContactMethod()}</p>
              <p className="text-xs text-muted-foreground">Primary communication</p>
            </div>
            <div className="p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="h-4 w-4 text-purple-500" />
                <span className="text-sm font-medium">Response Rate</span>
              </div>
              <p className="text-2xl font-bold text-purple-600">{calculateResponseRate()}%</p>
              <p className="text-xs text-muted-foreground">Client engagement</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search activities..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-[150px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Activities</SelectItem>
                <SelectItem value="call">Calls</SelectItem>
                <SelectItem value="email">Emails</SelectItem>
                <SelectItem value="meeting">Meetings</SelectItem>
                <SelectItem value="policy">Policies</SelectItem>
                <SelectItem value="payment">Payments</SelectItem>
                <SelectItem value="client">Clients</SelectItem>
                <SelectItem value="task">Tasks</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedDateRange} onValueChange={setSelectedDateRange}>
              <SelectTrigger className="w-[150px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="all">All Time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Activity Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
          <CardDescription>All system and user activities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            {Object.entries(groupedActivities).map(([date, activities]) => (
              <div key={date}>
                <div className="flex items-center gap-4 mb-4">
                  <div className="h-px bg-border flex-1" />
                  <span className="text-sm font-medium text-muted-foreground">
                    {format(new Date(date), 'EEEE, MMMM d, yyyy')}
                  </span>
                  <div className="h-px bg-border flex-1" />
                </div>
                
                <div className="space-y-4">
                  {activities.map((activity) => {
                    const Icon = getActivityIcon(activity.type);
                    return (
                      <div key={activity.id} className="flex gap-4 p-4 rounded-lg border hover:bg-accent/50 transition-colors">
                        <div className="flex-shrink-0">
                          <div className={cn(
                            "w-12 h-12 rounded-full flex items-center justify-center border-2 border-background shadow-sm",
                            getActivityColor(activity.type)
                          )}>
                            <Icon className="h-6 w-6" />
                          </div>
                        </div>
                        
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <p className="font-semibold">{activity.title}</p>
                              {activity.client && (
                                <Badge variant="outline" className="text-xs">
                                  {activity.client.name}
                                </Badge>
                              )}
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {format(new Date(activity.timestamp), 'h:mm a')}
                            </span>
                          </div>
                          
                          {activity.description && (
                            <p className="text-sm text-muted-foreground leading-relaxed">
                              {activity.description}
                            </p>
                          )}
                          
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Activity className="h-3 w-3" />
                              {activity.type.charAt(0).toUpperCase() + activity.type.slice(1)}
                            </span>
                            <span>•</span>
                            <span>by {activity.user}</span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}