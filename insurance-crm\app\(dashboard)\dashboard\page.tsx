"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { supabase } from "@/lib/supabaseClient";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { calculateMonthlyCommission } from "@/lib/commission-config";
import { StartupMotivation } from "@/components/dashboard/StartupMotivation";
import { useAuth } from "@clerk/nextjs";
import {
  Users,
  DollarSign,
  Shield,
  TrendingUp,
  CheckCircle,
  Clock,
  ArrowRight,
  Activity,
  Target,
  Zap,
  Award,
  UserPlus,
  Upload
} from "lucide-react";
import {
  ResponsiveContainer,
  Tooltip,
  AreaChart,
  Area
} from "recharts";
import { CustomGoalsWidget } from "@/components/dashboard/CustomGoalsWidget";

interface DashboardMetrics {
  totalClients: number;
  activeClients: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  activePolicies: number;
  newClientsThisMonth: number;
  pendingTasks: number;
  upcomingAppointments: number;
}

interface RecentActivity {
  id: string;
  type: 'client' | 'policy' | 'task';
  message: string;
  timestamp: string;
  status?: string;
}

export default function DashboardPage() {
  const router = useRouter();
  const { userId, user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [showMotivation, setShowMotivation] = useState(true);
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    totalClients: 0,
    activeClients: 0,
    monthlyRevenue: 0,
    revenueGrowth: 0,
    activePolicies: 0,
    newClientsThisMonth: 0,
    pendingTasks: 0,
    upcomingAppointments: 0
  });
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [revenueData, setRevenueData] = useState<any[]>([]);
  const [topClients, setTopClients] = useState<any[]>([]);

  useEffect(() => {
    if (userId) {
      fetchDashboardData();
    }
  }, [userId]);

  const fetchDashboardData = async () => {
    if (!userId) return;
    
    setLoading(true);
    try {
      // Fetch ALL clients (not filtered by clerk_user_id as per user requirements)
      const { data: clients, error: clientsError } = await supabase
        .from("active_clients")
        .select("*")
        .order("created_at", { ascending: false });

      if (clientsError) throw clientsError;

      // Fetch policies for current user
      const { data: policies, error: policiesError } = await supabase
        .from("policies")
        .select("*")
        .eq("clerk_user_id", userId);

      if (policiesError) throw policiesError;

      // Fetch tasks for current user
      const { data: tasks } = await supabase
        .from("tasks")
        .select("*")
        .eq("clerk_user_id", userId)
        .eq("status", "pending");

      // Fetch upcoming appointments (next 7 days)
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + 7);

      const { data: appointments } = await supabase
        .from("calendar_events")
        .select("*")
        .eq("clerk_user_id", userId)
        .gte("start_time", new Date().toISOString())
        .lte("start_time", nextWeek.toISOString())
        .eq("event_type", "appointment");

      // Calculate metrics
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      
      const activeClientCount = clients?.filter(c => c.status === "Active").length || 0;
      const activePolicies = policies?.filter(p => p.status === "Active") || [];

      // Calculate real monthly commission revenue
      const monthlyRevenue = activePolicies.reduce((sum, policy) => {
        if (!policy.carrier || !policy.state) return sum;

        const commission = calculateMonthlyCommission(
          policy.carrier,
          policy.state,
          policy.member_count || 1,
          policy.is_renewal || false,
          policy.is_off_exchange || false,
          policy.gross_premium || policy.premium
        );

        return sum + commission;
      }, 0);

      const newClientsThisMonth = clients?.filter(c =>
        new Date(c.created_at) >= startOfMonth
      ).length || 0;

      // Calculate real revenue growth by comparing with previous month policies
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

      const lastMonthPolicies = policies?.filter(p => {
        const createdDate = new Date(p.created_at);
        return createdDate >= lastMonthStart && createdDate <= lastMonthEnd && p.status === "Active";
      }) || [];

      const lastMonthRevenue = lastMonthPolicies.reduce((sum, policy) => {
        if (!policy.carrier || !policy.state) return sum;

        const commission = calculateMonthlyCommission(
          policy.carrier,
          policy.state,
          policy.member_count || 1,
          policy.is_renewal || false,
          policy.is_off_exchange || false,
          policy.gross_premium || policy.premium
        );

        return sum + commission;
      }, 0);

      const revenueGrowth = lastMonthRevenue > 0
        ? ((monthlyRevenue - lastMonthRevenue) / lastMonthRevenue) * 100
        : 0;

      setMetrics({
        totalClients: clients?.length || 0,
        activeClients: activeClientCount,
        monthlyRevenue,
        revenueGrowth,
        activePolicies: activePolicies.length,
        newClientsThisMonth,
        pendingTasks: tasks?.length || 0,
        upcomingAppointments: appointments?.length || 0
      });

      // Generate real revenue trend data (last 7 days based on policy creation)
      const revenueTrend = [];
      for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const dayEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);

        const dayPolicies = policies?.filter(p => {
          const createdDate = new Date(p.created_at);
          return createdDate >= dayStart && createdDate < dayEnd && p.status === "Active";
        }) || [];

        const dayRevenue = dayPolicies.reduce((sum, policy) => {
          if (!policy.carrier || !policy.state) return sum;

          const commission = calculateMonthlyCommission(
            policy.carrier,
            policy.state,
            policy.member_count || 1,
            policy.is_renewal || false,
            policy.is_off_exchange || false,
            policy.gross_premium || policy.premium
          );

          return sum + commission;
        }, 0);

        revenueTrend.push({
          date: format(date, "MMM dd"),
          revenue: dayRevenue
        });
      }
      setRevenueData(revenueTrend);

      // Get top clients by premium
      const clientPremiums = new Map();
      activePolicies.forEach(policy => {
        const current = clientPremiums.get(policy.client_id) || 0;
        clientPremiums.set(policy.client_id, current + (policy.premium || 0));
      });

      const topClientIds = Array.from(clientPremiums.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([id]) => id);

      const topClientsData = clients?.filter(c => topClientIds.includes(c.id))
        .map(client => ({
          ...client,
          totalPremium: clientPremiums.get(client.id) || 0
        }))
        .sort((a, b) => b.totalPremium - a.totalPremium) || [];

      setTopClients(topClientsData);

      // Generate recent activities
      const activities: RecentActivity[] = [];
      
      // Add recent clients
      clients?.slice(0, 3).forEach(client => {
        activities.push({
          id: client.id,
          type: 'client',
          message: `New client added: ${client.first_name} ${client.last_name}`,
          timestamp: client.created_at,
          status: 'success'
        });
      });

      // Add recent policies
      policies?.slice(0, 2).forEach(policy => {
        activities.push({
          id: policy.id,
          type: 'policy',
          message: `New ${policy.carrier} policy activated`,
          timestamp: policy.created_at,
          status: policy.status
        });
      });

      setRecentActivities(activities.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      ).slice(0, 5));


    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'client':
        return <Users className="h-4 w-4" />;
      case 'policy':
        return <Shield className="h-4 w-4" />;
      case 'task':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'success':
      case 'Active':
        return <Badge variant="default" className="bg-green-500">Active</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      default:
        return <Badge variant="outline">New</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Startup Motivation Modal */}
      {showMotivation && (
        <StartupMotivation 
          userName={user?.firstName || user?.fullName || "Champion"} 
        />
      )}
      
      <div className="flex flex-col gap-8 p-8">
        {/* Header */}
        <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Agent Dashboard</h1>
          <p className="text-muted-foreground">
            Good morning! Here's your business overview for today.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.push("/leads")}>
            <UserPlus className="mr-2 h-4 w-4" />
            View Pipeline
          </Button>
          <Button onClick={() => router.push("/clients/unified-import")}>
            <Upload className="mr-2 h-4 w-4" />
            Import Data
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Commissions</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${metrics.monthlyRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <span className={metrics.revenueGrowth >= 0 ? "text-green-600" : "text-red-600"}>
                {metrics.revenueGrowth >= 0 ? "+" : ""}{metrics.revenueGrowth.toFixed(1)}%
              </span>{" "}
              vs last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Enrolled Clients</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeClients}</div>
            <p className="text-xs text-muted-foreground">
              +{metrics.newClientsThisMonth} enrolled this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Policies Sold</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activePolicies}</div>
            <p className="text-xs text-muted-foreground">
              Life, health & supplements
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Tasks</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.pendingTasks}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.upcomingAppointments} appointments scheduled
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Activity Row */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* Revenue Trend */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Revenue Trend</CardTitle>
            <CardDescription>Daily revenue over the last week</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={350}>
              <AreaChart data={revenueData}>
                <defs>
                  <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <Tooltip
                  contentStyle={{
                    backgroundColor: "hsl(var(--background))",
                    border: "1px solid hsl(var(--border))",
                    borderRadius: "6px"
                  }}
                  formatter={(value: any) => [`$${value.toFixed(2)}`, "Revenue"]}
                />
                <Area
                  type="monotone"
                  dataKey="revenue"
                  stroke="#8B5CF6"
                  fillOpacity={1}
                  fill="url(#colorRevenue)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest updates from your CRM</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-4">
                  <div className="p-2 bg-muted rounded-full">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {activity.message}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(activity.timestamp), "MMM d, h:mm a")}
                    </p>
                  </div>
                  {getStatusBadge(activity.status)}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Goals and Top Clients Row */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Custom Goals Widget */}
        <CustomGoalsWidget userId={userId || ''} />

        {/* Top Clients */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Top Clients
            </CardTitle>
            <CardDescription>Clients with highest premium values</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topClients.map((client, index) => (
                <div key={client.id} className="flex items-center space-x-4">
                  <div className="relative">
                    <Avatar>
                      <AvatarFallback>
                        {client.first_name?.[0]}{client.last_name?.[0]}
                      </AvatarFallback>
                    </Avatar>
                    {index === 0 && (
                      <div className="absolute -top-1 -right-1 h-3 w-3 bg-yellow-500 rounded-full" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium leading-none">
                      {client.first_name} {client.last_name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      ${client.totalPremium.toLocaleString()}/month
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push(`/clients/${client.id}`)}
                  >
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Quick Actions
          </CardTitle>
          <CardDescription>Common tasks and shortcuts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => router.push("/clients/new")}
            >
              <Users className="mr-2 h-4 w-4" />
              New Client
            </Button>
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => router.push("/policies/new")}
            >
              <Shield className="mr-2 h-4 w-4" />
              New Policy
            </Button>
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => router.push("/tasks")}
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              View Tasks
            </Button>
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => router.push("/reports")}
            >
              <TrendingUp className="mr-2 h-4 w-4" />
              View Reports
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
    </>
  );
}