"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/google-logging-utils";
exports.ids = ["vendor-chunks/google-logging-utils"];
exports.modules = {

/***/ "(rsc)/./node_modules/google-logging-utils/build/src/colours.js":
/*!****************************************************************!*\
  !*** ./node_modules/google-logging-utils/build/src/colours.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     https://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Colours = void 0;\n/**\n * Handles figuring out if we can use ANSI colours and handing out the escape codes.\n *\n * This is for package-internal use only, and may change at any time.\n *\n * @private\n * @internal\n */\nclass Colours {\n    /**\n     * @param stream The stream (e.g. process.stderr)\n     * @returns true if the stream should have colourization enabled\n     */\n    static isEnabled(stream) {\n        return (stream.isTTY &&\n            (typeof stream.getColorDepth === 'function'\n                ? stream.getColorDepth() > 2\n                : true));\n    }\n    static refresh() {\n        Colours.enabled = Colours.isEnabled(process.stderr);\n        if (!this.enabled) {\n            Colours.reset = '';\n            Colours.bright = '';\n            Colours.dim = '';\n            Colours.red = '';\n            Colours.green = '';\n            Colours.yellow = '';\n            Colours.blue = '';\n            Colours.magenta = '';\n            Colours.cyan = '';\n            Colours.white = '';\n            Colours.grey = '';\n        }\n        else {\n            Colours.reset = '\\u001b[0m';\n            Colours.bright = '\\u001b[1m';\n            Colours.dim = '\\u001b[2m';\n            Colours.red = '\\u001b[31m';\n            Colours.green = '\\u001b[32m';\n            Colours.yellow = '\\u001b[33m';\n            Colours.blue = '\\u001b[34m';\n            Colours.magenta = '\\u001b[35m';\n            Colours.cyan = '\\u001b[36m';\n            Colours.white = '\\u001b[37m';\n            Colours.grey = '\\u001b[90m';\n        }\n    }\n}\nexports.Colours = Colours;\nColours.enabled = false;\nColours.reset = '';\nColours.bright = '';\nColours.dim = '';\nColours.red = '';\nColours.green = '';\nColours.yellow = '';\nColours.blue = '';\nColours.magenta = '';\nColours.cyan = '';\nColours.white = '';\nColours.grey = '';\nColours.refresh();\n//# sourceMappingURL=colours.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-logging-utils/build/src/colours.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-logging-utils/build/src/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/google-logging-utils/build/src/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     https://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./logging-utils */ \"(rsc)/./node_modules/google-logging-utils/build/src/logging-utils.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-logging-utils/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-logging-utils/build/src/logging-utils.js":
/*!**********************************************************************!*\
  !*** ./node_modules/google-logging-utils/build/src/logging-utils.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2021-2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     https://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.env = exports.DebugLogBackendBase = exports.placeholder = exports.AdhocDebugLogger = exports.LogSeverity = void 0;\nexports.getNodeBackend = getNodeBackend;\nexports.getDebugBackend = getDebugBackend;\nexports.getStructuredBackend = getStructuredBackend;\nexports.setBackend = setBackend;\nexports.log = log;\nconst node_events_1 = __webpack_require__(/*! node:events */ \"node:events\");\nconst process = __importStar(__webpack_require__(/*! node:process */ \"node:process\"));\nconst util = __importStar(__webpack_require__(/*! node:util */ \"node:util\"));\nconst colours_1 = __webpack_require__(/*! ./colours */ \"(rsc)/./node_modules/google-logging-utils/build/src/colours.js\");\n// Some functions (as noted) are based on the Node standard library, from\n// the following file:\n//\n// https://github.com/nodejs/node/blob/main/lib/internal/util/debuglog.js\n/**\n * This module defines an ad-hoc debug logger for Google Cloud Platform\n * client libraries in Node. An ad-hoc debug logger is a tool which lets\n * users use an external, unified interface (in this case, environment\n * variables) to determine what logging they want to see at runtime. This\n * isn't necessarily fed into the console, but is meant to be under the\n * control of the user. The kind of logging that will be produced by this\n * is more like \"call retry happened\", not \"event you'd want to record\n * in Cloud Logger\".\n *\n * More for Googlers implementing libraries with it:\n * go/cloud-client-logging-design\n */\n/**\n * Possible log levels. These are a subset of Cloud Observability levels.\n * https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry#LogSeverity\n */\nvar LogSeverity;\n(function (LogSeverity) {\n    LogSeverity[\"DEFAULT\"] = \"DEFAULT\";\n    LogSeverity[\"DEBUG\"] = \"DEBUG\";\n    LogSeverity[\"INFO\"] = \"INFO\";\n    LogSeverity[\"WARNING\"] = \"WARNING\";\n    LogSeverity[\"ERROR\"] = \"ERROR\";\n})(LogSeverity || (exports.LogSeverity = LogSeverity = {}));\n/**\n * Our logger instance. This actually contains the meat of dealing\n * with log lines, including EventEmitter. This contains the function\n * that will be passed back to users of the package.\n */\nclass AdhocDebugLogger extends node_events_1.EventEmitter {\n    /**\n     * @param upstream The backend will pass a function that will be\n     *   called whenever our logger function is invoked.\n     */\n    constructor(namespace, upstream) {\n        super();\n        this.namespace = namespace;\n        this.upstream = upstream;\n        this.func = Object.assign(this.invoke.bind(this), {\n            // Also add an instance pointer back to us.\n            instance: this,\n            // And pull over the EventEmitter functionality.\n            on: (event, listener) => this.on(event, listener),\n        });\n        // Convenience methods for log levels.\n        this.func.debug = (...args) => this.invokeSeverity(LogSeverity.DEBUG, ...args);\n        this.func.info = (...args) => this.invokeSeverity(LogSeverity.INFO, ...args);\n        this.func.warn = (...args) => this.invokeSeverity(LogSeverity.WARNING, ...args);\n        this.func.error = (...args) => this.invokeSeverity(LogSeverity.ERROR, ...args);\n        this.func.sublog = (namespace) => log(namespace, this.func);\n    }\n    invoke(fields, ...args) {\n        // Push out any upstream logger first.\n        if (this.upstream) {\n            this.upstream(fields, ...args);\n        }\n        // Emit sink events.\n        this.emit('log', fields, args);\n    }\n    invokeSeverity(severity, ...args) {\n        this.invoke({ severity }, ...args);\n    }\n}\nexports.AdhocDebugLogger = AdhocDebugLogger;\n/**\n * This can be used in place of a real logger while waiting for Promises or disabling logging.\n */\nexports.placeholder = new AdhocDebugLogger('', () => { }).func;\n/**\n * The base class for debug logging backends. It's possible to use this, but the\n * same non-guarantees above still apply (unstable interface, etc).\n *\n * @private\n * @internal\n */\nclass DebugLogBackendBase {\n    constructor() {\n        var _a;\n        this.cached = new Map();\n        this.filters = [];\n        this.filtersSet = false;\n        // Look for the Node config variable for what systems to enable. We'll store\n        // these for the log method below, which will call setFilters() once.\n        let nodeFlag = (_a = process.env[exports.env.nodeEnables]) !== null && _a !== void 0 ? _a : '*';\n        if (nodeFlag === 'all') {\n            nodeFlag = '*';\n        }\n        this.filters = nodeFlag.split(',');\n    }\n    log(namespace, fields, ...args) {\n        try {\n            if (!this.filtersSet) {\n                this.setFilters();\n                this.filtersSet = true;\n            }\n            let logger = this.cached.get(namespace);\n            if (!logger) {\n                logger = this.makeLogger(namespace);\n                this.cached.set(namespace, logger);\n            }\n            logger(fields, ...args);\n        }\n        catch (e) {\n            // Silently ignore all errors; we don't want them to interfere with\n            // the user's running app.\n            // e;\n            console.error(e);\n        }\n    }\n}\nexports.DebugLogBackendBase = DebugLogBackendBase;\n// The basic backend. This one definitely works, but it's less feature-filled.\n//\n// Rather than using util.debuglog, this implements the same basic logic directly.\n// The reason for this decision is that debuglog checks the value of the\n// NODE_DEBUG environment variable before any user code runs; we therefore\n// can't pipe our own enables into it (and util.debuglog will never print unless\n// the user duplicates it into NODE_DEBUG, which isn't reasonable).\n//\nclass NodeBackend extends DebugLogBackendBase {\n    constructor() {\n        super(...arguments);\n        // Default to allowing all systems, since we gate earlier based on whether the\n        // variable is empty.\n        this.enabledRegexp = /.*/g;\n    }\n    isEnabled(namespace) {\n        return this.enabledRegexp.test(namespace);\n    }\n    makeLogger(namespace) {\n        if (!this.enabledRegexp.test(namespace)) {\n            return () => { };\n        }\n        return (fields, ...args) => {\n            var _a;\n            // TODO: `fields` needs to be turned into a string here, one way or another.\n            const nscolour = `${colours_1.Colours.green}${namespace}${colours_1.Colours.reset}`;\n            const pid = `${colours_1.Colours.yellow}${process.pid}${colours_1.Colours.reset}`;\n            let level;\n            switch (fields.severity) {\n                case LogSeverity.ERROR:\n                    level = `${colours_1.Colours.red}${fields.severity}${colours_1.Colours.reset}`;\n                    break;\n                case LogSeverity.INFO:\n                    level = `${colours_1.Colours.magenta}${fields.severity}${colours_1.Colours.reset}`;\n                    break;\n                case LogSeverity.WARNING:\n                    level = `${colours_1.Colours.yellow}${fields.severity}${colours_1.Colours.reset}`;\n                    break;\n                default:\n                    level = (_a = fields.severity) !== null && _a !== void 0 ? _a : LogSeverity.DEFAULT;\n                    break;\n            }\n            const msg = util.formatWithOptions({ colors: colours_1.Colours.enabled }, ...args);\n            const filteredFields = Object.assign({}, fields);\n            delete filteredFields.severity;\n            const fieldsJson = Object.getOwnPropertyNames(filteredFields).length\n                ? JSON.stringify(filteredFields)\n                : '';\n            const fieldsColour = fieldsJson\n                ? `${colours_1.Colours.grey}${fieldsJson}${colours_1.Colours.reset}`\n                : '';\n            console.error('%s [%s|%s] %s%s', pid, nscolour, level, msg, fieldsJson ? ` ${fieldsColour}` : '');\n        };\n    }\n    // Regexp patterns below are from here:\n    // https://github.com/nodejs/node/blob/c0aebed4b3395bd65d54b18d1fd00f071002ac20/lib/internal/util/debuglog.js#L36\n    setFilters() {\n        const totalFilters = this.filters.join(',');\n        const regexp = totalFilters\n            .replace(/[|\\\\{}()[\\]^$+?.]/g, '\\\\$&')\n            .replace(/\\*/g, '.*')\n            .replace(/,/g, '$|^');\n        this.enabledRegexp = new RegExp(`^${regexp}$`, 'i');\n    }\n}\n/**\n * @returns A backend based on Node util.debuglog; this is the default.\n */\nfunction getNodeBackend() {\n    return new NodeBackend();\n}\nclass DebugBackend extends DebugLogBackendBase {\n    constructor(pkg) {\n        super();\n        this.debugPkg = pkg;\n    }\n    makeLogger(namespace) {\n        const debugLogger = this.debugPkg(namespace);\n        return (fields, ...args) => {\n            // TODO: `fields` needs to be turned into a string here.\n            debugLogger(args[0], ...args.slice(1));\n        };\n    }\n    setFilters() {\n        var _a;\n        const existingFilters = (_a = process.env['NODE_DEBUG']) !== null && _a !== void 0 ? _a : '';\n        process.env['NODE_DEBUG'] = `${existingFilters}${existingFilters ? ',' : ''}${this.filters.join(',')}`;\n    }\n}\n/**\n * Creates a \"debug\" package backend. The user must call require('debug') and pass\n * the resulting object to this function.\n *\n * ```\n *  setBackend(getDebugBackend(require('debug')))\n * ```\n *\n * https://www.npmjs.com/package/debug\n *\n * Note: Google does not explicitly endorse or recommend this package; it's just\n * being provided as an option.\n *\n * @returns A backend based on the npm \"debug\" package.\n */\nfunction getDebugBackend(debugPkg) {\n    return new DebugBackend(debugPkg);\n}\n/**\n * This pretty much works like the Node logger, but it outputs structured\n * logging JSON matching Google Cloud's ingestion specs. Rather than handling\n * its own output, it wraps another backend. The passed backend must be a subclass\n * of `DebugLogBackendBase` (any of the backends exposed by this package will work).\n */\nclass StructuredBackend extends DebugLogBackendBase {\n    constructor(upstream) {\n        var _a;\n        super();\n        this.upstream = (_a = upstream) !== null && _a !== void 0 ? _a : new NodeBackend();\n    }\n    makeLogger(namespace) {\n        const debugLogger = this.upstream.makeLogger(namespace);\n        return (fields, ...args) => {\n            var _a;\n            const severity = (_a = fields.severity) !== null && _a !== void 0 ? _a : LogSeverity.INFO;\n            const json = Object.assign({\n                severity,\n                message: util.format(...args),\n            }, fields);\n            const jsonString = JSON.stringify(json);\n            debugLogger(fields, jsonString);\n        };\n    }\n    setFilters() {\n        this.upstream.setFilters();\n    }\n}\n/**\n * Creates a \"structured logging\" backend. This pretty much works like the\n * Node logger, but it outputs structured logging JSON matching Google\n * Cloud's ingestion specs instead of plain text.\n *\n * ```\n *  setBackend(getStructuredBackend())\n * ```\n *\n * @param upstream If you want to use something besides the Node backend to\n *   write the actual log lines into, pass that here.\n * @returns A backend based on Google Cloud structured logging.\n */\nfunction getStructuredBackend(upstream) {\n    return new StructuredBackend(upstream);\n}\n/**\n * The environment variables that we standardized on, for all ad-hoc logging.\n */\nexports.env = {\n    /**\n     * Filter wildcards specific to the Node syntax, and similar to the built-in\n     * utils.debuglog() environment variable. If missing, disables logging.\n     */\n    nodeEnables: 'GOOGLE_SDK_NODE_LOGGING',\n};\n// Keep a copy of all namespaced loggers so users can reliably .on() them.\n// Note that these cached functions will need to deal with changes in the backend.\nconst loggerCache = new Map();\n// Our current global backend. This might be:\nlet cachedBackend = undefined;\n/**\n * Set the backend to use for our log output.\n * - A backend object\n * - null to disable logging\n * - undefined for \"nothing yet\", defaults to the Node backend\n *\n * @param backend Results from one of the get*Backend() functions.\n */\nfunction setBackend(backend) {\n    cachedBackend = backend;\n    loggerCache.clear();\n}\n/**\n * Creates a logging function. Multiple calls to this with the same namespace\n * will produce the same logger, with the same event emitter hooks.\n *\n * Namespaces can be a simple string (\"system\" name), or a qualified string\n * (system:subsystem), which can be used for filtering, or for \"system:*\".\n *\n * @param namespace The namespace, a descriptive text string.\n * @returns A function you can call that works similar to console.log().\n */\nfunction log(namespace, parent) {\n    // If the enable flag isn't set, do nothing.\n    const enablesFlag = process.env[exports.env.nodeEnables];\n    if (!enablesFlag) {\n        return exports.placeholder;\n    }\n    // This might happen mostly if the typings are dropped in a user's code,\n    // or if they're calling from JavaScript.\n    if (!namespace) {\n        return exports.placeholder;\n    }\n    // Handle sub-loggers.\n    if (parent) {\n        namespace = `${parent.instance.namespace}:${namespace}`;\n    }\n    // Reuse loggers so things like event sinks are persistent.\n    const existing = loggerCache.get(namespace);\n    if (existing) {\n        return existing.func;\n    }\n    // Do we have a backend yet?\n    if (cachedBackend === null) {\n        // Explicitly disabled.\n        return exports.placeholder;\n    }\n    else if (cachedBackend === undefined) {\n        // One hasn't been made yet, so default to Node.\n        cachedBackend = getNodeBackend();\n    }\n    // The logger is further wrapped so we can handle the backend changing out.\n    const logger = (() => {\n        let previousBackend = undefined;\n        const newLogger = new AdhocDebugLogger(namespace, (fields, ...args) => {\n            if (previousBackend !== cachedBackend) {\n                // Did the user pass a custom backend?\n                if (cachedBackend === null) {\n                    // Explicitly disabled.\n                    return;\n                }\n                else if (cachedBackend === undefined) {\n                    // One hasn't been made yet, so default to Node.\n                    cachedBackend = getNodeBackend();\n                }\n                previousBackend = cachedBackend;\n            }\n            cachedBackend === null || cachedBackend === void 0 ? void 0 : cachedBackend.log(namespace, fields, ...args);\n        });\n        return newLogger;\n    })();\n    loggerCache.set(namespace, logger);\n    return logger.func;\n}\n//# sourceMappingURL=logging-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-logging-utils/build/src/logging-utils.js\n");

/***/ })

};
;