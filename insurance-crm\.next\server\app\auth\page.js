/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/page";
exports.ids = ["app/auth/page"];
exports.modules = {

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/auth/page.tsx */ \"(rsc)/./app/auth/page.tsx\")), \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\crm\\\\insurance-crm\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/page\",\n        pathname: \"/auth\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%22syncKeylessConfigAction%22%2C%22deleteKeylessAction%22%2C%22createOrReadKeylessAction%22%5D%5D%2C%5B%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%22invalidateCacheAction%22%5D%5D%5D&__client_imported__=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%22syncKeylessConfigAction%22%2C%22deleteKeylessAction%22%2C%22createOrReadKeylessAction%22%5D%5D%2C%5B%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%22invalidateCacheAction%22%5D%5D%5D&__client_imported__=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst actions = {\n'6335cfa5b3d4f8469ff9ce2743c0bebf155a8c29': () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\")).then(mod => mod[\"syncKeylessConfigAction\"]),\n'6da3f807e2d62eb8cf1cd6f85870a5f2514d6511': () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\")).then(mod => mod[\"deleteKeylessAction\"]),\n'95fdd5e85e5705dc3c856766e6730e6bac583c8e': () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\")).then(mod => mod[\"createOrReadKeylessAction\"]),\n'f0f9c59350cb991b195a5706211162f5c3088bab': () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\")).then(mod => mod[\"invalidateCacheAction\"]),\n}\n\nasync function endpoint(id, ...args) {\n  const action = await actions[id]()\n  return action.apply(null, args)\n}\n\n// Using CJS to avoid this to be tree-shaken away due to unused exports.\nmodule.exports = {\n  '6335cfa5b3d4f8469ff9ce2743c0bebf155a8c29': endpoint.bind(null, '6335cfa5b3d4f8469ff9ce2743c0bebf155a8c29'),\n  '6da3f807e2d62eb8cf1cd6f85870a5f2514d6511': endpoint.bind(null, '6da3f807e2d62eb8cf1cd6f85870a5f2514d6511'),\n  '95fdd5e85e5705dc3c856766e6730e6bac583c8e': endpoint.bind(null, '95fdd5e85e5705dc3c856766e6730e6bac583c8e'),\n  'f0f9c59350cb991b195a5706211162f5c3088bab': endpoint.bind(null, 'f0f9c59350cb991b195a5706211162f5c3088bab'),\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%22syncKeylessConfigAction%22%2C%22deleteKeylessAction%22%2C%22createOrReadKeylessAction%22%5D%5D%2C%5B%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%22invalidateCacheAction%22%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/auth/page.tsx */ \"(ssr)/./app/auth/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjcm0lNUMlNUNpbnN1cmFuY2UtY3JtJTVDJTVDYXBwJTVDJTVDYXV0aCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrSkFBZ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnN1cmFuY2UtY3JtLz81MjUwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcY3JtXFxcXGluc3VyYW5jZS1jcm1cXFxcYXBwXFxcXGF1dGhcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjcm0lNUMlNUNpbnN1cmFuY2UtY3JtJTVDJTVDY29tcG9uZW50cyU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q2NybSU1QyU1Q2luc3VyYW5jZS1jcm0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q2NybSU1QyU1Q2luc3VyYW5jZS1jcm0lNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQXFIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8/ZTZkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb3ZpZGVyc1wiXSAqLyBcIkM6XFxcXGNybVxcXFxpbnN1cmFuY2UtY3JtXFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccrm%5C%5Cinsurance-crm%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/auth/page.tsx":
/*!***************************!*\
  !*** ./app/auth/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabaseClient */ \"(ssr)/./lib/supabaseClient.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/tabs */ \"(ssr)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chrome.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Chrome,Eye,EyeOff,Loader2,Lock,Mail,Quote,Rocket,Shield,Sparkles,Target,TrendingUp,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _lib_billionaire_quotes__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/billionaire-quotes */ \"(ssr)/./lib/billionaire-quotes.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\nfunction AuthPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"login\");\n    // Form states\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Animation states\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    // Quote states\n    const [currentQuote, setCurrentQuote] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((0,_lib_billionaire_quotes__WEBPACK_IMPORTED_MODULE_10__.getDailyQuote)());\n    const [quoteKey, setQuoteKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if already logged in\n        const checkAuth = async ()=>{\n            const { data: { session } } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n            if (session) {\n                router.push(\"/dashboard\");\n            }\n        };\n        checkAuth();\n        // Mouse follow effect\n        const handleMouseMove = (e)=>{\n            setMousePosition({\n                x: e.clientX,\n                y: e.clientY\n            });\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        // Quote rotation effect - change quote every 10 seconds\n        const quoteInterval = setInterval(()=>{\n            setCurrentQuote((0,_lib_billionaire_quotes__WEBPACK_IMPORTED_MODULE_10__.getRandomQuote)());\n            setQuoteKey((prev)=>prev + 1);\n        }, 10000);\n        return ()=>{\n            window.removeEventListener(\"mousemove\", handleMouseMove);\n            clearInterval(quoteInterval);\n        };\n    }, [\n        router\n    ]);\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            const { error } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) throw error;\n            const successQuote = (0,_lib_billionaire_quotes__WEBPACK_IMPORTED_MODULE_10__.getRandomQuote)();\n            toast({\n                title: \"\\uD83D\\uDE80 Welcome back, Champion!\",\n                description: `Ready to dominate today? \"${successQuote.quote}\" - ${successQuote.author}`\n            });\n            router.push(\"/dashboard\");\n        } catch (error) {\n            toast({\n                title: \"Login failed\",\n                description: error.message || \"Invalid credentials\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSignup = async (e)=>{\n        e.preventDefault();\n        if (password !== confirmPassword) {\n            toast({\n                title: \"Passwords don't match\",\n                description: \"Please make sure your passwords match\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setLoading(true);\n        try {\n            const { error } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: {\n                        full_name: name\n                    }\n                }\n            });\n            if (error) throw error;\n            const welcomeQuote = (0,_lib_billionaire_quotes__WEBPACK_IMPORTED_MODULE_10__.getRandomQuote)();\n            toast({\n                title: \"\\uD83C\\uDF89 Welcome to the Winners Circle!\",\n                description: `Account created! Check your email to verify. Remember: \"${welcomeQuote.quote}\" - ${welcomeQuote.author}`\n            });\n            setActiveTab(\"login\");\n        } catch (error) {\n            toast({\n                title: \"Signup failed\",\n                description: error.message || \"Failed to create account\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGoogleLogin = async ()=>{\n        try {\n            const { error } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signInWithOAuth({\n                provider: \"google\",\n                options: {\n                    redirectTo: `${window.location.origin}/auth/callback`\n                }\n            });\n            if (error) throw error;\n        } catch (error) {\n            toast({\n                title: \"Google login failed\",\n                description: error.message || \"Failed to login with Google\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const features = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            text: \"Smart Lead Management\",\n            color: \"text-blue-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            text: \"AI-Powered Insights\",\n            color: \"text-purple-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            text: \"Real-time Analytics\",\n            color: \"text-green-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            text: \"Team Collaboration\",\n            color: \"text-orange-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen relative overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-center\",\n                        style: {\n                            backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                        className: \"absolute w-96 h-96 bg-purple-500 rounded-full blur-3xl opacity-20\",\n                        animate: {\n                            x: [\n                                0,\n                                100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        style: {\n                            left: `${mousePosition.x * 0.05}px`,\n                            top: `${mousePosition.y * 0.05}px`\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                        className: \"absolute w-96 h-96 bg-blue-500 rounded-full blur-3xl opacity-20\",\n                        animate: {\n                            x: [\n                                0,\n                                -100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        style: {\n                            right: `${mousePosition.x * 0.05}px`,\n                            bottom: `${mousePosition.y * 0.05}px`\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex min-h-screen items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    className: \"w-full max-w-5xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-8 lg:grid-cols-2 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:block space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                                        whileHover: {\n                                                            rotate: 360\n                                                        },\n                                                        transition: {\n                                                            duration: 0.5\n                                                        },\n                                                        className: \"p-3 rounded-xl bg-gradient-to-br from-purple-600 to-blue-600 shadow-2xl\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl font-bold text-white\",\n                                                        children: \"CSM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-300\",\n                                                children: \"A Bad Ass Machine for Winners\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 0.4\n                                        },\n                                        className: \"space-y-4\",\n                                        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.5 + index * 0.1\n                                                },\n                                                whileHover: {\n                                                    x: 10\n                                                },\n                                                className: \"flex items-center gap-3 p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 transition-all\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                        className: `w-6 h-6 ${feature.color}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: feature.text\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: -20\n                                        },\n                                        transition: {\n                                            delay: 0.9,\n                                            duration: 0.6\n                                        },\n                                        className: \"relative p-6 rounded-2xl bg-gradient-to-br from-amber-500/10 to-orange-500/10 border border-amber-500/20 backdrop-blur-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-4 left-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-8 h-8 text-amber-400 opacity-60\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                transition: {\n                                                    delay: 1.1\n                                                },\n                                                className: \"pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                        className: \"text-white text-lg font-medium leading-relaxed mb-4\",\n                                                        children: [\n                                                            '\"',\n                                                            currentQuote.quote,\n                                                            '\"'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-amber-400 font-semibold\",\n                                                                        children: [\n                                                                            \"— \",\n                                                                            currentQuote.author\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-amber-300 text-sm opacity-80\",\n                                                                        children: [\n                                                                            \"Net Worth: \",\n                                                                            currentQuote.netWorth\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-3 py-1 bg-amber-500/20 rounded-full\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-amber-400 text-xs font-medium uppercase tracking-wider\",\n                                                                    children: currentQuote.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-2 -right-2 w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full opacity-20 blur-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, quoteKey, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 1.3\n                                        },\n                                        className: \"pt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-sm text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Enterprise-grade security\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-sm text-gray-400 mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Lightning fast performance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.95\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    delay: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    className: \"border-0 shadow-2xl bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            className: \"space-y-1 pb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                    className: \"text-2xl text-center\",\n                                                    children: \"Welcome to CSM\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                                    className: \"text-center\",\n                                                    children: \"Your success journey starts here\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.Tabs, {\n                                                value: activeTab,\n                                                onValueChange: (value)=>setActiveTab(value),\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsList, {\n                                                        className: \"grid w-full grid-cols-2 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                                                value: \"login\",\n                                                                children: \"Login\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                                                value: \"signup\",\n                                                                children: \"Sign Up\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.AnimatePresence, {\n                                                        mode: \"wait\",\n                                                        children: [\n                                                            activeTab === \"login\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.form, {\n                                                                initial: {\n                                                                    opacity: 0,\n                                                                    x: -20\n                                                                },\n                                                                animate: {\n                                                                    opacity: 1,\n                                                                    x: 0\n                                                                },\n                                                                exit: {\n                                                                    opacity: 0,\n                                                                    x: 20\n                                                                },\n                                                                onSubmit: handleLogin,\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                htmlFor: \"email\",\n                                                                                children: \"Email\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"absolute left-3 top-3 h-4 w-4 text-gray-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 360,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                        id: \"email\",\n                                                                                        type: \"email\",\n                                                                                        placeholder: \"<EMAIL>\",\n                                                                                        value: email,\n                                                                                        onChange: (e)=>setEmail(e.target.value),\n                                                                                        className: \"pl-10\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 361,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 359,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                htmlFor: \"password\",\n                                                                                children: \"Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 374,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        className: \"absolute left-3 top-3 h-4 w-4 text-gray-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 376,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                        id: \"password\",\n                                                                                        type: showPassword ? \"text\" : \"password\",\n                                                                                        placeholder: \"••••••••\",\n                                                                                        value: password,\n                                                                                        onChange: (e)=>setPassword(e.target.value),\n                                                                                        className: \"pl-10 pr-10\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 377,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                                                        className: \"absolute right-3 top-3 text-gray-500 hover:text-gray-700\",\n                                                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                            lineNumber: 391,\n                                                                                            columnNumber: 49\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                            lineNumber: 391,\n                                                                                            columnNumber: 82\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 386,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 375,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"link\",\n                                                                            className: \"text-purple-600 hover:text-purple-700 p-0\",\n                                                                            children: \"Forgot password?\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 396,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        type: \"submit\",\n                                                                        disabled: loading,\n                                                                        className: \"w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white\",\n                                                                        children: [\n                                                                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 414,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Login to CSM\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 flex items-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"w-full border-t\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                    lineNumber: 421,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 420,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative flex justify-center text-xs uppercase\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"bg-white dark:bg-slate-900 px-2 text-muted-foreground\",\n                                                                                    children: \"Or continue with\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                    lineNumber: 424,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 423,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"outline\",\n                                                                        className: \"w-full\",\n                                                                        onClick: handleGoogleLogin,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 436,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Google\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 430,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, \"login-form\", true, {\n                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            activeTab === \"signup\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.form, {\n                                                                initial: {\n                                                                    opacity: 0,\n                                                                    x: 20\n                                                                },\n                                                                animate: {\n                                                                    opacity: 1,\n                                                                    x: 0\n                                                                },\n                                                                exit: {\n                                                                    opacity: 0,\n                                                                    x: -20\n                                                                },\n                                                                onSubmit: handleSignup,\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                htmlFor: \"name\",\n                                                                                children: \"Full Name\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 452,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                        className: \"absolute left-3 top-3 h-4 w-4 text-gray-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 454,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                        id: \"name\",\n                                                                                        type: \"text\",\n                                                                                        placeholder: \"John Doe\",\n                                                                                        value: name,\n                                                                                        onChange: (e)=>setName(e.target.value),\n                                                                                        className: \"pl-10\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 455,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 453,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 451,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                htmlFor: \"signup-email\",\n                                                                                children: \"Email\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 468,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"absolute left-3 top-3 h-4 w-4 text-gray-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 470,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                        id: \"signup-email\",\n                                                                                        type: \"email\",\n                                                                                        placeholder: \"<EMAIL>\",\n                                                                                        value: email,\n                                                                                        onChange: (e)=>setEmail(e.target.value),\n                                                                                        className: \"pl-10\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 471,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 469,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                htmlFor: \"signup-password\",\n                                                                                children: \"Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 484,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        className: \"absolute left-3 top-3 h-4 w-4 text-gray-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 486,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                        id: \"signup-password\",\n                                                                                        type: showPassword ? \"text\" : \"password\",\n                                                                                        placeholder: \"••••••••\",\n                                                                                        value: password,\n                                                                                        onChange: (e)=>setPassword(e.target.value),\n                                                                                        className: \"pl-10 pr-10\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 487,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                                                        className: \"absolute right-3 top-3 text-gray-500 hover:text-gray-700\",\n                                                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                            lineNumber: 501,\n                                                                                            columnNumber: 49\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                            lineNumber: 501,\n                                                                                            columnNumber: 82\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 496,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 483,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                htmlFor: \"confirm-password\",\n                                                                                children: \"Confirm Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 507,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        className: \"absolute left-3 top-3 h-4 w-4 text-gray-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 509,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                        id: \"confirm-password\",\n                                                                                        type: \"password\",\n                                                                                        placeholder: \"••••••••\",\n                                                                                        value: confirmPassword,\n                                                                                        onChange: (e)=>setConfirmPassword(e.target.value),\n                                                                                        className: \"pl-10\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                        lineNumber: 510,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 508,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        type: \"submit\",\n                                                                        disabled: loading,\n                                                                        className: \"w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white\",\n                                                                        children: [\n                                                                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 528,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Create Account\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 flex items-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"w-full border-t\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                    lineNumber: 537,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 536,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative flex justify-center text-xs uppercase\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"bg-white dark:bg-slate-900 px-2 text-muted-foreground\",\n                                                                                    children: \"Or continue with\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                    lineNumber: 540,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 539,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 535,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"outline\",\n                                                                        className: \"w-full\",\n                                                                        onClick: handleGoogleLogin,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Chrome_Eye_EyeOff_Loader2_Lock_Mail_Quote_Rocket_Shield_Sparkles_Target_TrendingUp_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                                lineNumber: 552,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Google\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, \"signup-form\", true, {\n                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardFooter, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-gray-600 w-full\",\n                                                children: \"By continuing, you agree to our Terms of Service and Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\auth\\\\page.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/auth/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _lib_AgentContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/AgentContext */ \"(ssr)/./lib/AgentContext.tsx\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_5__.ClerkProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n            attribute: \"class\",\n            defaultTheme: \"dark\",\n            enableSystem: false,\n            disableTransitionOnChange: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_AgentContext__WEBPACK_IMPORTED_MODULE_3__.AgentProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_4__.ToastContainer, {}, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\providers.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\providers.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\providers.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\providers.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUUrQjtBQUNlO0FBQ29CO0FBQ2Y7QUFDSTtBQUVoRCxTQUFTTSxVQUFVLEVBQUVDLFFBQVEsRUFBaUM7SUFDbkUscUJBQ0UsOERBQUNOLHdEQUFhQTtrQkFDWiw0RUFBQ0Usc0RBQWtCQTtZQUNqQkssV0FBVTtZQUNWQyxjQUFhO1lBQ2JDLGNBQWM7WUFDZEMseUJBQXlCO3NCQUV6Qiw0RUFBQ1AsNERBQWFBOztvQkFDWEc7a0NBQ0QsOERBQUNGLGdFQUFjQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8uL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeD9jNTYyIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IENsZXJrUHJvdmlkZXIgfSBmcm9tIFwiQGNsZXJrL25leHRqc1wiO1xyXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlciB9IGZyb20gXCJuZXh0LXRoZW1lc1wiO1xyXG5pbXBvcnQgeyBBZ2VudFByb3ZpZGVyIH0gZnJvbSBcIkAvbGliL0FnZW50Q29udGV4dFwiO1xyXG5pbXBvcnQgeyBUb2FzdENvbnRhaW5lciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdG9hc3RcIjtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8Q2xlcmtQcm92aWRlcj5cclxuICAgICAgPE5leHRUaGVtZXNQcm92aWRlclxyXG4gICAgICAgIGF0dHJpYnV0ZT1cImNsYXNzXCJcclxuICAgICAgICBkZWZhdWx0VGhlbWU9XCJkYXJrXCJcclxuICAgICAgICBlbmFibGVTeXN0ZW09e2ZhbHNlfVxyXG4gICAgICAgIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2VcclxuICAgICAgPlxyXG4gICAgICAgIDxBZ2VudFByb3ZpZGVyPlxyXG4gICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgICAgPFRvYXN0Q29udGFpbmVyIC8+XHJcbiAgICAgICAgPC9BZ2VudFByb3ZpZGVyPlxyXG4gICAgICA8L05leHRUaGVtZXNQcm92aWRlcj5cclxuICAgIDwvQ2xlcmtQcm92aWRlcj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbIlJlYWN0IiwiQ2xlcmtQcm92aWRlciIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJBZ2VudFByb3ZpZGVyIiwiVG9hc3RDb250YWluZXIiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiIsImF0dHJpYnV0ZSIsImRlZmF1bHRUaGVtZSIsImVuYWJsZVN5c3RlbSIsImRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   ButtonGroup: () => (/* binding */ ButtonGroup),\n/* harmony export */   FloatingActionButton: () => (/* binding */ FloatingActionButton),\n/* harmony export */   IconButton: () => (/* binding */ IconButton),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n// components/ui/button.tsx\n\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90 active:scale-[0.98]\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 active:scale-[0.98]\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground active:scale-[0.98]\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 active:scale-[0.98]\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground active:scale-[0.98]\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            gradient: \"bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg hover:from-purple-700 hover:to-blue-700 active:scale-[0.98]\",\n            success: \"bg-green-600 text-white shadow-sm hover:bg-green-700 active:scale-[0.98]\",\n            warning: \"bg-yellow-600 text-white shadow-sm hover:bg-yellow-700 active:scale-[0.98]\",\n            premium: \"bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 text-black shadow-lg hover:from-yellow-500 hover:via-yellow-600 hover:to-yellow-700 active:scale-[0.98]\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            xl: \"h-12 rounded-md px-10 text-base\",\n            icon: \"h-9 w-9\",\n            \"icon-sm\": \"h-8 w-8\",\n            \"icon-lg\": \"h-10 w-10\"\n        },\n        fullWidth: {\n            true: \"w-full\"\n        },\n        loading: {\n            true: \"cursor-wait\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, fullWidth, asChild = false, loading = false, loadingText, leftIcon, rightIcon, children, disabled, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            fullWidth,\n            loading,\n            className\n        })),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\button.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 13\n                }, undefined),\n                loadingText || children\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex shrink-0\",\n                    children: leftIcon\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\button.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 26\n                }, undefined),\n                children,\n                rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex shrink-0\",\n                    children: rightIcon\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\button.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 27\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 85,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\nconst ButtonGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"horizontal\", children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex\", orientation === \"horizontal\" ? \"flex-row\" : \"flex-col\", \"[&>button]:rounded-none\", orientation === \"horizontal\" ? \"[&>button:first-child]:rounded-l-md [&>button:last-child]:rounded-r-md [&>button:not(:first-child)]:border-l-0\" : \"[&>button:first-child]:rounded-t-md [&>button:last-child]:rounded-b-md [&>button:not(:first-child)]:border-t-0\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 119,\n        columnNumber: 7\n    }, undefined);\n});\nButtonGroup.displayName = \"ButtonGroup\";\nconst IconButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ size = \"icon\", ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n        ref: ref,\n        size: size,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 146,\n        columnNumber: 12\n    }, undefined);\n});\nIconButton.displayName = \"IconButton\";\nconst FloatingActionButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ position = \"bottom-right\", className, ...props }, ref)=>{\n    const positionClasses = {\n        \"bottom-right\": \"bottom-6 right-6\",\n        \"bottom-left\": \"bottom-6 left-6\",\n        \"top-right\": \"top-6 right-6\",\n        \"top-left\": \"top-6 left-6\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed z-50 shadow-lg\", positionClasses[position], className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 166,\n        columnNumber: 7\n    }, undefined);\n});\nFloatingActionButton.displayName = \"FloatingActionButton\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle),\n/* harmony export */   ClientCard: () => (/* binding */ ClientCard),\n/* harmony export */   MetricCard: () => (/* binding */ MetricCard),\n/* harmony export */   cardVariants: () => (/* binding */ cardVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n\n\n\n\n// Enhanced card variants for different use cases in insurance CRM\nconst cardVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"rounded-xl border bg-card text-card-foreground transition-all duration-200\", {\n    variants: {\n        variant: {\n            default: \"shadow-sm hover:shadow-md\",\n            elevated: \"shadow-lg hover:shadow-xl border-0\",\n            interactive: \"cursor-pointer hover:scale-[1.02] hover:shadow-lg active:scale-[0.98]\",\n            success: \"border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/20\",\n            warning: \"border-amber-200 bg-amber-50/50 dark:border-amber-800 dark:bg-amber-950/20\",\n            danger: \"border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/20\",\n            premium: \"border-violet-200 bg-gradient-to-br from-violet-50/50 to-purple-50/50 dark:border-violet-800 dark:from-violet-950/20 dark:to-purple-950/20\"\n        },\n        size: {\n            default: \"p-0\",\n            compact: \"p-0\",\n            spacious: \"p-0\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, loading, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(cardVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props,\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                lineNumber: 47,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n            lineNumber: 46,\n            columnNumber: 9\n        }, undefined) : children\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, separated = false, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", separated && \"border-b\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, as: Component = \"h3\", ...props }, ref)=>{\n    const Comp = Component;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n});\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, noPadding = false, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(!noPadding && \"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 113,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, separated = false, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", separated && \"border-t pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 127,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\nconst MetricCard = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ title, value, change, changeLabel, icon, loading, className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        ref: ref,\n        className: className,\n        loading: loading,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                className: \"pb-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardDescription, {\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined),\n                        icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-muted-foreground\",\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 20\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-bold\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined),\n                    change !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs flex items-center gap-1 mt-1\", change > 0 ? \"text-green-600\" : change < 0 ? \"text-red-600\" : \"text-muted-foreground\"),\n                        children: [\n                            change > 0 && \"↑\",\n                            change < 0 && \"↓\",\n                            Math.abs(change),\n                            \"%\",\n                            changeLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-muted-foreground ml-1\",\n                                children: changeLabel\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, undefined));\nMetricCard.displayName = \"MetricCard\";\nconst ClientCard = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ name, email, phone, policyType, status = \"active\", avatar, onClick, className, ...props }, ref)=>{\n    const statusColors = {\n        active: \"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400\",\n        pending: \"bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400\",\n        expired: \"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        ref: ref,\n        variant: \"interactive\",\n        onClick: onClick,\n        className: className,\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n            className: \"p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary font-semibold\",\n                        children: avatar || name.charAt(0).toUpperCase()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold truncate\",\n                                children: name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, undefined),\n                            email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground truncate\",\n                                children: email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 25\n                            }, undefined),\n                            phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: phone\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mt-2\",\n                                children: [\n                                    policyType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs bg-primary/10 text-primary px-2 py-1 rounded-md\",\n                                        children: policyType\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs px-2 py-1 rounded-md capitalize\", statusColors[status]),\n                                        children: status\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n                lineNumber: 205,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n            lineNumber: 204,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 197,\n        columnNumber: 7\n    }, undefined);\n});\nClientCard.displayName = \"ClientCard\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQStCO0FBRUU7QUFFakMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLDJXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8uL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2RhNzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcblxyXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiO1xyXG5cclxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiaW5wdXRcIj4+KFxyXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8aW5wdXRcclxuICAgICAgICB0eXBlPXt0eXBlfVxyXG4gICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICBcImZsZXggaC05IHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctdHJhbnNwYXJlbnQgcHgtMyBweS0xIHRleHQtYmFzZSBzaGFkb3ctc20gdHJhbnNpdGlvbi1jb2xvcnMgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMSBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MCBtZDp0ZXh0LXNtXCIsXHJcbiAgICAgICAgICBjbGFzc05hbWVcclxuICAgICAgICApfVxyXG4gICAgICAgIHJlZj17cmVmfVxyXG4gICAgICAgIHsuLi5wcm9wc31cclxuICAgICAgLz5cclxuICAgICk7XHJcbiAgfVxyXG4pO1xyXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIjtcclxuXHJcbmV4cG9ydCB7IElucHV0IH07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFK0I7QUFDeUI7QUFDVTtBQUVqQztBQUVqQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnN1cmFuY2UtY3JtLy4vY29tcG9uZW50cy91aS9sYWJlbC50c3g/ODhlZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCI7XHJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCI7XHJcblxyXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiO1xyXG5cclxuY29uc3QgbGFiZWxWYXJpYW50cyA9IGN2YShcclxuICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiXHJcbik7XHJcblxyXG5jb25zdCBMYWJlbCA9IFJlYWN0LmZvcndhcmRSZWY8XHJcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4sXHJcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PiAmXHJcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGxhYmVsVmFyaWFudHM+XHJcbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcclxuICA8TGFiZWxQcmltaXRpdmUuUm9vdFxyXG4gICAgcmVmPXtyZWZ9XHJcbiAgICBjbGFzc05hbWU9e2NuKGxhYmVsVmFyaWFudHMoKSwgY2xhc3NOYW1lKX1cclxuICAgIHsuLi5wcm9wc31cclxuICAvPlxyXG4pKTtcclxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lO1xyXG5cclxuZXhwb3J0IHsgTGFiZWwgfTtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGFiZWxQcmltaXRpdmUiLCJjdmEiLCJjbiIsImxhYmVsVmFyaWFudHMiLCJMYWJlbCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsIlJvb3QiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/tabs.tsx":
/*!********************************!*\
  !*** ./components/ui/tabs.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-tabs */ \"(ssr)/./node_modules/@radix-ui/react-tabs/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tabs,TabsList,TabsTrigger,TabsContent auto */ \n\n\n\n\nconst tabsListVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", {\n    variants: {\n        variant: {\n            default: \"\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Tabs = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_4__.Root;\nconst TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_4__.List, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(tabsListVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 29,\n        columnNumber: 3\n    }, undefined));\nTabsList.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_4__.List.displayName;\nconst TabsTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_4__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 41,\n        columnNumber: 3\n    }, undefined));\nTabsTrigger.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_4__.Trigger.displayName;\nconst TabsContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_4__.Content, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 56,\n        columnNumber: 3\n    }, undefined));\nTabsContent.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_4__.Content.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/tabs.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast-store.ts":
/*!**************************************!*\
  !*** ./components/ui/toast-store.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useToastStore: () => (/* binding */ useToastStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n\nconst useToastStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        toasts: [],\n        addToast: (toast)=>set((state)=>({\n                    toasts: [\n                        ...state.toasts,\n                        {\n                            ...toast,\n                            id: toast.id || Date.now().toString()\n                        }\n                    ]\n                })),\n        removeToast: (id)=>set((state)=>({\n                    toasts: state.toasts.filter((toast)=>toast.id !== id)\n                })),\n        clearToasts: ()=>set({\n                toasts: []\n            })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RvYXN0LXN0b3JlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlDO0FBVTFCLE1BQU1DLGdCQUFnQkQsK0NBQU1BLENBQWEsQ0FBQ0UsTUFBUztRQUN4REMsUUFBUSxFQUFFO1FBQ1ZDLFVBQVUsQ0FBQ0MsUUFDVEgsSUFBSSxDQUFDSSxRQUFXO29CQUNkSCxRQUFROzJCQUFJRyxNQUFNSCxNQUFNO3dCQUFFOzRCQUFFLEdBQUdFLEtBQUs7NEJBQUVFLElBQUlGLE1BQU1FLEVBQUUsSUFBSUMsS0FBS0MsR0FBRyxHQUFHQyxRQUFRO3dCQUFHO3FCQUFFO2dCQUNoRjtRQUNGQyxhQUFhLENBQUNKLEtBQ1pMLElBQUksQ0FBQ0ksUUFBVztvQkFDZEgsUUFBUUcsTUFBTUgsTUFBTSxDQUFDUyxNQUFNLENBQUMsQ0FBQ1AsUUFBVUEsTUFBTUUsRUFBRSxLQUFLQTtnQkFDdEQ7UUFDRk0sYUFBYSxJQUFNWCxJQUFJO2dCQUFFQyxRQUFRLEVBQUU7WUFBQztJQUN0QyxJQUFJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8uL2NvbXBvbmVudHMvdWkvdG9hc3Qtc3RvcmUudHM/OTRlNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJztcclxuaW1wb3J0IHsgVG9hc3QgfSBmcm9tICcuL3RvYXN0JztcclxuXHJcbmludGVyZmFjZSBUb2FzdFN0b3JlIHtcclxuICB0b2FzdHM6IFRvYXN0W107XHJcbiAgYWRkVG9hc3Q6ICh0b2FzdDogVG9hc3QpID0+IHZvaWQ7XHJcbiAgcmVtb3ZlVG9hc3Q6IChpZDogc3RyaW5nKSA9PiB2b2lkO1xyXG4gIGNsZWFyVG9hc3RzOiAoKSA9PiB2b2lkO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgdXNlVG9hc3RTdG9yZSA9IGNyZWF0ZTxUb2FzdFN0b3JlPigoc2V0KSA9PiAoe1xyXG4gIHRvYXN0czogW10sXHJcbiAgYWRkVG9hc3Q6ICh0b2FzdCkgPT5cclxuICAgIHNldCgoc3RhdGUpID0+ICh7XHJcbiAgICAgIHRvYXN0czogWy4uLnN0YXRlLnRvYXN0cywgeyAuLi50b2FzdCwgaWQ6IHRvYXN0LmlkIHx8IERhdGUubm93KCkudG9TdHJpbmcoKSB9XSxcclxuICAgIH0pKSxcclxuICByZW1vdmVUb2FzdDogKGlkKSA9PlxyXG4gICAgc2V0KChzdGF0ZSkgPT4gKHtcclxuICAgICAgdG9hc3RzOiBzdGF0ZS50b2FzdHMuZmlsdGVyKCh0b2FzdCkgPT4gdG9hc3QuaWQgIT09IGlkKSxcclxuICAgIH0pKSxcclxuICBjbGVhclRvYXN0czogKCkgPT4gc2V0KHsgdG9hc3RzOiBbXSB9KSxcclxufSkpOyJdLCJuYW1lcyI6WyJjcmVhdGUiLCJ1c2VUb2FzdFN0b3JlIiwic2V0IiwidG9hc3RzIiwiYWRkVG9hc3QiLCJ0b2FzdCIsInN0YXRlIiwiaWQiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJyZW1vdmVUb2FzdCIsImZpbHRlciIsImNsZWFyVG9hc3RzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast-store.ts\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastContainer: () => (/* binding */ ToastContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,Trophy,X,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _toast_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./toast-store */ \"(ssr)/./components/ui/toast-store.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastContainer auto */ \n\n\n\n\nconst getIcon = (variant)=>{\n    switch(variant){\n        case \"success\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-5 h-5 text-green-500 animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 20,\n                columnNumber: 14\n            }, undefined);\n        case \"error\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-5 h-5 text-red-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 22,\n                columnNumber: 14\n            }, undefined);\n        case \"destructive\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-5 h-5 text-red-500 animate-bounce\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 24,\n                columnNumber: 14\n            }, undefined);\n        case \"info\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-5 h-5 text-blue-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 26,\n                columnNumber: 14\n            }, undefined);\n        case \"winner\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"w-5 h-5 text-yellow-500 animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 28,\n                columnNumber: 14\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"w-5 h-5 text-purple-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 30,\n                columnNumber: 14\n            }, undefined);\n    }\n};\nconst ToastContainer = ()=>{\n    const { toasts, removeToast } = (0,_toast_store__WEBPACK_IMPORTED_MODULE_3__.useToastStore)();\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        const timers = toasts.map((toast)=>{\n            return setTimeout(()=>{\n                removeToast(toast.id);\n            }, 5000);\n        });\n        return ()=>{\n            timers.forEach(clearTimeout);\n        };\n    }, [\n        toasts,\n        removeToast\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 space-y-2 pointer-events-none\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"pointer-events-auto flex items-start gap-4 rounded-md border p-4 shadow-2xl transition-all animate-in slide-in-from-right-5 duration-300\", \"backdrop-blur-sm bg-opacity-95\", toast.variant === \"success\" ? \"bg-green-950/90 border-green-500/50 text-green-50 shadow-green-500/20\" : toast.variant === \"error\" || toast.variant === \"destructive\" ? \"bg-red-950/90 border-red-500/50 text-red-50 shadow-red-500/20\" : toast.variant === \"info\" ? \"bg-blue-950/90 border-blue-500/50 text-blue-50 shadow-blue-500/20\" : toast.variant === \"winner\" ? \"bg-gradient-to-r from-yellow-900/90 to-amber-900/90 border-yellow-500/50 text-yellow-50 shadow-yellow-500/30\" : \"bg-gray-950/90 border-gray-500/50 text-gray-50 shadow-purple-500/20\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: getIcon(toast.variant)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-1 flex-grow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-bold text-sm uppercase tracking-wide\",\n                                    children: toast.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, undefined),\n                            toast.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm opacity-90 leading-tight\",\n                                children: toast.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, undefined),\n                            toast.action\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>removeToast(toast.id),\n                        className: \"flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_Trophy_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, toast.id, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/use-toast.ts":
/*!************************************!*\
  !*** ./components/ui/use-toast.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var _components_ui_toast_store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/components/ui/toast-store */ \"(ssr)/./components/ui/toast-store.ts\");\n// components/ui/use-toast.ts\n\nconst useToast = ()=>{\n    const { addToast } = (0,_components_ui_toast_store__WEBPACK_IMPORTED_MODULE_0__.useToastStore)();\n    return {\n        toast: (props)=>{\n            addToast({\n                ...props\n            });\n        }\n    };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3VzZS10b2FzdC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLDZCQUE2QjtBQUUrQjtBQUU1RCxNQUFNQyxXQUFXO0lBQ2YsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBR0YseUVBQWFBO0lBQ2xDLE9BQU87UUFDTEcsT0FBTyxDQUFDQztZQUNORixTQUFTO2dCQUFFLEdBQUdFLEtBQUs7WUFBQztRQUN0QjtJQUNGO0FBQ0Y7QUFFb0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnN1cmFuY2UtY3JtLy4vY29tcG9uZW50cy91aS91c2UtdG9hc3QudHM/ODA1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBjb21wb25lbnRzL3VpL3VzZS10b2FzdC50c1xyXG5pbXBvcnQgeyBUb2FzdEFjdGlvbkVsZW1lbnQsIHR5cGUgVG9hc3QgfSBmcm9tIFwiLi90b2FzdFwiO1xyXG5pbXBvcnQgeyB1c2VUb2FzdFN0b3JlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90b2FzdC1zdG9yZVwiO1xyXG5cclxuY29uc3QgdXNlVG9hc3QgPSAoKSA9PiB7XHJcbiAgY29uc3QgeyBhZGRUb2FzdCB9ID0gdXNlVG9hc3RTdG9yZSgpO1xyXG4gIHJldHVybiB7XHJcbiAgICB0b2FzdDogKHByb3BzOiBUb2FzdCAmIHsgYWN0aW9uPzogVG9hc3RBY3Rpb25FbGVtZW50IH0pID0+IHtcclxuICAgICAgYWRkVG9hc3QoeyAuLi5wcm9wcyB9KTtcclxuICAgIH0sXHJcbiAgfTtcclxufTtcclxuXHJcbmV4cG9ydCB7IHVzZVRvYXN0IH07XHJcbiJdLCJuYW1lcyI6WyJ1c2VUb2FzdFN0b3JlIiwidXNlVG9hc3QiLCJhZGRUb2FzdCIsInRvYXN0IiwicHJvcHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/AgentContext.tsx":
/*!******************************!*\
  !*** ./lib/AgentContext.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgentProvider: () => (/* binding */ AgentProvider),\n/* harmony export */   useAgent: () => (/* binding */ useAgent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabaseClient */ \"(ssr)/./lib/supabaseClient.ts\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* __next_internal_client_entry_do_not_use__ AgentProvider,useAgent auto */ \n\n\n\n// Context default\nconst AgentContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    agent: null,\n    setAgent: ()=>{}\n});\n// Provider component\nconst AgentProvider = ({ children })=>{\n    const [agent, setAgent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchAgentSettings = async ()=>{\n            const { data, error } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"agent_settings\").select(\"*\").single();\n            if (data && !error) {\n                setAgent(data);\n                if (data.theme) setTheme(data.theme); // Apply preferred theme\n            } else {\n                setTheme(\"dark\"); // Default fallback\n            }\n        };\n        fetchAgentSettings();\n    }, [\n        setTheme\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AgentContext.Provider, {\n        value: {\n            agent,\n            setAgent\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\lib\\\\AgentContext.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook to access agent anywhere\nconst useAgent = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AgentContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/AgentContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/billionaire-quotes.ts":
/*!***********************************!*\
  !*** ./lib/billionaire-quotes.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   billionaireQuotes: () => (/* binding */ billionaireQuotes),\n/* harmony export */   getDailyQuote: () => (/* binding */ getDailyQuote),\n/* harmony export */   getQuotesByCategory: () => (/* binding */ getQuotesByCategory),\n/* harmony export */   getRandomQuote: () => (/* binding */ getRandomQuote)\n/* harmony export */ });\n// Inspiring billionaire quotes about success, money, and achievement\nconst billionaireQuotes = [\n    // Warren Buffett\n    {\n        quote: \"The most important investment you can make is in yourself.\",\n        author: \"Warren Buffett\",\n        netWorth: \"$118 Billion\",\n        category: \"success\"\n    },\n    {\n        quote: \"Rule No.1: Never lose money. Rule No.2: Never forget rule No.1.\",\n        author: \"Warren Buffett\",\n        netWorth: \"$118 Billion\",\n        category: \"money\"\n    },\n    {\n        quote: \"Price is what you pay. Value is what you get.\",\n        author: \"Warren Buffett\",\n        netWorth: \"$118 Billion\",\n        category: \"business\"\n    },\n    // Jeff Bezos\n    {\n        quote: \"I knew that if I failed I wouldn't regret that, but I knew the one thing I might regret is not trying.\",\n        author: \"Jeff Bezos\",\n        netWorth: \"$171 Billion\",\n        category: \"success\"\n    },\n    {\n        quote: \"Your brand is what other people say about you when you're not in the room.\",\n        author: \"Jeff Bezos\",\n        netWorth: \"$171 Billion\",\n        category: \"business\"\n    },\n    // Elon Musk  \n    {\n        quote: \"When something is important enough, you do it even if the odds are not in your favor.\",\n        author: \"Elon Musk\",\n        netWorth: \"$240 Billion\",\n        category: \"mindset\"\n    },\n    {\n        quote: \"The first step is to establish that something is possible; then probability will occur.\",\n        author: \"Elon Musk\",\n        netWorth: \"$240 Billion\",\n        category: \"success\"\n    },\n    {\n        quote: \"Persistence is very important. You should not give up unless you are forced to give up.\",\n        author: \"Elon Musk\",\n        netWorth: \"$240 Billion\",\n        category: \"hustle\"\n    },\n    // Bill Gates\n    {\n        quote: \"Your most unhappy customers are your greatest source of learning.\",\n        author: \"Bill Gates\",\n        netWorth: \"$128 Billion\",\n        category: \"business\"\n    },\n    {\n        quote: \"Success is a lousy teacher. It seduces smart people into thinking they can't lose.\",\n        author: \"Bill Gates\",\n        netWorth: \"$128 Billion\",\n        category: \"mindset\"\n    },\n    // Mark Cuban\n    {\n        quote: \"It doesn't matter how many times you fail. You only have to be right once and then everyone can tell you that you are an overnight success.\",\n        author: \"Mark Cuban\",\n        netWorth: \"$5.7 Billion\",\n        category: \"success\"\n    },\n    {\n        quote: \"Sweat equity is the most valuable equity there is. Know your business and industry better than anyone else in the world.\",\n        author: \"Mark Cuban\",\n        netWorth: \"$5.7 Billion\",\n        category: \"hustle\"\n    },\n    // Larry Page\n    {\n        quote: \"Always deliver more than expected.\",\n        author: \"Larry Page\",\n        netWorth: \"$111 Billion\",\n        category: \"business\"\n    },\n    // Sergey Brin\n    {\n        quote: \"You always hear the phrase, money doesn't buy you happiness. But I always in the back of my mind figured a lot of money will buy you a little bit of happiness.\",\n        author: \"Sergey Brin\",\n        netWorth: \"$107 Billion\",\n        category: \"money\"\n    },\n    // Larry Ellison\n    {\n        quote: \"When you innovate, you've got to be prepared for everyone telling you you're nuts.\",\n        author: \"Larry Ellison\",\n        netWorth: \"$114 Billion\",\n        category: \"mindset\"\n    },\n    // Steve Ballmer\n    {\n        quote: \"The number one benefit of information technology is that it empowers people to do what they want to do.\",\n        author: \"Steve Ballmer\",\n        netWorth: \"$101 Billion\",\n        category: \"business\"\n    },\n    // Michael Bloomberg\n    {\n        quote: \"Don't be afraid to assert yourself, have confidence in your abilities and don't let the bastards get you down.\",\n        author: \"Michael Bloomberg\",\n        netWorth: \"$76 Billion\",\n        category: \"mindset\"\n    },\n    // Charles Koch\n    {\n        quote: \"Success is one of the worst enemies of success, because success tends to breed complacency and lack of humility.\",\n        author: \"Charles Koch\",\n        netWorth: \"$64 Billion\",\n        category: \"success\"\n    },\n    // Mackenzie Scott\n    {\n        quote: \"There are lots of resources each of us can pull from our safes. Time, attention, money, effort. As resources, they're not self-replenishing.\",\n        author: \"MacKenzie Scott\",\n        netWorth: \"$37 Billion\",\n        category: \"money\"\n    },\n    // Ray Dalio\n    {\n        quote: \"He who lives by the crystal ball will eat shattered glass.\",\n        author: \"Ray Dalio\",\n        netWorth: \"$19 Billion\",\n        category: \"business\"\n    },\n    {\n        quote: \"The biggest mistake investors make is to believe that what happened in the recent past is likely to persist in the future.\",\n        author: \"Ray Dalio\",\n        netWorth: \"$19 Billion\",\n        category: \"money\"\n    },\n    // Jack Ma\n    {\n        quote: \"Never give up. Today is hard, tomorrow will be worse, but the day after tomorrow will be sunshine.\",\n        author: \"Jack Ma\",\n        netWorth: \"$25 Billion\",\n        category: \"hustle\"\n    },\n    // Richard Branson  \n    {\n        quote: \"Business opportunities are like buses, there's always another one coming.\",\n        author: \"Richard Branson\",\n        netWorth: \"$4 Billion\",\n        category: \"business\"\n    },\n    // Mark Zuckerberg\n    {\n        quote: \"The biggest risk is not taking any risk. In a world that is changing quickly, the only strategy that is guaranteed to fail is not taking risks.\",\n        author: \"Mark Zuckerberg\",\n        netWorth: \"$106 Billion\",\n        category: \"mindset\"\n    },\n    // Additional powerful quotes\n    {\n        quote: \"The way to get started is to quit talking and begin doing.\",\n        author: \"Walt Disney\",\n        netWorth: \"$5 Billion (estate)\",\n        category: \"hustle\"\n    },\n    {\n        quote: \"Innovation distinguishes between a leader and a follower.\",\n        author: \"Steve Jobs\",\n        netWorth: \"$10 Billion (estate)\",\n        category: \"business\"\n    }\n];\n// Function to get a random quote\nconst getRandomQuote = ()=>{\n    const randomIndex = Math.floor(Math.random() * billionaireQuotes.length);\n    return billionaireQuotes[randomIndex];\n};\n// Function to get quotes by category\nconst getQuotesByCategory = (category)=>{\n    return billionaireQuotes.filter((quote)=>quote.category === category);\n};\n// Function to get a daily quote (changes once per day)\nconst getDailyQuote = ()=>{\n    const today = new Date();\n    const dayOfYear = Math.floor((today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) / 1000 / 60 / 60 / 24);\n    const quoteIndex = dayOfYear % billionaireQuotes.length;\n    return billionaireQuotes[quoteIndex];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/billionaire-quotes.ts\n");

/***/ }),

/***/ "(ssr)/./lib/supabaseClient.ts":
/*!*******************************!*\
  !*** ./lib/supabaseClient.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://ufguunkzmqlkrtlfascw.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVmZ3V1bmt6bXFsa3J0bGZhc2N3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MjE2NzM2NCwiZXhwIjoyMDU3NzQzMzY0fQ.f5ivY0tOZ82p6xyUeR7yoz4rVq41y4wOzBVHVv3lls0\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        persistSession: true,\n        autoRefreshToken: true,\n        detectSessionInUrl: true,\n        storage:  false ? 0 : undefined\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvc3VwYWJhc2VDbGllbnQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUQ7QUFFckQsTUFBTUMsY0FBY0MsMENBQW9DO0FBQ3hELE1BQU1HLGtCQUFrQkgsNk5BQXlDO0FBRTFELE1BQU1LLFdBQVdQLG1FQUFZQSxDQUFDQyxhQUFhSSxpQkFBaUI7SUFDakVHLE1BQU07UUFDSkMsZ0JBQWdCO1FBQ2hCQyxrQkFBa0I7UUFDbEJDLG9CQUFvQjtRQUNwQkMsU0FBUyxNQUFrQixHQUFjQyxDQUFtQixHQUFHRTtJQUNqRTtBQUNGLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnN1cmFuY2UtY3JtLy4vbGliL3N1cGFiYXNlQ2xpZW50LnRzPzNhN2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcclxuXHJcbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMITtcclxuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkhO1xyXG5cclxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXksIHtcclxuICBhdXRoOiB7XHJcbiAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSxcclxuICAgIGF1dG9SZWZyZXNoVG9rZW46IHRydWUsXHJcbiAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUsXHJcbiAgICBzdG9yYWdlOiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IHdpbmRvdy5sb2NhbFN0b3JhZ2UgOiB1bmRlZmluZWQsXHJcbiAgfSxcclxufSk7Il0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwic3VwYWJhc2UiLCJhdXRoIiwicGVyc2lzdFNlc3Npb24iLCJhdXRvUmVmcmVzaFRva2VuIiwiZGV0ZWN0U2Vzc2lvbkluVXJsIiwic3RvcmFnZSIsIndpbmRvdyIsImxvY2FsU3RvcmFnZSIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabaseClient.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBQ0o7QUFFbEMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8uL2xpYi91dGlscy50cz9mNzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCI7XHJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIjtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xyXG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSk7XHJcbn1cclxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1839c07a2566\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnN1cmFuY2UtY3JtLy4vYXBwL2dsb2JhbHMuY3NzPzU3YjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxODM5YzA3YTI1NjZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/auth/page.tsx":
/*!***************************!*\
  !*** ./app/auth/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\crm\insurance-crm\app\auth\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"CSM - Chris Shannahan's Machine\",\n    description: \"A Bad Ass Machine for Winners - The Ultimate Insurance CRM\",\n    keywords: [\n        \"CRM\",\n        \"insurance\",\n        \"Chris Shannahan\",\n        \"sales machine\",\n        \"winners\"\n    ],\n    authors: [\n        {\n            name: \"Chris Shannahan\"\n        }\n    ],\n    openGraph: {\n        title: \"CSM - Chris Shannahan's Machine\",\n        description: \"A Bad Ass Machine for Winners - The Ultimate Insurance CRM\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    src: \"https://assets.calendly.com/assets/external/widget.js\",\n                    async: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} min-h-screen bg-background antialiased`,\n                suppressHydrationWarning: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\crm\insurance-crm\components\providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPWpzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz10c3ghLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL2luc3VyYW5jZS1jcm0vLi9hcHAvZmF2aWNvbi5pY28/YjYwNSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@clerk","vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/tslib","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/snakecase-keys","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/swr","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/use-sync-external-store","vendor-chunks/next-themes","vendor-chunks/dequal","vendor-chunks/@swc","vendor-chunks/zustand","vendor-chunks/clsx","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();