"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/activities/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/activities/page.tsx":
/*!*********************************************!*\
  !*** ./app/(dashboard)/activities/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ActivitiesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabaseClient */ \"(app-pages-browser)/./lib/supabaseClient.ts\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,CheckCircle,Clock,DollarSign,FileText,Mail,MessageSquare,Phone,Search,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,CheckCircle,Clock,DollarSign,FileText,Mail,MessageSquare,Phone,Search,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,CheckCircle,Clock,DollarSign,FileText,Mail,MessageSquare,Phone,Search,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,CheckCircle,Clock,DollarSign,FileText,Mail,MessageSquare,Phone,Search,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,CheckCircle,Clock,DollarSign,FileText,Mail,MessageSquare,Phone,Search,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,CheckCircle,Clock,DollarSign,FileText,Mail,MessageSquare,Phone,Search,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,CheckCircle,Clock,DollarSign,FileText,Mail,MessageSquare,Phone,Search,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,CheckCircle,Clock,DollarSign,FileText,Mail,MessageSquare,Phone,Search,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,CheckCircle,Clock,DollarSign,FileText,Mail,MessageSquare,Phone,Search,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,CheckCircle,Clock,DollarSign,FileText,Mail,MessageSquare,Phone,Search,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,CheckCircle,Clock,DollarSign,FileText,Mail,MessageSquare,Phone,Search,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,CheckCircle,Clock,DollarSign,FileText,Mail,MessageSquare,Phone,Search,TrendingUp,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n// app/(dashboard)/activities/page.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ActivitiesPage() {\n    _s();\n    const { userId } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_10__.usePromisifiedAuth)();\n    const [activities, setActivities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [filteredActivities, setFilteredActivities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedDateRange, setSelectedDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userId) {\n            fetchActivities();\n        }\n    }, [\n        userId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterActivities();\n    }, [\n        activities,\n        searchQuery,\n        selectedType,\n        selectedDateRange\n    ]);\n    const fetchActivities = async ()=>{\n        if (!userId) return;\n        setLoading(true);\n        try {\n            // Fetch activities from database\n            const { data: activitiesData, error } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"activities\").select(\"\\n          *,\\n          leads (name),\\n          clients (name)\\n        \").eq(\"clerk_user_id\", userId).order(\"created_at\", {\n                ascending: false\n            }).limit(100);\n            if (error) {\n                console.error(\"Error fetching activities:\", error);\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to fetch activities\");\n                return;\n            }\n            // Transform data to match interface\n            const transformedActivities = (activitiesData || []).map((activity)=>{\n                var _activity_leads, _activity_clients, _activity_leads1, _activity_clients1;\n                return {\n                    id: activity.id,\n                    type: activity.type || \"note\",\n                    title: activity.title || activity.description || \"Activity\",\n                    description: activity.description,\n                    client: ((_activity_leads = activity.leads) === null || _activity_leads === void 0 ? void 0 : _activity_leads.name) || ((_activity_clients = activity.clients) === null || _activity_clients === void 0 ? void 0 : _activity_clients.name) ? {\n                        id: activity.lead_id || activity.client_id,\n                        name: ((_activity_leads1 = activity.leads) === null || _activity_leads1 === void 0 ? void 0 : _activity_leads1.name) || ((_activity_clients1 = activity.clients) === null || _activity_clients1 === void 0 ? void 0 : _activity_clients1.name)\n                    } : undefined,\n                    user: \"You\",\n                    timestamp: activity.created_at,\n                    metadata: activity.metadata || {}\n                };\n            });\n            setActivities(transformedActivities);\n        } catch (error) {\n            console.error(\"Error fetching activities:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to fetch activities\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterActivities = ()=>{\n        let filtered = [\n            ...activities\n        ];\n        // Filter by search query\n        if (searchQuery) {\n            filtered = filtered.filter((activity)=>{\n                var _activity_description, _activity_client;\n                return activity.title.toLowerCase().includes(searchQuery.toLowerCase()) || ((_activity_description = activity.description) === null || _activity_description === void 0 ? void 0 : _activity_description.toLowerCase().includes(searchQuery.toLowerCase())) || ((_activity_client = activity.client) === null || _activity_client === void 0 ? void 0 : _activity_client.name.toLowerCase().includes(searchQuery.toLowerCase()));\n            });\n        }\n        // Filter by type\n        if (selectedType !== \"all\") {\n            filtered = filtered.filter((activity)=>activity.type === selectedType);\n        }\n        // Filter by date range\n        const now = new Date();\n        let startDate;\n        switch(selectedDateRange){\n            case \"today\":\n                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n                break;\n            case \"week\":\n                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n                break;\n            case \"month\":\n                startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n                break;\n            case \"quarter\":\n                startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);\n                break;\n            default:\n                startDate = new Date(0); // All time\n        }\n        filtered = filtered.filter((activity)=>new Date(activity.timestamp) >= startDate);\n        setFilteredActivities(filtered);\n    };\n    // Smart insights calculations\n    const getMostActiveHour = ()=>{\n        if (activities.length === 0) return \"N/A\";\n        const hourCounts = {};\n        activities.forEach((activity)=>{\n            const hour = new Date(activity.timestamp).getHours();\n            hourCounts[hour] = (hourCounts[hour] || 0) + 1;\n        });\n        const entries = Object.entries(hourCounts);\n        if (entries.length === 0) return \"N/A\";\n        const mostActive = entries.reduce((a, b)=>hourCounts[Number(a[0])] > hourCounts[Number(b[0])] ? a : b);\n        return mostActive ? \"\".concat(mostActive[0], \":00\") : \"N/A\";\n    };\n    const getTopContactMethod = ()=>{\n        if (activities.length === 0) return \"N/A\";\n        const methodCounts = {};\n        activities.forEach((activity)=>{\n            if ([\n                \"call\",\n                \"email\",\n                \"meeting\"\n            ].includes(activity.type)) {\n                methodCounts[activity.type] = (methodCounts[activity.type] || 0) + 1;\n            }\n        });\n        const entries = Object.entries(methodCounts);\n        if (entries.length === 0) return \"N/A\";\n        const topMethod = entries.reduce((a, b)=>methodCounts[a[0]] > methodCounts[b[0]] ? a : b);\n        return topMethod ? topMethod[0] : \"N/A\";\n    };\n    const calculateResponseRate = ()=>{\n        const totalOutbound = activities.filter((a)=>{\n            var _a_description;\n            return [\n                \"call\",\n                \"email\"\n            ].includes(a.type) && ((_a_description = a.description) === null || _a_description === void 0 ? void 0 : _a_description.includes(\"outbound\"));\n        }).length;\n        const totalResponses = activities.filter((a)=>{\n            var _a_description;\n            return a.type === \"call\" && ((_a_description = a.description) === null || _a_description === void 0 ? void 0 : _a_description.includes(\"answered\"));\n        }).length;\n        return totalOutbound > 0 ? (totalResponses / totalOutbound * 100).toFixed(1) : \"0\";\n    };\n    // Calculate real-time stats\n    const calculateStats = ()=>{\n        const today = new Date();\n        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());\n        const startOfWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);\n        const todayActivities = activities.filter((a)=>new Date(a.timestamp) >= startOfDay).length;\n        const weekActivities = activities.filter((a)=>new Date(a.timestamp) >= startOfWeek).length;\n        const clientInteractions = activities.filter((a)=>[\n                \"call\",\n                \"email\",\n                \"meeting\"\n            ].includes(a.type)).length;\n        const completedTasks = activities.filter((a)=>{\n            var _a_description;\n            return a.type === \"task\" && ((_a_description = a.description) === null || _a_description === void 0 ? void 0 : _a_description.includes(\"completed\"));\n        }).length;\n        return {\n            todayActivities,\n            weekActivities,\n            clientInteractions,\n            completedTasks\n        };\n    };\n    const stats = calculateStats();\n    // Get icon for activity type\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case \"call\":\n                return _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n            case \"email\":\n                return _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n            case \"meeting\":\n                return _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"];\n            case \"note\":\n                return _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n            case \"policy\":\n                return _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"];\n            case \"payment\":\n                return _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"];\n            case \"client\":\n                return _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"];\n            case \"task\":\n                return _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"];\n            default:\n                return _barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\n        }\n    };\n    // Get color for activity type (enhanced with dark mode support)\n    const getActivityColor = (type)=>{\n        switch(type){\n            case \"call\":\n                return \"text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400\";\n            case \"email\":\n                return \"text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400\";\n            case \"meeting\":\n                return \"text-purple-600 bg-purple-50 dark:bg-purple-900/20 dark:text-purple-400\";\n            case \"note\":\n                return \"text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400\";\n            case \"policy\":\n                return \"text-orange-600 bg-orange-50 dark:bg-orange-900/20 dark:text-orange-400\";\n            case \"payment\":\n                return \"text-emerald-600 bg-emerald-50 dark:bg-emerald-900/20 dark:text-emerald-400\";\n            case \"client\":\n                return \"text-indigo-600 bg-indigo-50 dark:bg-indigo-900/20 dark:text-indigo-400\";\n            case \"task\":\n                return \"text-teal-600 bg-teal-50 dark:bg-teal-900/20 dark:text-teal-400\";\n            default:\n                return \"text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400\";\n        }\n    };\n    // Export functionality\n    const exportActivities = ()=>{\n        const exportData = {\n            generated: new Date().toISOString(),\n            dateRange: selectedDateRange,\n            activityType: selectedType,\n            totalActivities: filteredActivities.length,\n            insights: {\n                mostActiveHour: getMostActiveHour(),\n                topContactMethod: getTopContactMethod(),\n                responseRate: calculateResponseRate() + \"%\"\n            },\n            activities: filteredActivities.map((activity)=>{\n                var _activity_client;\n                return {\n                    date: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_20__.format)(new Date(activity.timestamp), \"yyyy-MM-dd\"),\n                    time: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_20__.format)(new Date(activity.timestamp), \"HH:mm\"),\n                    type: activity.type,\n                    title: activity.title,\n                    description: activity.description,\n                    client: (_activity_client = activity.client) === null || _activity_client === void 0 ? void 0 : _activity_client.name,\n                    user: activity.user\n                };\n            })\n        };\n        const blob = new Blob([\n            JSON.stringify(exportData, null, 2)\n        ], {\n            type: \"application/json\"\n        });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"activities-\".concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_20__.format)(new Date(), \"yyyy-MM-dd\"), \".json\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n        sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Activities exported successfully!\");\n    };\n    // Group activities by date\n    const groupedActivities = filteredActivities.reduce((groups, activity)=>{\n        const date = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_20__.format)(new Date(activity.timestamp), \"yyyy-MM-dd\");\n        if (!groups[date]) {\n            groups[date] = [];\n        }\n        groups[date].push(activity);\n        return groups;\n    }, {});\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6 max-w-7xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Activity Timeline\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mt-1\",\n                                children: \"Track all actions and events\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        onClick: exportActivities,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            \"Export Activities\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Today's Activities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.todayActivities\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground mt-1\",\n                                        children: [\n                                            stats.todayActivities > 0 ? \"+\" : \"\",\n                                            stats.todayActivities,\n                                            \" from yesterday\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-sm font-medium\",\n                                    children: \"This Week\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.weekActivities\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground mt-1\",\n                                        children: [\n                                            \"Across \",\n                                            activities.length,\n                                            \" total activities\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Client Interactions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.clientInteractions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground mt-1\",\n                                        children: [\n                                            \"Most active: \",\n                                            getMostActiveHour()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Tasks Completed\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stats.completedTasks\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground mt-1\",\n                                        children: [\n                                            \"Response rate: \",\n                                            calculateResponseRate(),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Smart Insights\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"AI-powered activity analysis\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-muted/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Peak Activity Hour\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: getMostActiveHour()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Most productive time\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-muted/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Top Contact Method\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-green-600 capitalize\",\n                                            children: getTopContactMethod()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Primary communication\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-muted/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-4 w-4 text-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Response Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-purple-600\",\n                                            children: [\n                                                calculateResponseRate(),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Client engagement\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        placeholder: \"Search activities...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: selectedType,\n                                onValueChange: setSelectedType,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-[150px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Activities\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"call\",\n                                                children: \"Calls\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"email\",\n                                                children: \"Emails\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"meeting\",\n                                                children: \"Meetings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"policy\",\n                                                children: \"Policies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"payment\",\n                                                children: \"Payments\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"client\",\n                                                children: \"Clients\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"task\",\n                                                children: \"Tasks\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: selectedDateRange,\n                                onValueChange: setSelectedDateRange,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-[150px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"today\",\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"week\",\n                                                children: \"This Week\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"month\",\n                                                children: \"This Month\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Time\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Recent Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"All system and user activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: Object.entries(groupedActivities).map((param)=>{\n                                let [date, activities] = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px bg-border flex-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-muted-foreground\",\n                                                    children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_20__.format)(new Date(date), \"EEEE, MMMM d, yyyy\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-px bg-border flex-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: activities.map((activity)=>{\n                                                const Icon = getActivityIcon(activity.type);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 p-4 rounded-lg border hover:bg-accent/50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"w-12 h-12 rounded-full flex items-center justify-center border-2 border-background shadow-sm\", getActivityColor(activity.type)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-semibold\",\n                                                                                    children: activity.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                                    lineNumber: 497,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                activity.client && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: activity.client.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                                    lineNumber: 499,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_20__.format)(new Date(activity.timestamp), \"h:mm a\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                activity.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                                    children: activity.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-4 text-xs text-muted-foreground\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_CheckCircle_Clock_DollarSign_FileText_Mail_MessageSquare_Phone_Search_TrendingUp_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                                    lineNumber: 517,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                activity.type.charAt(0).toUpperCase() + activity.type.slice(1)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                            lineNumber: 516,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                            lineNumber: 520,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"by \",\n                                                                                activity.user\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, activity.id, true, {\n                                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, date, true, {\n                                    fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n                lineNumber: 463,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\(dashboard)\\\\activities\\\\page.tsx\",\n        lineNumber: 319,\n        columnNumber: 5\n    }, this);\n}\n_s(ActivitiesPage, \"7JkziEazwC192GQ8COf8XCurOWE=\", false, function() {\n    return [\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_10__.usePromisifiedAuth\n    ];\n});\n_c = ActivitiesPage;\nvar _c;\n$RefreshReg$(_c, \"ActivitiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/activities/page.tsx\n"));

/***/ })

});