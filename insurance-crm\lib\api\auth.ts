import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface APIKeyValidation {
  isValid: boolean;
  clerkUserId?: string;
  permissions?: {
    read: boolean;
    write: boolean;
    delete: boolean;
  };
  rateLimit?: number;
  keyId?: string;
}

export interface APIContext {
  userId: string;
  permissions: {
    read: boolean;
    write: boolean;
    delete: boolean;
  };
  keyId: string;
}

/**
 * Validates API key and returns user context
 */
export async function validateAPIKey(apiKey: string): Promise<APIKeyValidation> {
  if (!apiKey || !apiKey.startsWith('crm_')) {
    return { isValid: false };
  }

  try {
    const { data, error } = await supabase.rpc('validate_api_key', {
      key: apiKey
    });

    if (error || !data || data.length === 0) {
      console.error('API key validation error:', error);
      return { isValid: false };
    }

    const result = data[0];
    
    return {
      isValid: result.is_valid,
      clerkUserId: result.clerk_user_id,
      permissions: result.permissions,
      rateLimit: result.rate_limit
    };
  } catch (error) {
    console.error('API key validation failed:', error);
    return { isValid: false };
  }
}

/**
 * Checks if API key has exceeded rate limit
 */
export async function checkRateLimit(keyId: string, limit: number): Promise<boolean> {
  try {
    const { data, error } = await supabase.rpc('check_rate_limit', {
      key_id: keyId,
      limit_per_hour: limit
    });

    if (error) {
      console.error('Rate limit check error:', error);
      return false; // Fail closed - deny if we can't check
    }

    return data;
  } catch (error) {
    console.error('Rate limit check failed:', error);
    return false;
  }
}

/**
 * Logs API usage for analytics and monitoring
 */
export async function logAPIUsage(
  keyId: string,
  endpoint: string,
  method: string,
  statusCode: number = 200,
  responseTime?: number,
  requestSize?: number,
  responseSize?: number,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    await supabase.rpc('log_api_usage', {
      key_id: keyId,
      endpoint_path: endpoint,
      http_method: method,
      status: statusCode,
      response_time: responseTime,
      req_size: requestSize,
      resp_size: responseSize,
      client_ip: ipAddress,
      client_agent: userAgent
    });
  } catch (error) {
    console.error('Failed to log API usage:', error);
    // Don't throw - logging failure shouldn't break the API
  }
}

/**
 * Middleware to authenticate API requests
 */
export async function authenticateAPI(
  request: NextRequest,
  requiredPermission: 'read' | 'write' | 'delete' = 'read'
): Promise<{ success: true; context: APIContext } | { success: false; response: NextResponse }> {
  const startTime = Date.now();
  const apiKey = request.headers.get('x-api-key') || request.headers.get('authorization')?.replace('Bearer ', '');
  
  if (!apiKey) {
    return {
      success: false,
      response: NextResponse.json(
        { error: 'API key required. Include X-API-Key header or Authorization: Bearer <key>' },
        { status: 401 }
      )
    };
  }

  // Validate API key
  const validation = await validateAPIKey(apiKey);
  
  if (!validation.isValid || !validation.clerkUserId) {
    await logAPIUsage(
      'unknown',
      request.nextUrl.pathname,
      request.method,
      401,
      Date.now() - startTime,
      undefined,
      undefined,
      request.ip,
      request.headers.get('user-agent') || undefined
    );
    
    return {
      success: false,
      response: NextResponse.json(
        { error: 'Invalid or expired API key' },
        { status: 401 }
      )
    };
  }

  // Check permissions
  if (!validation.permissions?.[requiredPermission]) {
    return {
      success: false,
      response: NextResponse.json(
        { error: `Insufficient permissions. Required: ${requiredPermission}` },
        { status: 403 }
      )
    };
  }

  // Get API key ID for rate limiting
  const { data: keyData } = await supabase
    .from('api_keys')
    .select('id')
    .eq('api_key', apiKey)
    .single();

  if (!keyData) {
    return {
      success: false,
      response: NextResponse.json(
        { error: 'API key not found' },
        { status: 401 }
      )
    };
  }

  // Check rate limit
  const withinLimit = await checkRateLimit(keyData.id, validation.rateLimit || 1000);
  
  if (!withinLimit) {
    await logAPIUsage(
      keyData.id,
      request.nextUrl.pathname,
      request.method,
      429,
      Date.now() - startTime,
      undefined,
      undefined,
      request.ip,
      request.headers.get('user-agent') || undefined
    );
    
    return {
      success: false,
      response: NextResponse.json(
        { error: 'Rate limit exceeded. Try again later.' },
        { status: 429 }
      )
    };
  }

  return {
    success: true,
    context: {
      userId: validation.clerkUserId,
      permissions: validation.permissions!,
      keyId: keyData.id
    }
  };
}

/**
 * Wrapper function to handle API authentication and logging
 */
export function withAPIAuth(
  handler: (request: NextRequest, context: APIContext) => Promise<NextResponse>,
  requiredPermission: 'read' | 'write' | 'delete' = 'read'
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const startTime = Date.now();
    
    try {
      const auth = await authenticateAPI(request, requiredPermission);
      
      if (!auth.success) {
        return auth.response;
      }

      const response = await handler(request, auth.context);
      
      // Log successful request
      await logAPIUsage(
        auth.context.keyId,
        request.nextUrl.pathname,
        request.method,
        response.status,
        Date.now() - startTime,
        undefined, // Could calculate request size if needed
        undefined, // Could calculate response size if needed
        request.ip,
        request.headers.get('user-agent') || undefined
      );

      return response;
      
    } catch (error) {
      console.error('API handler error:', error);
      
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}
