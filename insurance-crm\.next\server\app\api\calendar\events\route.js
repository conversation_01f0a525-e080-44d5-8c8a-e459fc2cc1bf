/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/calendar/events/route";
exports.ids = ["app/api/calendar/events/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcalendar%2Fevents%2Froute&page=%2Fapi%2Fcalendar%2Fevents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcalendar%2Fevents%2Froute.ts&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcalendar%2Fevents%2Froute&page=%2Fapi%2Fcalendar%2Fevents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcalendar%2Fevents%2Froute.ts&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_crm_insurance_crm_app_api_calendar_events_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/calendar/events/route.ts */ \"(rsc)/./app/api/calendar/events/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/calendar/events/route\",\n        pathname: \"/api/calendar/events\",\n        filename: \"route\",\n        bundlePath: \"app/api/calendar/events/route\"\n    },\n    resolvedPagePath: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\api\\\\calendar\\\\events\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_crm_insurance_crm_app_api_calendar_events_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/calendar/events/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZjYWxlbmRhciUyRmV2ZW50cyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGY2FsZW5kYXIlMkZldmVudHMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZjYWxlbmRhciUyRmV2ZW50cyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDY3JtJTVDaW5zdXJhbmNlLWNybSU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz1qcyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9dHN4JnJvb3REaXI9QyUzQSU1Q2NybSU1Q2luc3VyYW5jZS1jcm0maXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQ1k7QUFDekY7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpRUFBaUU7QUFDekU7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUN1SDs7QUFFdkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnN1cmFuY2UtY3JtLz82ODc3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXGNybVxcXFxpbnN1cmFuY2UtY3JtXFxcXGFwcFxcXFxhcGlcXFxcY2FsZW5kYXJcXFxcZXZlbnRzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9jYWxlbmRhci9ldmVudHMvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9jYWxlbmRhci9ldmVudHNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2NhbGVuZGFyL2V2ZW50cy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXGNybVxcXFxpbnN1cmFuY2UtY3JtXFxcXGFwcFxcXFxhcGlcXFxcY2FsZW5kYXJcXFxcZXZlbnRzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS9jYWxlbmRhci9ldmVudHMvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcalendar%2Fevents%2Froute&page=%2Fapi%2Fcalendar%2Fevents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcalendar%2Fevents%2Froute.ts&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/calendar/events/route.ts":
/*!******************************************!*\
  !*** ./app/api/calendar/events/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! googleapis */ \"(rsc)/./node_modules/googleapis/build/src/index.js\");\n/* harmony import */ var _clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs/server */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/server/auth.js\");\n/* harmony import */ var _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabaseClient */ \"(rsc)/./lib/supabaseClient.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        // Get authenticated user from Clerk\n        const { userId } = await (0,_clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_2__.auth)();\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Get user's Google tokens using Clerk user ID\n        const { data: settings, error: settingsError } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"agent_settings\").select(\"google_calendar_token, google_calendar_refresh_token, google_calendar_token_expiry, clerk_user_id\").eq(\"clerk_user_id\", userId).single();\n        if (settingsError || !settings?.google_calendar_token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Google Calendar not connected\"\n            }, {\n                status: 400\n            });\n        }\n        // Initialize OAuth client\n        const baseUrl = \"http://localhost:3000\" || 0 || 0;\n        const oauth2Client = new googleapis__WEBPACK_IMPORTED_MODULE_3__.google.auth.OAuth2(process.env.GOOGLE_CLIENT_ID, process.env.GOOGLE_CLIENT_SECRET, `${baseUrl}/api/auth/google/callback`);\n        oauth2Client.setCredentials({\n            access_token: settings.google_calendar_token,\n            refresh_token: settings.google_calendar_refresh_token,\n            expiry_date: settings.google_calendar_token_expiry ? new Date(settings.google_calendar_token_expiry).getTime() : null\n        });\n        // Handle token refresh if needed\n        if (settings.google_calendar_token_expiry && new Date(settings.google_calendar_token_expiry) < new Date()) {\n            const { credentials } = await oauth2Client.refreshAccessToken();\n            // Update tokens in database\n            await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"agent_settings\").update({\n                google_calendar_token: credentials.access_token,\n                google_calendar_token_expiry: new Date(credentials.expiry_date).toISOString()\n            }).eq(\"clerk_user_id\", userId);\n            oauth2Client.setCredentials(credentials);\n        }\n        // Get calendar events\n        const calendar = googleapis__WEBPACK_IMPORTED_MODULE_3__.google.calendar({\n            version: \"v3\",\n            auth: oauth2Client\n        });\n        const searchParams = request.nextUrl.searchParams;\n        const timeMin = searchParams.get(\"timeMin\") || new Date().toISOString();\n        const timeMax = searchParams.get(\"timeMax\") || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();\n        const response = await calendar.events.list({\n            calendarId: \"primary\",\n            timeMin,\n            timeMax,\n            singleEvents: true,\n            orderBy: \"startTime\",\n            maxResults: 100\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            events: response.data.items || []\n        });\n    } catch (error) {\n        console.error(\"Error fetching calendar events:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch calendar events\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2NhbGVuZGFyL2V2ZW50cy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF3RDtBQUNwQjtBQUNRO0FBQ0k7QUFFekMsZUFBZUksSUFBSUMsT0FBb0I7SUFDNUMsSUFBSTtRQUNGLG9DQUFvQztRQUNwQyxNQUFNLEVBQUVDLE1BQU0sRUFBRSxHQUFHLE1BQU1KLDBEQUFJQTtRQUU3QixJQUFJLENBQUNJLFFBQVE7WUFDWCxPQUFPTixxREFBWUEsQ0FBQ08sSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQWUsR0FBRztnQkFBRUMsUUFBUTtZQUFJO1FBQ3BFO1FBRUEsK0NBQStDO1FBQy9DLE1BQU0sRUFBRUMsTUFBTUMsUUFBUSxFQUFFSCxPQUFPSSxhQUFhLEVBQUUsR0FBRyxNQUFNVCx5REFBUUEsQ0FDNURVLElBQUksQ0FBQyxrQkFDTEMsTUFBTSxDQUFDLHFHQUNQQyxFQUFFLENBQUMsaUJBQWlCVCxRQUNwQlUsTUFBTTtRQUVULElBQUlKLGlCQUFpQixDQUFDRCxVQUFVTSx1QkFBdUI7WUFDckQsT0FBT2pCLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7Z0JBQUVDLE9BQU87WUFBZ0MsR0FBRztnQkFBRUMsUUFBUTtZQUFJO1FBQ3JGO1FBRUEsMEJBQTBCO1FBQzFCLE1BQU1TLFVBQVVDLHVCQUErQixJQUFJLENBQXdDLElBQUk7UUFDL0YsTUFBTUssZUFBZSxJQUFJdkIsOENBQU1BLENBQUNDLElBQUksQ0FBQ3VCLE1BQU0sQ0FDekNOLFFBQVFDLEdBQUcsQ0FBQ00sZ0JBQWdCLEVBQzVCUCxRQUFRQyxHQUFHLENBQUNPLG9CQUFvQixFQUNoQyxDQUFDLEVBQUVULFFBQVEseUJBQXlCLENBQUM7UUFHdkNNLGFBQWFJLGNBQWMsQ0FBQztZQUMxQkMsY0FBY2xCLFNBQVNNLHFCQUFxQjtZQUM1Q2EsZUFBZW5CLFNBQVNvQiw2QkFBNkI7WUFDckRDLGFBQWFyQixTQUFTc0IsNEJBQTRCLEdBQzlDLElBQUlDLEtBQUt2QixTQUFTc0IsNEJBQTRCLEVBQUVFLE9BQU8sS0FDdkQ7UUFDTjtRQUVBLGlDQUFpQztRQUNqQyxJQUFJeEIsU0FBU3NCLDRCQUE0QixJQUFJLElBQUlDLEtBQUt2QixTQUFTc0IsNEJBQTRCLElBQUksSUFBSUMsUUFBUTtZQUN6RyxNQUFNLEVBQUVFLFdBQVcsRUFBRSxHQUFHLE1BQU1aLGFBQWFhLGtCQUFrQjtZQUU3RCw0QkFBNEI7WUFDNUIsTUFBTWxDLHlEQUFRQSxDQUNYVSxJQUFJLENBQUMsa0JBQ0x5QixNQUFNLENBQUM7Z0JBQ05yQix1QkFBdUJtQixZQUFZUCxZQUFZO2dCQUMvQ0ksOEJBQThCLElBQUlDLEtBQUtFLFlBQVlKLFdBQVcsRUFBR08sV0FBVztZQUM5RSxHQUNDeEIsRUFBRSxDQUFDLGlCQUFpQlQ7WUFFdkJrQixhQUFhSSxjQUFjLENBQUNRO1FBQzlCO1FBRUEsc0JBQXNCO1FBQ3RCLE1BQU1JLFdBQVd2Qyw4Q0FBTUEsQ0FBQ3VDLFFBQVEsQ0FBQztZQUFFQyxTQUFTO1lBQU12QyxNQUFNc0I7UUFBYTtRQUVyRSxNQUFNa0IsZUFBZXJDLFFBQVFzQyxPQUFPLENBQUNELFlBQVk7UUFDakQsTUFBTUUsVUFBVUYsYUFBYW5CLEdBQUcsQ0FBQyxjQUFjLElBQUlXLE9BQU9LLFdBQVc7UUFDckUsTUFBTU0sVUFBVUgsYUFBYW5CLEdBQUcsQ0FBQyxjQUFjLElBQUlXLEtBQUtBLEtBQUtZLEdBQUcsS0FBSyxLQUFLLEtBQUssS0FBSyxLQUFLLE1BQU1QLFdBQVc7UUFFMUcsTUFBTVEsV0FBVyxNQUFNUCxTQUFTUSxNQUFNLENBQUNDLElBQUksQ0FBQztZQUMxQ0MsWUFBWTtZQUNaTjtZQUNBQztZQUNBTSxjQUFjO1lBQ2RDLFNBQVM7WUFDVEMsWUFBWTtRQUNkO1FBRUEsT0FBT3JELHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7WUFBRXlDLFFBQVFELFNBQVNyQyxJQUFJLENBQUM0QyxLQUFLLElBQUksRUFBRTtRQUFDO0lBQy9ELEVBQUUsT0FBTzlDLE9BQU87UUFDZCtDLFFBQVEvQyxLQUFLLENBQUMsbUNBQW1DQTtRQUNqRCxPQUFPUixxREFBWUEsQ0FBQ08sSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQWtDLEdBQzNDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5zdXJhbmNlLWNybS8uL2FwcC9hcGkvY2FsZW5kYXIvZXZlbnRzL3JvdXRlLnRzP2ZkZGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L3NlcnZlclwiO1xyXG5pbXBvcnQgeyBnb29nbGUgfSBmcm9tIFwiZ29vZ2xlYXBpc1wiO1xyXG5pbXBvcnQgeyBhdXRoIH0gZnJvbSBcIkBjbGVyay9uZXh0anMvc2VydmVyXCI7XHJcbmltcG9ydCB7IHN1cGFiYXNlIH0gZnJvbSBcIkAvbGliL3N1cGFiYXNlQ2xpZW50XCI7XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XHJcbiAgdHJ5IHtcclxuICAgIC8vIEdldCBhdXRoZW50aWNhdGVkIHVzZXIgZnJvbSBDbGVya1xyXG4gICAgY29uc3QgeyB1c2VySWQgfSA9IGF3YWl0IGF1dGgoKTtcclxuICAgIFxyXG4gICAgaWYgKCF1c2VySWQpIHtcclxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6IFwiVW5hdXRob3JpemVkXCIgfSwgeyBzdGF0dXM6IDQwMSB9KTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBHZXQgdXNlcidzIEdvb2dsZSB0b2tlbnMgdXNpbmcgQ2xlcmsgdXNlciBJRFxyXG4gICAgY29uc3QgeyBkYXRhOiBzZXR0aW5ncywgZXJyb3I6IHNldHRpbmdzRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwiYWdlbnRfc2V0dGluZ3NcIilcclxuICAgICAgLnNlbGVjdChcImdvb2dsZV9jYWxlbmRhcl90b2tlbiwgZ29vZ2xlX2NhbGVuZGFyX3JlZnJlc2hfdG9rZW4sIGdvb2dsZV9jYWxlbmRhcl90b2tlbl9leHBpcnksIGNsZXJrX3VzZXJfaWRcIilcclxuICAgICAgLmVxKFwiY2xlcmtfdXNlcl9pZFwiLCB1c2VySWQpXHJcbiAgICAgIC5zaW5nbGUoKTtcclxuXHJcbiAgICBpZiAoc2V0dGluZ3NFcnJvciB8fCAhc2V0dGluZ3M/Lmdvb2dsZV9jYWxlbmRhcl90b2tlbikge1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogXCJHb29nbGUgQ2FsZW5kYXIgbm90IGNvbm5lY3RlZFwiIH0sIHsgc3RhdHVzOiA0MDAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gSW5pdGlhbGl6ZSBPQXV0aCBjbGllbnRcclxuICAgIGNvbnN0IGJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUFBfVVJMIHx8IGBodHRwczovLyR7cmVxdWVzdC5oZWFkZXJzLmdldCgnaG9zdCcpfWAgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMCc7XHJcbiAgICBjb25zdCBvYXV0aDJDbGllbnQgPSBuZXcgZ29vZ2xlLmF1dGguT0F1dGgyKFxyXG4gICAgICBwcm9jZXNzLmVudi5HT09HTEVfQ0xJRU5UX0lELFxyXG4gICAgICBwcm9jZXNzLmVudi5HT09HTEVfQ0xJRU5UX1NFQ1JFVCxcclxuICAgICAgYCR7YmFzZVVybH0vYXBpL2F1dGgvZ29vZ2xlL2NhbGxiYWNrYFxyXG4gICAgKTtcclxuXHJcbiAgICBvYXV0aDJDbGllbnQuc2V0Q3JlZGVudGlhbHMoe1xyXG4gICAgICBhY2Nlc3NfdG9rZW46IHNldHRpbmdzLmdvb2dsZV9jYWxlbmRhcl90b2tlbixcclxuICAgICAgcmVmcmVzaF90b2tlbjogc2V0dGluZ3MuZ29vZ2xlX2NhbGVuZGFyX3JlZnJlc2hfdG9rZW4sXHJcbiAgICAgIGV4cGlyeV9kYXRlOiBzZXR0aW5ncy5nb29nbGVfY2FsZW5kYXJfdG9rZW5fZXhwaXJ5IFxyXG4gICAgICAgID8gbmV3IERhdGUoc2V0dGluZ3MuZ29vZ2xlX2NhbGVuZGFyX3Rva2VuX2V4cGlyeSkuZ2V0VGltZSgpIFxyXG4gICAgICAgIDogbnVsbCxcclxuICAgIH0pO1xyXG5cclxuICAgIC8vIEhhbmRsZSB0b2tlbiByZWZyZXNoIGlmIG5lZWRlZFxyXG4gICAgaWYgKHNldHRpbmdzLmdvb2dsZV9jYWxlbmRhcl90b2tlbl9leHBpcnkgJiYgbmV3IERhdGUoc2V0dGluZ3MuZ29vZ2xlX2NhbGVuZGFyX3Rva2VuX2V4cGlyeSkgPCBuZXcgRGF0ZSgpKSB7XHJcbiAgICAgIGNvbnN0IHsgY3JlZGVudGlhbHMgfSA9IGF3YWl0IG9hdXRoMkNsaWVudC5yZWZyZXNoQWNjZXNzVG9rZW4oKTtcclxuICAgICAgXHJcbiAgICAgIC8vIFVwZGF0ZSB0b2tlbnMgaW4gZGF0YWJhc2VcclxuICAgICAgYXdhaXQgc3VwYWJhc2VcclxuICAgICAgICAuZnJvbShcImFnZW50X3NldHRpbmdzXCIpXHJcbiAgICAgICAgLnVwZGF0ZSh7XHJcbiAgICAgICAgICBnb29nbGVfY2FsZW5kYXJfdG9rZW46IGNyZWRlbnRpYWxzLmFjY2Vzc190b2tlbixcclxuICAgICAgICAgIGdvb2dsZV9jYWxlbmRhcl90b2tlbl9leHBpcnk6IG5ldyBEYXRlKGNyZWRlbnRpYWxzLmV4cGlyeV9kYXRlISkudG9JU09TdHJpbmcoKSxcclxuICAgICAgICB9KVxyXG4gICAgICAgIC5lcShcImNsZXJrX3VzZXJfaWRcIiwgdXNlcklkKTtcclxuXHJcbiAgICAgIG9hdXRoMkNsaWVudC5zZXRDcmVkZW50aWFscyhjcmVkZW50aWFscyk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gR2V0IGNhbGVuZGFyIGV2ZW50c1xyXG4gICAgY29uc3QgY2FsZW5kYXIgPSBnb29nbGUuY2FsZW5kYXIoeyB2ZXJzaW9uOiBcInYzXCIsIGF1dGg6IG9hdXRoMkNsaWVudCB9KTtcclxuICAgIFxyXG4gICAgY29uc3Qgc2VhcmNoUGFyYW1zID0gcmVxdWVzdC5uZXh0VXJsLnNlYXJjaFBhcmFtcztcclxuICAgIGNvbnN0IHRpbWVNaW4gPSBzZWFyY2hQYXJhbXMuZ2V0KFwidGltZU1pblwiKSB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCk7XHJcbiAgICBjb25zdCB0aW1lTWF4ID0gc2VhcmNoUGFyYW1zLmdldChcInRpbWVNYXhcIikgfHwgbmV3IERhdGUoRGF0ZS5ub3coKSArIDMwICogMjQgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKTtcclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNhbGVuZGFyLmV2ZW50cy5saXN0KHtcclxuICAgICAgY2FsZW5kYXJJZDogXCJwcmltYXJ5XCIsXHJcbiAgICAgIHRpbWVNaW4sXHJcbiAgICAgIHRpbWVNYXgsXHJcbiAgICAgIHNpbmdsZUV2ZW50czogdHJ1ZSxcclxuICAgICAgb3JkZXJCeTogXCJzdGFydFRpbWVcIixcclxuICAgICAgbWF4UmVzdWx0czogMTAwLFxyXG4gICAgfSk7XHJcblxyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXZlbnRzOiByZXNwb25zZS5kYXRhLml0ZW1zIHx8IFtdIH0pO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgY2FsZW5kYXIgZXZlbnRzOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXHJcbiAgICAgIHsgZXJyb3I6IFwiRmFpbGVkIHRvIGZldGNoIGNhbGVuZGFyIGV2ZW50c1wiIH0sXHJcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxyXG4gICAgKTtcclxuICB9XHJcbn0iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiZ29vZ2xlIiwiYXV0aCIsInN1cGFiYXNlIiwiR0VUIiwicmVxdWVzdCIsInVzZXJJZCIsImpzb24iLCJlcnJvciIsInN0YXR1cyIsImRhdGEiLCJzZXR0aW5ncyIsInNldHRpbmdzRXJyb3IiLCJmcm9tIiwic2VsZWN0IiwiZXEiLCJzaW5nbGUiLCJnb29nbGVfY2FsZW5kYXJfdG9rZW4iLCJiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQUF9VUkwiLCJoZWFkZXJzIiwiZ2V0Iiwib2F1dGgyQ2xpZW50IiwiT0F1dGgyIiwiR09PR0xFX0NMSUVOVF9JRCIsIkdPT0dMRV9DTElFTlRfU0VDUkVUIiwic2V0Q3JlZGVudGlhbHMiLCJhY2Nlc3NfdG9rZW4iLCJyZWZyZXNoX3Rva2VuIiwiZ29vZ2xlX2NhbGVuZGFyX3JlZnJlc2hfdG9rZW4iLCJleHBpcnlfZGF0ZSIsImdvb2dsZV9jYWxlbmRhcl90b2tlbl9leHBpcnkiLCJEYXRlIiwiZ2V0VGltZSIsImNyZWRlbnRpYWxzIiwicmVmcmVzaEFjY2Vzc1Rva2VuIiwidXBkYXRlIiwidG9JU09TdHJpbmciLCJjYWxlbmRhciIsInZlcnNpb24iLCJzZWFyY2hQYXJhbXMiLCJuZXh0VXJsIiwidGltZU1pbiIsInRpbWVNYXgiLCJub3ciLCJyZXNwb25zZSIsImV2ZW50cyIsImxpc3QiLCJjYWxlbmRhcklkIiwic2luZ2xlRXZlbnRzIiwib3JkZXJCeSIsIm1heFJlc3VsdHMiLCJpdGVtcyIsImNvbnNvbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/calendar/events/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabaseClient.ts":
/*!*******************************!*\
  !*** ./lib/supabaseClient.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://ufguunkzmqlkrtlfascw.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVmZ3V1bmt6bXFsa3J0bGZhc2N3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MjE2NzM2NCwiZXhwIjoyMDU3NzQzMzY0fQ.f5ivY0tOZ82p6xyUeR7yoz4rVq41y4wOzBVHVv3lls0\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        persistSession: true,\n        autoRefreshToken: true,\n        detectSessionInUrl: true,\n        storage:  false ? 0 : undefined\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvc3VwYWJhc2VDbGllbnQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUQ7QUFFckQsTUFBTUMsY0FBY0MsMENBQW9DO0FBQ3hELE1BQU1HLGtCQUFrQkgsNk5BQXlDO0FBRTFELE1BQU1LLFdBQVdQLG1FQUFZQSxDQUFDQyxhQUFhSSxpQkFBaUI7SUFDakVHLE1BQU07UUFDSkMsZ0JBQWdCO1FBQ2hCQyxrQkFBa0I7UUFDbEJDLG9CQUFvQjtRQUNwQkMsU0FBUyxNQUFrQixHQUFjQyxDQUFtQixHQUFHRTtJQUNqRTtBQUNGLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnN1cmFuY2UtY3JtLy4vbGliL3N1cGFiYXNlQ2xpZW50LnRzPzNhN2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcclxuXHJcbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMITtcclxuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkhO1xyXG5cclxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXksIHtcclxuICBhdXRoOiB7XHJcbiAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSxcclxuICAgIGF1dG9SZWZyZXNoVG9rZW46IHRydWUsXHJcbiAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUsXHJcbiAgICBzdG9yYWdlOiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IHdpbmRvdy5sb2NhbFN0b3JhZ2UgOiB1bmRlZmluZWQsXHJcbiAgfSxcclxufSk7Il0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwic3VwYWJhc2UiLCJhdXRoIiwicGVyc2lzdFNlc3Npb24iLCJhdXRvUmVmcmVzaFRva2VuIiwiZGV0ZWN0U2Vzc2lvbkluVXJsIiwic3RvcmFnZSIsIndpbmRvdyIsImxvY2FsU3RvcmFnZSIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabaseClient.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@clerk","vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/tslib","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/snakecase-keys","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/googleapis","vendor-chunks/google-auth-library","vendor-chunks/googleapis-common","vendor-chunks/gaxios","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/jws","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/json-bigint","vendor-chunks/google-logging-utils","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/https-proxy-agent","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/gcp-metadata","vendor-chunks/function-bind","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/agent-base","vendor-chunks/url-template","vendor-chunks/supports-color","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/jwa","vendor-chunks/is-stream","vendor-chunks/hasown","vendor-chunks/has-flag","vendor-chunks/gtoken","vendor-chunks/get-intrinsic","vendor-chunks/extend","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound","vendor-chunks/buffer-equal-constant-time","vendor-chunks/bignumber.js","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcalendar%2Fevents%2Froute&page=%2Fapi%2Fcalendar%2Fevents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcalendar%2Fevents%2Froute.ts&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();