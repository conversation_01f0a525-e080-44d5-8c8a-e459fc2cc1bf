/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/commissions/route";
exports.ids = ["app/api/commissions/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcommissions%2Froute&page=%2Fapi%2Fcommissions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcommissions%2Froute.ts&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcommissions%2Froute&page=%2Fapi%2Fcommissions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcommissions%2Froute.ts&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_crm_insurance_crm_app_api_commissions_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/commissions/route.ts */ \"(rsc)/./app/api/commissions/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/commissions/route\",\n        pathname: \"/api/commissions\",\n        filename: \"route\",\n        bundlePath: \"app/api/commissions/route\"\n    },\n    resolvedPagePath: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\api\\\\commissions\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_crm_insurance_crm_app_api_commissions_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/commissions/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcommissions%2Froute&page=%2Fapi%2Fcommissions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcommissions%2Froute.ts&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/commissions/route.ts":
/*!**************************************!*\
  !*** ./app/api/commissions/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs/server */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/server/auth.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n\n// Create a service client that bypasses RLS\nconst supabaseServiceClient = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://ufguunkzmqlkrtlfascw.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nasync function GET(request) {\n    try {\n        const { userId } = await (0,_clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_2__.auth)();\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Get URL parameters for date filtering\n        const { searchParams } = new URL(request.url);\n        const startDate = searchParams.get(\"start_date\");\n        const endDate = searchParams.get(\"end_date\");\n        // Build the query\n        let query = supabaseServiceClient.from(\"active_clients\").select(`\r\n        id,\r\n        clerk_user_id,\r\n        first_name,\r\n        last_name,\r\n        carrier,\r\n        premium,\r\n        monthly_commission,\r\n        annual_commission,\r\n        state,\r\n        created_at\r\n      `).eq(\"clerk_user_id\", userId);\n        // Add date filters if provided\n        if (startDate) {\n            query = query.gte(\"created_at\", startDate);\n        }\n        if (endDate) {\n            query = query.lte(\"created_at\", endDate);\n        }\n        const { data: clients, error: clientsError } = await query;\n        if (clientsError) {\n            console.error(\"Error fetching clients:\", clientsError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch commission data\"\n            }, {\n                status: 500\n            });\n        }\n        // Also fetch policies data\n        let policiesQuery = supabaseServiceClient.from(\"policies\").select(`\r\n        id,\r\n        clerk_user_id,\r\n        client_id,\r\n        plan_name,\r\n        carrier,\r\n        premium,\r\n        monthly_commission,\r\n        annual_commission,\r\n        state,\r\n        created_at\r\n      `).eq(\"clerk_user_id\", userId);\n        if (startDate) {\n            policiesQuery = policiesQuery.gte(\"created_at\", startDate);\n        }\n        if (endDate) {\n            policiesQuery = policiesQuery.lte(\"created_at\", endDate);\n        }\n        const { data: policies, error: policiesError } = await policiesQuery;\n        if (policiesError) {\n            console.error(\"Error fetching policies:\", policiesError);\n        // Don't fail the request, just log the error\n        }\n        // Calculate commission summaries\n        const carrierCommissions = (clients || []).reduce((acc, client)=>{\n            const carrier = client.carrier || \"Unknown\";\n            if (!acc[carrier]) {\n                acc[carrier] = {\n                    carrier,\n                    clientCount: 0,\n                    totalMonthly: 0,\n                    totalAnnual: 0,\n                    clients: []\n                };\n            }\n            acc[carrier].clientCount++;\n            acc[carrier].totalMonthly += client.monthly_commission || 0;\n            acc[carrier].totalAnnual += client.annual_commission || 0;\n            acc[carrier].clients.push(client);\n            return acc;\n        }, {});\n        const stateCommissions = (clients || []).reduce((acc, client)=>{\n            const state = client.state || \"Unknown\";\n            if (!acc[state]) {\n                acc[state] = {\n                    state,\n                    clientCount: 0,\n                    totalMonthly: 0,\n                    totalAnnual: 0,\n                    avgCommission: 0\n                };\n            }\n            acc[state].clientCount++;\n            acc[state].totalMonthly += client.monthly_commission || 0;\n            acc[state].totalAnnual += client.annual_commission || 0;\n            return acc;\n        }, {});\n        // Calculate averages for states\n        Object.values(stateCommissions).forEach((state)=>{\n            state.avgCommission = state.clientCount > 0 ? state.totalMonthly / state.clientCount : 0;\n        });\n        console.log(`API: Found ${clients?.length || 0} clients with commission data for user ${userId}`);\n        console.log(`API: Found ${policies?.length || 0} policies for user ${userId}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            clients: clients || [],\n            policies: policies || [],\n            summaries: {\n                byCarrier: Object.values(carrierCommissions),\n                byState: Object.values(stateCommissions),\n                totals: {\n                    totalMonthly: (clients || []).reduce((sum, c)=>sum + (c.monthly_commission || 0), 0),\n                    totalAnnual: (clients || []).reduce((sum, c)=>sum + (c.annual_commission || 0), 0),\n                    clientCount: clients?.length || 0\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"Error in commissions API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/commissions/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@clerk","vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/tslib","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/snakecase-keys","vendor-chunks/snake-case","vendor-chunks/dot-case"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcommissions%2Froute&page=%2Fapi%2Fcommissions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcommissions%2Froute.ts&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();