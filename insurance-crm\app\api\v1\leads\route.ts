import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { withAPIAuth, APIContext } from '@/lib/api/auth';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * GET /api/v1/leads
 * Retrieve all leads for the authenticated user
 */
async function handleGET(request: NextRequest, context: APIContext): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status');
    const source = searchParams.get('source');
    const stage = searchParams.get('stage');
    const search = searchParams.get('search');

    // Build query
    let query = supabase
      .from('leads')
      .select(`
        id,
        full_name,
        first_name,
        last_name,
        email,
        phone,
        dob,
        birth_date,
        gender,
        address,
        city,
        state,
        zip,
        status,
        stage,
        source,
        estimated_value,
        priority,
        appointment_date,
        last_contact_date,
        next_follow_up,
        notes,
        created_at,
        updated_at,
        activities (
          id,
          type,
          title,
          description,
          created_at
        )
      `)
      .eq('clerk_user_id', context.userId)
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (source) {
      query = query.eq('source', source);
    }
    if (stage) {
      query = query.eq('stage', stage);
    }
    if (search) {
      query = query.or(`full_name.ilike.%${search}%,first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%,phone.like.%${search.replace(/\D/g, '')}%`);
    }

    const { data: leads, error } = await query;

    if (error) {
      console.error('Error fetching leads:', error);
      return NextResponse.json(
        { error: 'Failed to fetch leads', details: error.message },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('leads')
      .select('*', { count: 'exact', head: true })
      .eq('clerk_user_id', context.userId);

    if (status) countQuery = countQuery.eq('status', status);
    if (source) countQuery = countQuery.eq('source', source);
    if (stage) countQuery = countQuery.eq('stage', stage);
    if (search) {
      countQuery = countQuery.or(`full_name.ilike.%${search}%,first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%,phone.like.%${search.replace(/\D/g, '')}%`);
    }

    const { count } = await countQuery;

    return NextResponse.json({
      success: true,
      data: leads,
      pagination: {
        limit,
        offset,
        total: count || 0,
        hasMore: (offset + limit) < (count || 0)
      },
      filters: {
        status,
        source,
        stage,
        search
      }
    });

  } catch (error) {
    console.error('Leads API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/leads
 * Create a new lead
 */
async function handlePOST(request: NextRequest, context: APIContext): Promise<NextResponse> {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['full_name'];
    const missingFields = requiredFields.filter(field => !body[field]);
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Prepare lead data
    const leadData = {
      clerk_user_id: context.userId,
      full_name: body.full_name,
      first_name: body.first_name,
      last_name: body.last_name,
      email: body.email,
      phone: body.phone,
      dob: body.dob,
      birth_date: body.birth_date,
      gender: body.gender,
      address: body.address,
      city: body.city,
      state: body.state,
      zip: body.zip,
      status: body.status || 'New Lead',
      stage: body.stage || 'new_lead',
      source: body.source || 'API',
      estimated_value: body.estimated_value,
      priority: body.priority || 'medium',
      appointment_date: body.appointment_date,
      last_contact_date: body.last_contact_date,
      next_follow_up: body.next_follow_up,
      notes: body.notes
    };

    const { data: lead, error } = await supabase
      .from('leads')
      .insert(leadData)
      .select()
      .single();

    if (error) {
      console.error('Error creating lead:', error);
      return NextResponse.json(
        { error: 'Failed to create lead', details: error.message },
        { status: 500 }
      );
    }

    // Create initial activity
    if (body.create_activity !== false) {
      await supabase
        .from('activities')
        .insert({
          clerk_user_id: context.userId,
          lead_id: lead.id,
          type: 'note',
          title: 'Lead Created via API',
          description: `New lead created: ${lead.full_name}`,
        });
    }

    return NextResponse.json({
      success: true,
      data: lead,
      message: 'Lead created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create lead API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Export the wrapped handlers
export const GET = withAPIAuth(handleGET, 'read');
export const POST = withAPIAuth(handlePOST, 'write');
