// Simple commission fix script
const { createClient } = require('@supabase/supabase-js');

// You'll need to replace these with your actual values
const SUPABASE_URL = 'https://your-project.supabase.co';
const SUPABASE_SERVICE_KEY = 'your-service-role-key';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Commission rates (simplified)
const COMMISSION_RATES = [
  { carrier: 'Aetna', state: 'FL', new: 25, renewal: 10 },
  { carrier: 'Cigna', state: 'FL', new: 25, renewal: 10 },
  { carrier: 'Oscar', state: 'FL', new: 25, renewal: 10 },
  { carrier: 'UHC', state: 'FL', new: 25, renewal: 10 },
  { carrier: 'United Healthcare', state: 'FL', new: 25, renewal: 10 },
  { carrier: 'Ambetter', state: 'FL', new: 25, renewal: 10 },
];

function getCommissionRate(carrier, state, isRenewal = false) {
  if (!carrier || !state) return 20;
  
  const rate = COMMISSION_RATES.find(
    r => r.carrier.toLowerCase() === carrier.toLowerCase() && 
        r.state.toUpperCase() === state.toUpperCase()
  );
  
  return rate ? (isRenewal ? rate.renewal : rate.new) : 20;
}

function calculateMonthlyCommission(carrier, state, memberCount, isRenewal = false, grossPremium) {
  const rate = getCommissionRate(carrier, state, isRenewal);
  
  // If rate is a percentage, multiply by gross premium
  if (rate < 1 && rate > 0 && grossPremium) {
    return grossPremium * rate;
  }
  
  // Otherwise, it's per member per month
  return rate * (memberCount || 1);
}

async function fixCommissions() {
  console.log('🔄 Starting commission fix...');
  
  try {
    // First, let's just update a few test policies
    const { data: policies, error } = await supabase
      .from('policies')
      .select('*')
      .limit(5);
      
    if (error) {
      console.error('Error:', error);
      return;
    }
    
    console.log(`Found ${policies.length} policies`);
    
    for (const policy of policies) {
      const monthlyCommission = calculateMonthlyCommission(
        policy.carrier || 'Aetna',
        policy.state || 'FL',
        policy.member_count || 1,
        policy.is_renewal || false,
        policy.gross_premium || policy.premium || 500
      );
      
      console.log(`Policy ${policy.id}: $${monthlyCommission}/month`);
      
      const { error: updateError } = await supabase
        .from('policies')
        .update({
          monthly_commission: monthlyCommission,
          annual_commission: monthlyCommission * 12
        })
        .eq('id', policy.id);
        
      if (updateError) {
        console.error('Update error:', updateError);
      }
    }
    
    console.log('✅ Commission fix complete!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run if called directly
if (require.main === module) {
  fixCommissions();
}

module.exports = { fixCommissions };
