-- API System Database Schema
-- Run this in Supabase SQL Editor to set up API key management

-- Create API keys table
CREATE TABLE IF NOT EXISTS api_keys (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    clerk_user_id TEXT NOT NULL,
    key_name TEXT NOT NULL,
    api_key TEXT NOT NULL UNIQUE,
    permissions JSONB DEFAULT '{"read": true, "write": false, "delete": false}'::jsonb,
    rate_limit INTEGER DEFAULT 1000, -- requests per hour
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create API usage tracking table
CREATE TABLE IF NOT EXISTS api_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    api_key_id UUID REFERENCES api_keys(id) ON DELETE CASCADE,
    endpoint TEXT NOT NULL,
    method TEXT NOT NULL,
    status_code INTEGER,
    response_time_ms INTEGER,
    request_size_bytes INTEGER,
    response_size_bytes INTEGER,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_api_keys_clerk_user_id ON api_keys(clerk_user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_api_key ON api_keys(api_key);
CREATE INDEX IF NOT EXISTS idx_api_keys_active ON api_keys(is_active);
CREATE INDEX IF NOT EXISTS idx_api_usage_api_key_id ON api_usage(api_key_id);
CREATE INDEX IF NOT EXISTS idx_api_usage_created_at ON api_usage(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_usage_endpoint ON api_usage(endpoint);

-- Enable Row Level Security
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_usage ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can only access their own API keys" ON api_keys
FOR ALL USING (
  clerk_user_id = (
    SELECT raw_user_meta_data->>'clerk_user_id' 
    FROM auth.users 
    WHERE id = auth.uid()
  )
);

CREATE POLICY "Users can only see usage for their API keys" ON api_usage
FOR SELECT USING (
  api_key_id IN (
    SELECT id FROM api_keys 
    WHERE clerk_user_id = (
      SELECT raw_user_meta_data->>'clerk_user_id' 
      FROM auth.users 
      WHERE id = auth.uid()
    )
  )
);

-- Create function to generate API keys
CREATE OR REPLACE FUNCTION generate_api_key()
RETURNS TEXT AS $$
BEGIN
  RETURN 'crm_' || encode(gen_random_bytes(32), 'hex');
END;
$$ LANGUAGE plpgsql;

-- Create function to validate API key and get user info
CREATE OR REPLACE FUNCTION validate_api_key(key TEXT)
RETURNS TABLE(
  is_valid BOOLEAN,
  clerk_user_id TEXT,
  permissions JSONB,
  rate_limit INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (ak.is_active AND (ak.expires_at IS NULL OR ak.expires_at > NOW())) as is_valid,
    ak.clerk_user_id,
    ak.permissions,
    ak.rate_limit
  FROM api_keys ak
  WHERE ak.api_key = key;
  
  -- Update last_used_at
  UPDATE api_keys 
  SET last_used_at = NOW() 
  WHERE api_key = key;
END;
$$ LANGUAGE plpgsql;

-- Create function to check rate limit
CREATE OR REPLACE FUNCTION check_rate_limit(key_id UUID, limit_per_hour INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
  request_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO request_count
  FROM api_usage
  WHERE api_key_id = key_id
    AND created_at > NOW() - INTERVAL '1 hour';
    
  RETURN request_count < limit_per_hour;
END;
$$ LANGUAGE plpgsql;

-- Create function to log API usage
CREATE OR REPLACE FUNCTION log_api_usage(
  key_id UUID,
  endpoint_path TEXT,
  http_method TEXT,
  status INTEGER DEFAULT 200,
  response_time INTEGER DEFAULT NULL,
  req_size INTEGER DEFAULT NULL,
  resp_size INTEGER DEFAULT NULL,
  client_ip INET DEFAULT NULL,
  client_agent TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO api_usage (
    api_key_id,
    endpoint,
    method,
    status_code,
    response_time_ms,
    request_size_bytes,
    response_size_bytes,
    ip_address,
    user_agent
  ) VALUES (
    key_id,
    endpoint_path,
    http_method,
    status,
    response_time,
    req_size,
    resp_size,
    client_ip,
    client_agent
  );
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT ALL ON api_keys TO authenticated;
GRANT ALL ON api_usage TO authenticated;
GRANT EXECUTE ON FUNCTION generate_api_key() TO authenticated;
GRANT EXECUTE ON FUNCTION validate_api_key(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION check_rate_limit(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION log_api_usage(UUID, TEXT, TEXT, INTEGER, INTEGER, INTEGER, INTEGER, INET, TEXT) TO authenticated;

-- Add helpful comments
COMMENT ON TABLE api_keys IS 'Stores API keys for external integrations like Make.com';
COMMENT ON TABLE api_usage IS 'Tracks API usage for rate limiting and analytics';
COMMENT ON FUNCTION validate_api_key IS 'Validates API key and returns user permissions';
COMMENT ON FUNCTION check_rate_limit IS 'Checks if API key has exceeded rate limit';
COMMENT ON FUNCTION log_api_usage IS 'Logs API request for analytics and monitoring';
