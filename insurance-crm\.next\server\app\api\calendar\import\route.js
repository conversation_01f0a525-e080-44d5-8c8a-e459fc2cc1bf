/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/calendar/import/route";
exports.ids = ["app/api/calendar/import/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcalendar%2Fimport%2Froute&page=%2Fapi%2Fcalendar%2Fimport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcalendar%2Fimport%2Froute.ts&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcalendar%2Fimport%2Froute&page=%2Fapi%2Fcalendar%2Fimport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcalendar%2Fimport%2Froute.ts&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_crm_insurance_crm_app_api_calendar_import_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/calendar/import/route.ts */ \"(rsc)/./app/api/calendar/import/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/calendar/import/route\",\n        pathname: \"/api/calendar/import\",\n        filename: \"route\",\n        bundlePath: \"app/api/calendar/import/route\"\n    },\n    resolvedPagePath: \"C:\\\\crm\\\\insurance-crm\\\\app\\\\api\\\\calendar\\\\import\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_crm_insurance_crm_app_api_calendar_import_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/calendar/import/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcalendar%2Fimport%2Froute&page=%2Fapi%2Fcalendar%2Fimport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcalendar%2Fimport%2Froute.ts&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/calendar/import/route.ts":
/*!******************************************!*\
  !*** ./app/api/calendar/import/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(rsc)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _lib_googleCalendar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/googleCalendar */ \"(rsc)/./lib/googleCalendar.ts\");\n/* harmony import */ var _lib_import_parseCalendarEvent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/import/parseCalendarEvent */ \"(rsc)/./lib/import/parseCalendarEvent.ts\");\n/* harmony import */ var _clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @clerk/nextjs/server */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/server/auth.js\");\n// app/api/calendar/import/route.ts\n\n\n\n\n\n\n// Create a service client that bypasses RLS for calendar import\nconst supabaseServiceClient = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://ufguunkzmqlkrtlfascw.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nasync function POST(request) {\n    try {\n        const { userId } = await (0,_clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_4__.auth)();\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const supabase = supabaseServiceClient;\n        const body = await request.json();\n        const { timeMin, timeMax, skipPast = true, aiParsing = true } = body;\n        // Get agent settings - but don't require agent_name and agent_npn\n        const { data: agentSettings } = await supabase.from(\"agent_settings\").select(\"*\").eq(\"clerk_user_id\", userId).single();\n        // Don't fail if settings don't have agent_name/agent_npn\n        // These fields might not be needed for calendar import anyway\n        console.log(\"Agent settings found:\", !!agentSettings);\n        // Fetch events from Google Calendar\n        const events = await _lib_googleCalendar__WEBPACK_IMPORTED_MODULE_1__.googleCalendar.getEvents(userId, \"primary\", timeMin ? new Date(timeMin) : new Date(), timeMax ? new Date(timeMax) : undefined);\n        console.log(`CALENDAR IMPORT: Found ${events.length} events from Google Calendar`);\n        const importResults = {\n            total: events.length,\n            imported: 0,\n            leads: 0,\n            tasks: 0,\n            appointments: 0,\n            followUps: 0,\n            duplicates: 0,\n            errors: []\n        };\n        const now = new Date();\n        // Process each event\n        for (const event of events){\n            try {\n                console.log(`Processing event: ${event.summary}`);\n                if (event.status === \"cancelled\") {\n                    console.log(`Skipping cancelled event: ${event.summary}`);\n                    continue;\n                }\n                // Skip past events if requested\n                const eventStartDate = event.start?.dateTime || event.start?.date;\n                if (!eventStartDate) continue;\n                const eventStart = new Date(eventStartDate);\n                if (skipPast && eventStart < now) {\n                    continue;\n                }\n                // Parse event with AI if enabled\n                const parsedEvent = aiParsing ? await (0,_lib_import_parseCalendarEvent__WEBPACK_IMPORTED_MODULE_2__.parseCalendarEvent)(event) : {\n                    type: \"other\",\n                    data: {\n                        name: event.summary || \"Unknown\",\n                        notes: event.description || \"\"\n                    },\n                    confidence: 50\n                };\n                // Check for duplicates based on phone and email\n                let isDuplicate = false;\n                if (parsedEvent.data.phone) {\n                    const { data: existingByPhone } = await supabase.from(\"leads\").select(\"id\").eq(\"phone\", parsedEvent.data.phone).eq(\"clerk_user_id\", userId).limit(1);\n                    if (existingByPhone && existingByPhone.length > 0) {\n                        isDuplicate = true;\n                    }\n                }\n                if (!isDuplicate && parsedEvent.data.email) {\n                    const { data: existingByEmail } = await supabase.from(\"leads\").select(\"id\").eq(\"email\", parsedEvent.data.email).eq(\"clerk_user_id\", userId).limit(1);\n                    if (existingByEmail && existingByEmail.length > 0) {\n                        isDuplicate = true;\n                    }\n                }\n                if (isDuplicate) {\n                    importResults.duplicates++;\n                    continue;\n                }\n                // Extract name from patterns like \"Follow up with - Bradley Brutlag -(*************\"\n                const extractNameFromEvent = (eventSummary)=>{\n                    // Try multiple patterns to extract names\n                    const patterns = [\n                        /(?:with|for)\\s*[-–]\\s*([^-–(]+?)(?:\\s*[-–(]|$)/i,\n                        /(?:call|meeting|appointment)\\s+(?:with\\s+)?([A-Z][a-z]+(?:\\s+[A-Z][a-z]+)*)/i,\n                        /^([A-Z][a-z]+(?:\\s+[A-Z][a-z]+)*)\\s*[-–]/i,\n                        /[-–]\\s*([A-Z][a-z]+(?:\\s+[A-Z][a-z]+)*)\\s*[-–]/i // \"- John Smith -\"\n                    ];\n                    for (const pattern of patterns){\n                        const match = eventSummary?.match(pattern);\n                        if (match && match[1]) {\n                            return match[1].trim();\n                        }\n                    }\n                    return null;\n                };\n                const extractedName = extractNameFromEvent(event.summary || \"\");\n                const finalName = extractedName || parsedEvent.data.name || event.summary || \"Unknown Contact\";\n                const nameParts = finalName.split(\" \");\n                const firstName = nameParts[0] || \"Unknown\";\n                const lastName = nameParts.slice(1).join(\" \") || \"Contact\";\n                // Process based on event type\n                switch(parsedEvent.type){\n                    case \"follow-up\":\n                        {\n                            // Create a follow-up in leads table with proper date fields\n                            const leadData = {\n                                clerk_user_id: userId,\n                                first_name: firstName,\n                                last_name: lastName,\n                                email: parsedEvent.data.email || null,\n                                phone: parsedEvent.data.phone || null,\n                                state: parsedEvent.data.state || null,\n                                city: parsedEvent.data.city || null,\n                                source: \"Google Calendar\",\n                                status: \"Attempting Contact\",\n                                priority: parsedEvent.data.priority || \"high\",\n                                estimated_value: parsedEvent.data.estimatedValue?.toString() || null,\n                                notes: `Follow-up scheduled for ${(0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_5__.format)(eventStart, \"MMM d, yyyy h:mm a\")}\\n\\nExtracted from: \"${event.summary}\"\\n\\n${parsedEvent.data.notes || \"\"}`,\n                                score: 75,\n                                created_at: new Date().toISOString(),\n                                // Add the required date fields for persistence\n                                follow_up_date: eventStart.toISOString(),\n                                appointment_date: eventStart.toISOString(),\n                                calendar_event_id: event.id,\n                                ai_classification: \"follow_up\",\n                                follow_up_required: true,\n                                raw_data: {\n                                    calendar_event_id: event.id,\n                                    original_title: event.summary,\n                                    extracted_name: extractedName,\n                                    parsed_data: parsedEvent.data,\n                                    confidence: parsedEvent.confidence,\n                                    event_date: eventStart.toISOString(),\n                                    event_type: \"follow-up\"\n                                }\n                            };\n                            const { data: newLead, error: leadError } = await supabase.from(\"leads\").insert(leadData).select().single();\n                            if (leadError) {\n                                console.error(\"Error creating follow-up lead:\", leadError);\n                                throw leadError;\n                            }\n                            // Create AI analysis entry\n                            await supabase.from(\"ai_lead_analysis\").insert({\n                                lead_id: newLead.id,\n                                stage: \"missed_appointment\",\n                                ai_score: 75,\n                                ai_recommendations: [\n                                    \"Send re-scheduling SMS\",\n                                    \"Follow up within 24 hours\"\n                                ],\n                                next_action: \"Send re-scheduling SMS\",\n                                calendar_event_id: event.id,\n                                follow_up_sent: false,\n                                follow_up_method: \"none\",\n                                created_at: new Date().toISOString()\n                            });\n                            importResults.followUps++;\n                            break;\n                        }\n                    case \"appointment\":\n                        {\n                            // Create appointment as a lead with proper date fields\n                            const leadData = {\n                                clerk_user_id: userId,\n                                first_name: firstName,\n                                last_name: lastName,\n                                email: parsedEvent.data.email || null,\n                                phone: parsedEvent.data.phone || null,\n                                state: parsedEvent.data.state || null,\n                                city: parsedEvent.data.city || null,\n                                source: \"Google Calendar\",\n                                status: \"Appointment Scheduled\",\n                                priority: \"high\",\n                                estimated_value: parsedEvent.data.estimatedValue?.toString() || null,\n                                notes: `Appointment scheduled for ${(0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_5__.format)(eventStart, \"MMM d, yyyy h:mm a\")}\\nLocation: ${event.location || \"Not specified\"}\\n\\nExtracted from: \"${event.summary}\"\\n\\n${parsedEvent.data.notes || \"\"}`,\n                                score: 80,\n                                created_at: new Date().toISOString(),\n                                // Add the required date fields for persistence\n                                appointment_date: eventStart.toISOString(),\n                                follow_up_date: eventStart.toISOString(),\n                                calendar_event_id: event.id,\n                                ai_classification: \"virtual_appointment\",\n                                follow_up_required: false,\n                                raw_data: {\n                                    calendar_event_id: event.id,\n                                    original_title: event.summary,\n                                    extracted_name: extractedName,\n                                    parsed_data: parsedEvent.data,\n                                    confidence: parsedEvent.confidence,\n                                    event_date: eventStart.toISOString(),\n                                    event_type: \"appointment\",\n                                    location: event.location\n                                }\n                            };\n                            const { data: newLead, error: leadError } = await supabase.from(\"leads\").insert(leadData).select().single();\n                            if (leadError) {\n                                console.error(\"Error creating appointment lead:\", leadError);\n                                throw leadError;\n                            }\n                            // Create AI analysis entry\n                            await supabase.from(\"ai_lead_analysis\").insert({\n                                lead_id: newLead.id,\n                                stage: \"appointment_scheduled\",\n                                ai_score: 80,\n                                ai_recommendations: [\n                                    \"Send appointment confirmation\",\n                                    \"Prepare meeting materials\"\n                                ],\n                                next_action: \"Send appointment confirmation\",\n                                calendar_event_id: event.id,\n                                follow_up_sent: false,\n                                follow_up_method: \"none\",\n                                created_at: new Date().toISOString()\n                            });\n                            importResults.appointments++;\n                            break;\n                        }\n                    case \"lead\":\n                    case \"other\":\n                    default:\n                        {\n                            // Create standard lead with proper date fields\n                            const leadData = {\n                                clerk_user_id: userId,\n                                first_name: firstName,\n                                last_name: lastName,\n                                email: parsedEvent.data.email || null,\n                                phone: parsedEvent.data.phone || null,\n                                state: parsedEvent.data.state || null,\n                                city: parsedEvent.data.city || null,\n                                source: \"Google Calendar\",\n                                status: \"New Lead\",\n                                priority: parsedEvent.data.priority || \"medium\",\n                                estimated_value: parsedEvent.data.estimatedValue?.toString() || null,\n                                notes: `${parsedEvent.data.notes || event.description || \"\"}\\n\\nExtracted from: \"${event.summary}\"`,\n                                score: 50,\n                                created_at: new Date().toISOString(),\n                                // Add the required date fields for persistence\n                                appointment_date: eventStart.toISOString(),\n                                follow_up_date: eventStart.toISOString(),\n                                calendar_event_id: event.id,\n                                ai_classification: \"other\",\n                                follow_up_required: false,\n                                raw_data: {\n                                    calendar_event_id: event.id,\n                                    original_title: event.summary,\n                                    extracted_name: extractedName,\n                                    parsed_data: parsedEvent.data,\n                                    confidence: parsedEvent.confidence,\n                                    referral_source: parsedEvent.data.referralSource || null,\n                                    event_date: event.start?.dateTime || event.start?.date\n                                }\n                            };\n                            const { data: newLead, error: leadError } = await supabase.from(\"leads\").insert(leadData).select().single();\n                            if (leadError) {\n                                console.error(\"Error creating standard lead:\", leadError);\n                                throw leadError;\n                            }\n                            // Create AI analysis entry\n                            await supabase.from(\"ai_lead_analysis\").insert({\n                                lead_id: newLead.id,\n                                stage: \"new_lead\",\n                                ai_score: 50,\n                                ai_recommendations: [\n                                    \"Review and categorize manually\",\n                                    \"Add contact information\"\n                                ],\n                                next_action: \"Review and categorize manually\",\n                                calendar_event_id: event.id,\n                                follow_up_sent: false,\n                                follow_up_method: \"none\",\n                                created_at: new Date().toISOString()\n                            });\n                            importResults.leads++;\n                            break;\n                        }\n                }\n                importResults.imported++;\n            } catch (error) {\n                const errorEntry = {\n                    event: event.summary || \"Unknown event\",\n                    error: error.message || \"Unknown error\"\n                };\n                importResults.errors.push(errorEntry);\n                console.error(\"Error processing event:\", event.summary, error);\n            }\n        }\n        console.log(\"CALENDAR IMPORT RESULTS:\", {\n            total: importResults.total,\n            imported: importResults.imported,\n            leads: importResults.leads,\n            appointments: importResults.appointments,\n            followUps: importResults.followUps,\n            duplicates: importResults.duplicates,\n            errors: importResults.errors\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            ...importResults,\n            message: `Successfully imported ${importResults.imported} events (${importResults.leads} leads, ${importResults.appointments} appointments, ${importResults.followUps} follow-ups)`\n        });\n    } catch (error) {\n        console.error(\"Error importing events:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Failed to import calendar events\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/calendar/import/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/googleCalendar.ts":
/*!*******************************!*\
  !*** ./lib/googleCalendar.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getGoogleAuthUrl: () => (/* binding */ getGoogleAuthUrl),\n/* harmony export */   getStoredTokens: () => (/* binding */ getStoredTokens),\n/* harmony export */   getTokensFromCode: () => (/* binding */ getTokensFromCode),\n/* harmony export */   googleCalendar: () => (/* binding */ googleCalendar),\n/* harmony export */   oauth2Client: () => (/* binding */ oauth2Client),\n/* harmony export */   storeTokensForUser: () => (/* binding */ storeTokensForUser)\n/* harmony export */ });\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! googleapis */ \"(rsc)/./node_modules/googleapis/build/src/index.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// lib/googleCalendar.ts (or utils/googleCalendar.ts - they're typically the same)\n\n\n// Initialize OAuth2 client\nconst oauth2Client = new googleapis__WEBPACK_IMPORTED_MODULE_0__.google.auth.OAuth2(process.env.GOOGLE_CLIENT_ID, process.env.GOOGLE_CLIENT_SECRET, process.env.GOOGLE_REDIRECT_URI);\n// Initialize Supabase client for server-side operations\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://ufguunkzmqlkrtlfascw.supabase.co\", process.env.SUPABASE_SERVICE_KEY);\nfunction getGoogleAuthUrl(state) {\n    const scopes = [\n        \"https://www.googleapis.com/auth/calendar\",\n        \"https://www.googleapis.com/auth/calendar.events\",\n        \"https://www.googleapis.com/auth/userinfo.email\",\n        \"https://www.googleapis.com/auth/userinfo.profile\"\n    ];\n    return oauth2Client.generateAuthUrl({\n        access_type: \"offline\",\n        prompt: \"consent\",\n        scope: scopes,\n        state: state // Pass state for security and user identification\n    });\n}\n// Exchange authorization code for tokens\nasync function getTokensFromCode(code) {\n    try {\n        const { tokens } = await oauth2Client.getToken(code);\n        oauth2Client.setCredentials(tokens);\n        return tokens;\n    } catch (error) {\n        console.error(\"Error exchanging code for tokens:\", error);\n        throw new Error(\"Failed to exchange authorization code\");\n    }\n}\n// Store tokens in Supabase\nasync function storeTokensForUser(userId, tokens) {\n    try {\n        const { error } = await supabase.from(\"agent_settings\").upsert({\n            clerk_user_id: userId,\n            google_calendar_token: tokens.access_token,\n            google_calendar_refresh_token: tokens.refresh_token,\n            google_calendar_token_expiry: new Date(tokens.expiry_date).toISOString(),\n            updated_at: new Date().toISOString()\n        }, {\n            onConflict: \"clerk_user_id\" // Changed from \"user_id\" to \"clerk_user_id\"\n        });\n        if (error) {\n            console.error(\"Error storing tokens:\", error);\n            throw error;\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Failed to store tokens:\", error);\n        throw new Error(\"Failed to store authentication tokens\");\n    }\n}\n// Get stored tokens for a user\nasync function getStoredTokens(userId) {\n    try {\n        const { data, error } = await supabase.from(\"agent_settings\").select(\"google_calendar_token, google_calendar_refresh_token, google_calendar_token_expiry\").eq(\"clerk_user_id\", userId) // Changed from \"user_id\" to \"clerk_user_id\"\n        .single();\n        if (error || !data) {\n            return null;\n        }\n        // Check if token is expired\n        const expiryDate = new Date(data.google_calendar_token_expiry);\n        const now = new Date();\n        if (expiryDate <= now) {\n            // Token is expired, try to refresh\n            if (data.google_calendar_refresh_token) {\n                return await refreshAccessToken(userId, data.google_calendar_refresh_token);\n            }\n            return null;\n        }\n        return {\n            access_token: data.google_calendar_token,\n            refresh_token: data.google_calendar_refresh_token,\n            expiry_date: data.google_calendar_token_expiry\n        };\n    } catch (error) {\n        console.error(\"Error getting stored tokens:\", error);\n        return null;\n    }\n}\n// Refresh access token\nasync function refreshAccessToken(userId, refreshToken) {\n    try {\n        oauth2Client.setCredentials({\n            refresh_token: refreshToken\n        });\n        const { credentials } = await oauth2Client.refreshAccessToken();\n        // Store the new tokens\n        await storeTokensForUser(userId, credentials);\n        return credentials;\n    } catch (error) {\n        console.error(\"Error refreshing token:\", error);\n        return null;\n    }\n}\n// Enhanced googleCalendar object with all methods\nconst googleCalendar = {\n    // Get calendar events\n    async getEvents (userId, calendarId = \"primary\", timeMin, timeMax) {\n        try {\n            // Get stored tokens\n            const tokens = await getStoredTokens(userId);\n            if (!tokens) {\n                throw new Error(\"No valid authentication tokens found\");\n            }\n            // Set credentials\n            oauth2Client.setCredentials({\n                access_token: tokens.access_token,\n                refresh_token: tokens.refresh_token\n            });\n            const calendar = googleapis__WEBPACK_IMPORTED_MODULE_0__.google.calendar({\n                version: \"v3\",\n                auth: oauth2Client\n            });\n            const response = await calendar.events.list({\n                calendarId,\n                timeMin: timeMin?.toISOString() || new Date().toISOString(),\n                timeMax: timeMax?.toISOString(),\n                singleEvents: true,\n                orderBy: \"startTime\",\n                maxResults: 250 // Adjust as needed\n            });\n            return response.data.items || [];\n        } catch (error) {\n            console.error(\"Error fetching calendar events:\", error);\n            throw new Error(\"Failed to fetch calendar events\");\n        }\n    },\n    // Create a new calendar event\n    async createEvent (userId, eventData, calendarId = \"primary\") {\n        try {\n            const tokens = await getStoredTokens(userId);\n            if (!tokens) {\n                throw new Error(\"No valid authentication tokens found\");\n            }\n            oauth2Client.setCredentials({\n                access_token: tokens.access_token,\n                refresh_token: tokens.refresh_token\n            });\n            const calendar = googleapis__WEBPACK_IMPORTED_MODULE_0__.google.calendar({\n                version: \"v3\",\n                auth: oauth2Client\n            });\n            const event = {\n                summary: eventData.summary,\n                description: eventData.description,\n                location: eventData.location,\n                start: {\n                    dateTime: eventData.start.toISOString(),\n                    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone\n                },\n                end: {\n                    dateTime: eventData.end.toISOString(),\n                    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone\n                },\n                attendees: eventData.attendees,\n                reminders: eventData.reminders || {\n                    useDefault: false,\n                    overrides: [\n                        {\n                            method: \"email\",\n                            minutes: 24 * 60\n                        },\n                        {\n                            method: \"popup\",\n                            minutes: 30\n                        }\n                    ]\n                }\n            };\n            const response = await calendar.events.insert({\n                calendarId,\n                requestBody: event,\n                sendUpdates: \"all\" // Send invites to attendees\n            });\n            // Log event in CRM\n            await logEventInCRM(userId, response.data);\n            return response.data;\n        } catch (error) {\n            console.error(\"Error creating calendar event:\", error);\n            throw new Error(\"Failed to create calendar event\");\n        }\n    },\n    // Update an existing event\n    async updateEvent (userId, eventId, updates, calendarId = \"primary\") {\n        try {\n            const tokens = await getStoredTokens(userId);\n            if (!tokens) {\n                throw new Error(\"No valid authentication tokens found\");\n            }\n            oauth2Client.setCredentials({\n                access_token: tokens.access_token,\n                refresh_token: tokens.refresh_token\n            });\n            const calendar = googleapis__WEBPACK_IMPORTED_MODULE_0__.google.calendar({\n                version: \"v3\",\n                auth: oauth2Client\n            });\n            const eventPatch = {};\n            if (updates.summary) eventPatch.summary = updates.summary;\n            if (updates.description) eventPatch.description = updates.description;\n            if (updates.location) eventPatch.location = updates.location;\n            if (updates.start) {\n                eventPatch.start = {\n                    dateTime: updates.start.toISOString(),\n                    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone\n                };\n            }\n            if (updates.end) {\n                eventPatch.end = {\n                    dateTime: updates.end.toISOString(),\n                    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone\n                };\n            }\n            if (updates.attendees) eventPatch.attendees = updates.attendees;\n            const response = await calendar.events.patch({\n                calendarId,\n                eventId,\n                requestBody: eventPatch,\n                sendUpdates: \"all\"\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Error updating calendar event:\", error);\n            throw new Error(\"Failed to update calendar event\");\n        }\n    },\n    // Delete an event\n    async deleteEvent (userId, eventId, calendarId = \"primary\") {\n        try {\n            const tokens = await getStoredTokens(userId);\n            if (!tokens) {\n                throw new Error(\"No valid authentication tokens found\");\n            }\n            oauth2Client.setCredentials({\n                access_token: tokens.access_token,\n                refresh_token: tokens.refresh_token\n            });\n            const calendar = googleapis__WEBPACK_IMPORTED_MODULE_0__.google.calendar({\n                version: \"v3\",\n                auth: oauth2Client\n            });\n            await calendar.events.delete({\n                calendarId,\n                eventId,\n                sendUpdates: \"all\"\n            });\n            // Remove from CRM tracking\n            await supabase.from(\"calendar_events\").delete().eq(\"google_event_id\", eventId);\n            return true;\n        } catch (error) {\n            console.error(\"Error deleting calendar event:\", error);\n            throw new Error(\"Failed to delete calendar event\");\n        }\n    },\n    // Import events as leads (enhanced version)\n    async importEventsAsLeads (userId, events, options) {\n        try {\n            const now = new Date();\n            const leadsToImport = [];\n            for (const event of events){\n                const eventStart = new Date(event.start?.dateTime || event.start?.date || \"\");\n                // Skip past events if requested\n                if (options.skipPast && eventStart < now) {\n                    continue;\n                }\n                // Extract attendee emails\n                const attendeeEmails = event.attendees?.map((a)=>a.email) || [];\n                // Try to find existing client\n                let clientId = null;\n                for (const email of attendeeEmails){\n                    if (options.clientIdMapping?.[email]) {\n                        clientId = options.clientIdMapping[email];\n                        break;\n                    }\n                    // Check if client exists in database\n                    const { data: existingClient } = await supabase.from(\"clients\").select(\"id\").eq(\"email\", email).eq(\"user_id\", userId).single();\n                    if (existingClient) {\n                        clientId = existingClient.id;\n                        break;\n                    }\n                }\n                // If no existing client, create a lead\n                if (!clientId && attendeeEmails.length > 0) {\n                    const leadData = {\n                        user_id: userId,\n                        first_name: event.summary || \"Calendar\",\n                        last_name: \"Lead\",\n                        email: attendeeEmails[0],\n                        status: options.defaultStatus || \"lead\",\n                        lead_source: options.defaultSource || \"Google Calendar\",\n                        notes: `Imported from Google Calendar event: ${event.summary}\\n${event.description || \"\"}`,\n                        next_followup_date: eventStart.toISOString().split(\"T\")[0]\n                    };\n                    const { data: newClient, error } = await supabase.from(\"clients\").insert(leadData).select().single();\n                    if (!error && newClient) {\n                        clientId = newClient.id;\n                        leadsToImport.push(newClient);\n                    }\n                }\n                // Log the calendar event in CRM\n                if (clientId) {\n                    await supabase.from(\"calendar_events\").insert({\n                        user_id: userId,\n                        client_id: clientId,\n                        google_event_id: event.id,\n                        summary: event.summary || \"Untitled Event\",\n                        description: event.description,\n                        location: event.location,\n                        start_time: event.start?.dateTime || event.start?.date,\n                        end_time: event.end?.dateTime || event.end?.date,\n                        attendees: event.attendees || [],\n                        event_type: \"meeting\",\n                        status: event.status || \"confirmed\"\n                    });\n                }\n            }\n            return {\n                imported: leadsToImport.length,\n                leads: leadsToImport,\n                totalProcessed: events.length\n            };\n        } catch (error) {\n            console.error(\"Error importing events as leads:\", error);\n            throw new Error(\"Failed to import calendar events\");\n        }\n    },\n    // Get user's calendar list\n    async getCalendarList (userId) {\n        try {\n            const tokens = await getStoredTokens(userId);\n            if (!tokens) {\n                throw new Error(\"No valid authentication tokens found\");\n            }\n            oauth2Client.setCredentials({\n                access_token: tokens.access_token,\n                refresh_token: tokens.refresh_token\n            });\n            const calendar = googleapis__WEBPACK_IMPORTED_MODULE_0__.google.calendar({\n                version: \"v3\",\n                auth: oauth2Client\n            });\n            const response = await calendar.calendarList.list();\n            return response.data.items || [];\n        } catch (error) {\n            console.error(\"Error fetching calendar list:\", error);\n            throw new Error(\"Failed to fetch calendar list\");\n        }\n    },\n    // Check authentication status\n    async isAuthenticated (userId) {\n        const tokens = await getStoredTokens(userId);\n        return !!tokens;\n    },\n    /**\r\n   * AI-powered event classification based on user's naming conventions\r\n   */ classifyCalendarEvent (event) {\n        const title = (event.summary || \"\").toLowerCase();\n        const description = (event.description || \"\").toLowerCase();\n        const now = new Date();\n        const eventStart = new Date(event.start?.dateTime || event.start?.date);\n        const isPastEvent = eventStart < now;\n        // Classify based on naming conventions\n        if (title.includes(\"follow up\") || title.includes(\"follow-up\")) {\n            return {\n                type: \"follow_up\",\n                leadStatus: \"missed_appointment\",\n                priority: \"high\",\n                aiScore: 85,\n                nextAction: \"Send re-scheduling SMS\",\n                followUpRequired: true,\n                smsTemplate: `Hi {name}, I noticed we missed our appointment. I'd love to reschedule and help you with your insurance needs. When would be a good time to connect? Reply with your preferred time or call me at {agent_phone}.`\n            };\n        }\n        if (title.includes(\"virt\") || title.includes(\"virtual\")) {\n            return {\n                type: \"virtual_appointment\",\n                leadStatus: \"appointment_scheduled\",\n                priority: \"high\",\n                aiScore: 90,\n                nextAction: isPastEvent ? \"Check if appointment was attended\" : \"Send appointment reminder\",\n                followUpRequired: isPastEvent,\n                smsTemplate: isPastEvent ? `Hi {name}, I hope our virtual meeting went well! I wanted to follow up on our discussion about your insurance options. Do you have any questions or would you like to move forward? Let me know!` : `Hi {name}, this is a reminder about our virtual appointment scheduled for {appointment_time}. Looking forward to discussing your insurance needs! Meeting link: {meeting_link}`\n            };\n        }\n        if (title.includes(\"calendly\") || description.includes(\"calendly\")) {\n            return {\n                type: \"calendly_appointment\",\n                leadStatus: \"upcoming_appointment\",\n                priority: \"high\",\n                aiScore: 95,\n                nextAction: \"Send pre-appointment preparation\",\n                followUpRequired: false,\n                smsTemplate: `Hi {name}, thank you for scheduling our appointment for {appointment_time}! I'm excited to help you find the perfect insurance solution. Please have any current policies ready for review. See you soon!`\n            };\n        }\n        // Default classification for other events\n        return {\n            type: \"other\",\n            leadStatus: \"new_lead\",\n            priority: \"medium\",\n            aiScore: 60,\n            nextAction: \"Review and categorize manually\",\n            followUpRequired: false,\n            smsTemplate: `Hi {name}, I wanted to reach out regarding our upcoming meeting. I'm here to help with any insurance questions you might have. Looking forward to connecting!`\n        };\n    },\n    /**\r\n   * Process calendar events and convert to leads with AI classification\r\n   */ async processEventsToLeads (userId, events, options = {}) {\n        const processedEvents = [];\n        const now = new Date();\n        for (const event of events){\n            try {\n                const eventStart = new Date(event.start?.dateTime || event.start?.date);\n                // Skip past events if requested\n                if (options.skipPast && eventStart < now) {\n                    continue;\n                }\n                // Classify the event\n                const classification = this.classifyCalendarEvent(event);\n                // Extract data from event\n                const extractedData = this.extractEventData(event);\n                const processedEvent = {\n                    originalEvent: event,\n                    classification,\n                    extractedData\n                };\n                processedEvents.push(processedEvent);\n                // Auto-create leads if enabled\n                if (options.autoCreateLeads && extractedData.email) {\n                    await this.createLeadFromEvent(userId, processedEvent);\n                }\n                // Send follow-ups if enabled and required\n                if (options.sendFollowUps && classification.followUpRequired) {\n                    await this.scheduleFollowUpMessage(userId, processedEvent);\n                }\n            } catch (error) {\n                console.error(\"Error processing event:\", event.id, error);\n            }\n        }\n        return processedEvents;\n    },\n    /**\r\n   * Extract relevant data from calendar event\r\n   */ extractEventData (event) {\n        const attendees = event.attendees || [];\n        const primaryAttendee = attendees.find((a)=>!a.organizer) || attendees[0];\n        return {\n            name: this.extractNameFromEvent(event),\n            email: primaryAttendee?.email,\n            phone: this.extractPhoneFromEvent(event),\n            notes: event.description || \"\",\n            appointmentTime: event.start?.dateTime || event.start?.date,\n            location: event.location\n        };\n    },\n    /**\r\n   * Extract name from event title or attendees\r\n   */ extractNameFromEvent (event) {\n        const title = event.summary || \"\";\n        const attendees = event.attendees || [];\n        // Try to extract name from title (common patterns)\n        const namePatterns = [\n            /(?:meeting with|call with|appointment with)\\s+([^-\\n]+)/i,\n            /^([^-\\n]+?)\\s*(?:-|meeting|call|appointment)/i,\n            /^([A-Z][a-z]+\\s+[A-Z][a-z]+)/\n        ];\n        for (const pattern of namePatterns){\n            const match = title.match(pattern);\n            if (match && match[1]) {\n                return match[1].trim();\n            }\n        }\n        // Fallback to attendee name\n        const primaryAttendee = attendees.find((a)=>!a.organizer);\n        if (primaryAttendee?.displayName) {\n            return primaryAttendee.displayName;\n        }\n        // Extract name from email\n        if (primaryAttendee?.email) {\n            const emailName = primaryAttendee.email.split(\"@\")[0];\n            return emailName.replace(/[._]/g, \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase());\n        }\n        return \"Unknown Contact\";\n    },\n    /**\r\n   * Extract phone number from event description\r\n   */ extractPhoneFromEvent (event) {\n        const description = event.description || \"\";\n        const phonePattern = /(?:phone|tel|call|mobile):\\s*([+]?[\\d\\s\\-().]{10,})/i;\n        const match = description.match(phonePattern);\n        return match ? match[1].replace(/\\D/g, \"\") : undefined;\n    },\n    /**\r\n   * Create a lead from a processed calendar event\r\n   */ async createLeadFromEvent (userId, processedEvent) {\n        try {\n            const { classification, extractedData, originalEvent } = processedEvent;\n            // Check if lead already exists\n            if (extractedData.email) {\n                const { data: existingLead } = await supabase.from(\"leads\").select(\"id\").eq(\"clerk_user_id\", userId).eq(\"email\", extractedData.email).single();\n                if (existingLead) {\n                    console.log(\"Lead already exists for email:\", extractedData.email);\n                    return existingLead;\n                }\n            }\n            // Create new lead\n            const leadData = {\n                clerk_user_id: userId,\n                name: extractedData.name || \"Calendar Lead\",\n                email: extractedData.email || null,\n                phone: extractedData.phone || null,\n                source: `Google Calendar - ${classification.type}`,\n                status: this.mapLeadStatusToStatus(classification.leadStatus),\n                priority: classification.priority,\n                estimated_value: this.estimateLeadValue(classification),\n                notes: `${extractedData.notes}\\n\\nAI Classification: ${classification.type}\\nNext Action: ${classification.nextAction}`,\n                score: classification.aiScore,\n                calendar_event_id: originalEvent.id,\n                appointment_date: extractedData.appointmentTime,\n                created_at: new Date().toISOString()\n            };\n            const { data: newLead, error } = await supabase.from(\"leads\").insert(leadData).select().single();\n            if (error) {\n                console.error(\"Error creating lead from event:\", error);\n                throw error;\n            }\n            // Create AI analysis entry\n            await supabase.from(\"ai_lead_analysis\").insert({\n                lead_id: newLead.id,\n                stage: classification.leadStatus,\n                ai_score: classification.aiScore,\n                ai_recommendations: [\n                    classification.nextAction\n                ],\n                next_action: classification.nextAction,\n                created_at: new Date().toISOString()\n            });\n            console.log(\"Created lead from calendar event:\", newLead.id);\n            return newLead;\n        } catch (error) {\n            console.error(\"Error creating lead from event:\", error);\n            throw error;\n        }\n    },\n    /**\r\n   * Schedule a follow-up message for a processed event\r\n   */ async scheduleFollowUpMessage (userId, processedEvent) {\n        try {\n            const { classification, extractedData } = processedEvent;\n            if (!extractedData.phone && !extractedData.email) {\n                console.log(\"No contact info available for follow-up\");\n                return;\n            }\n            // Get user's agent info for personalization\n            const { data: agentSettings } = await supabase.from(\"agent_settings\").select(\"agent_name, agent_phone\").eq(\"clerk_user_id\", userId).single();\n            // Personalize the SMS template\n            let message = classification.smsTemplate || \"Hi {name}, following up on our appointment. Please let me know if you have any questions!\";\n            message = message.replace(\"{name}\", extractedData.name || \"there\").replace(\"{appointment_time}\", this.formatAppointmentTime(extractedData.appointmentTime)).replace(\"{agent_phone}\", agentSettings?.agent_phone || \"your agent\").replace(\"{meeting_link}\", extractedData.location || \"\");\n            // Schedule the message (create a campaign for immediate sending)\n            const campaignData = {\n                name: `Auto Follow-up: ${extractedData.name || \"Calendar Lead\"}`,\n                type: extractedData.phone ? \"SMS\" : \"Email\",\n                subject: extractedData.phone ? null : \"Following up on our appointment\",\n                content: message,\n                audience: \"Custom\",\n                status: \"Scheduled\",\n                scheduled_for: new Date().toISOString(),\n                created_at: new Date().toISOString()\n            };\n            const { data: campaign, error: campaignError } = await supabase.from(\"campaigns\").insert(campaignData).select().single();\n            if (campaignError) {\n                console.error(\"Error creating follow-up campaign:\", campaignError);\n                return;\n            }\n            // Create campaign recipient\n            await supabase.from(\"campaign_recipients\").insert({\n                campaign_id: campaign.id,\n                email: extractedData.email,\n                phone: extractedData.phone,\n                name: extractedData.name\n            });\n            console.log(\"Scheduled follow-up message for:\", extractedData.name);\n        } catch (error) {\n            console.error(\"Error scheduling follow-up message:\", error);\n        }\n    },\n    /**\r\n   * Helper methods for lead creation\r\n   */ mapLeadStatusToStatus (leadStatus) {\n        const statusMap = {\n            \"missed_appointment\": \"Attempting Contact\",\n            \"appointment_scheduled\": \"Contact Made\",\n            \"upcoming_appointment\": \"Appointment Scheduled\",\n            \"new_lead\": \"New Lead\"\n        };\n        return statusMap[leadStatus] || \"New Lead\";\n    },\n    estimateLeadValue (classification) {\n        const valueMap = {\n            \"follow_up\": 750,\n            \"virtual_appointment\": 1000,\n            \"calendly_appointment\": 1200,\n            \"other\": 500\n        };\n        return valueMap[classification.type] || 500;\n    },\n    formatAppointmentTime (appointmentTime) {\n        if (!appointmentTime) return \"our scheduled time\";\n        try {\n            const date = new Date(appointmentTime);\n            return date.toLocaleDateString(\"en-US\", {\n                weekday: \"long\",\n                year: \"numeric\",\n                month: \"long\",\n                day: \"numeric\",\n                hour: \"numeric\",\n                minute: \"2-digit\"\n            });\n        } catch  {\n            return appointmentTime;\n        }\n    },\n    // Revoke access\n    async revokeAccess (userId) {\n        try {\n            const tokens = await getStoredTokens(userId);\n            if (tokens?.access_token) {\n                await oauth2Client.revokeToken(tokens.access_token);\n            }\n            // Clear tokens from database\n            await supabase.from(\"agent_settings\").update({\n                google_calendar_token: null,\n                google_calendar_refresh_token: null,\n                google_calendar_token_expiry: null,\n                updated_at: new Date().toISOString()\n            }).eq(\"clerk_user_id\", userId);\n            return true;\n        } catch (error) {\n            console.error(\"Error revoking access:\", error);\n            throw new Error(\"Failed to revoke Google Calendar access\");\n        }\n    }\n};\n// Helper function to log events in CRM\nasync function logEventInCRM(userId, googleEvent) {\n    try {\n        await supabase.from(\"calendar_events\").upsert({\n            user_id: userId,\n            google_event_id: googleEvent.id,\n            summary: googleEvent.summary,\n            description: googleEvent.description,\n            location: googleEvent.location,\n            start_time: googleEvent.start?.dateTime || googleEvent.start?.date,\n            end_time: googleEvent.end?.dateTime || googleEvent.end?.date,\n            attendees: googleEvent.attendees || [],\n            event_type: \"meeting\",\n            status: googleEvent.status || \"confirmed\",\n            updated_at: new Date().toISOString()\n        }, {\n            onConflict: \"google_event_id\"\n        });\n    } catch (error) {\n        console.error(\"Error logging event in CRM:\", error);\n    // Non-fatal error, don't throw\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/googleCalendar.ts\n");

/***/ }),

/***/ "(rsc)/./lib/import/parseCalendarEvent.ts":
/*!******************************************!*\
  !*** ./lib/import/parseCalendarEvent.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseCalendarEvent: () => (/* binding */ parseCalendarEvent)\n/* harmony export */ });\n// lib/import/parseCalendarEvent.ts\nasync function parseCalendarEvent(event) {\n    const title = event.summary || \"\";\n    const description = event.description || \"\";\n    const location = event.location || \"\";\n    // Detect event type\n    const eventType = detectEventType(title, description, location);\n    // Extract structured data based on your format\n    const extractedData = extractStructuredData(title, description);\n    // Parse attendee information\n    const attendeeData = parseAttendees(event.attendees || []);\n    // Combine all data\n    const parsedData = {\n        type: eventType,\n        data: {\n            ...extractedData,\n            ...attendeeData,\n            notes: description,\n            priority: determinePriority(title, description),\n            estimatedValue: estimateValue(eventType, extractedData)\n        },\n        confidence: calculateConfidence(extractedData)\n    };\n    return parsedData;\n}\nfunction detectEventType(title, description, location) {\n    const titleLower = title.toLowerCase();\n    const descLower = description.toLowerCase();\n    // Follow-up detection\n    if (titleLower.includes(\"follow up\") || titleLower.includes(\"follow-up\")) {\n        return \"follow-up\";\n    }\n    // Appointment detection\n    if (titleLower.includes(\"appointment\") || titleLower.includes(\"meeting\") || titleLower.includes(\"consultation\") || titleLower.includes(\"virtual appointment\") || location.toLowerCase().includes(\"zoom\") || location.toLowerCase().includes(\"meet\") || descLower.includes(\"calendly\")) {\n        return \"appointment\";\n    }\n    // Task detection\n    if (titleLower.includes(\"task\") || titleLower.includes(\"reminder\")) {\n        return \"task\";\n    }\n    // Default to lead\n    return \"lead\";\n}\nfunction extractStructuredData(title, description) {\n    const data = {};\n    // Extract from your specific format\n    // Example: \"Follow up with - Harold Dehner Jr -************ - agent who sent me prospect -Eric cunningham\"\n    const titleMatch = title.match(/Follow up with - (.+?) -(\\d{3}-\\d{3}-\\d{4})/);\n    if (titleMatch) {\n        data.name = titleMatch[1].trim();\n        data.phone = titleMatch[2].trim();\n    }\n    // Extract from description JSON if present\n    try {\n        const jsonMatch = description.match(/\\{\"phone\":.+?\\}/);\n        if (jsonMatch) {\n            const jsonData = JSON.parse(jsonMatch[0]);\n            Object.assign(data, {\n                phone: jsonData.phone || data.phone,\n                email: jsonData.email,\n                zip: jsonData.zip,\n                address: jsonData.address,\n                dob: jsonData.dob,\n                gender: jsonData.gender,\n                income: jsonData.income,\n                name: jsonData.name || data.name\n            });\n        }\n    } catch  {\n    // JSON parsing failed, continue with regex extraction\n    }\n    // Extract referral partner\n    const referralMatch = description.match(/Referral partner who gave Lead: (.+?)(?:\\n|$)/);\n    if (referralMatch) {\n        data.referralSource = referralMatch[1].trim();\n    }\n    // Extract email\n    const emailMatch = description.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})/);\n    if (emailMatch) {\n        data.email = emailMatch[1];\n    }\n    // Extract phone if not already found\n    if (!data.phone) {\n        const phoneMatch = description.match(/(\\d{3}[-.]?\\d{3}[-.]?\\d{4})/);\n        if (phoneMatch) {\n            data.phone = phoneMatch[1].replace(/[.-]/g, \"-\");\n        }\n    }\n    return data;\n}\nfunction parseAttendees(attendees) {\n    const data = {};\n    if (attendees.length > 0) {\n        // Find the first attendee that's not the organizer\n        const clientAttendee = attendees.find((a)=>!a.organizer && a.email);\n        if (clientAttendee) {\n            data.email = clientAttendee.email;\n            if (clientAttendee.displayName) {\n                data.name = clientAttendee.displayName;\n            }\n        }\n    }\n    return data;\n}\nfunction determinePriority(title, description) {\n    const combined = (title + \" \" + description).toLowerCase();\n    if (combined.includes(\"urgent\") || combined.includes(\"asap\") || combined.includes(\"important\")) {\n        return \"high\";\n    }\n    if (combined.includes(\"follow up\") || combined.includes(\"check in\")) {\n        return \"medium\";\n    }\n    return \"medium\";\n}\nfunction estimateValue(eventType, data) {\n    // Estimate based on type and available data\n    if (eventType === \"appointment\") return 2500;\n    if (eventType === \"follow-up\" && data.phone && data.email) return 1500;\n    if (eventType === \"lead\") return 1000;\n    return 500;\n}\nfunction calculateConfidence(data) {\n    let score = 0;\n    const fields = [\n        \"name\",\n        \"phone\",\n        \"email\",\n        \"address\",\n        \"dob\"\n    ];\n    fields.forEach((field)=>{\n        if (data[field]) score += 20;\n    });\n    return Math.min(score, 100);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/import/parseCalendarEvent.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@clerk","vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/tslib","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/snakecase-keys","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/date-fns","vendor-chunks/googleapis","vendor-chunks/google-auth-library","vendor-chunks/gaxios","vendor-chunks/bignumber.js","vendor-chunks/googleapis-common","vendor-chunks/qs","vendor-chunks/json-bigint","vendor-chunks/google-logging-utils","vendor-chunks/object-inspect","vendor-chunks/gcp-metadata","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/https-proxy-agent","vendor-chunks/gtoken","vendor-chunks/agent-base","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/url-template","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/base64-js","vendor-chunks/side-channel-list","vendor-chunks/extend","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/side-channel-weakmap","vendor-chunks/has-symbols","vendor-chunks/function-bind","vendor-chunks/side-channel-map","vendor-chunks/safe-buffer","vendor-chunks/side-channel","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/buffer-equal-constant-time","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/call-bound","vendor-chunks/is-stream","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcalendar%2Fimport%2Froute&page=%2Fapi%2Fcalendar%2Fimport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcalendar%2Fimport%2Froute.ts&appDir=C%3A%5Ccrm%5Cinsurance-crm%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5Ccrm%5Cinsurance-crm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();