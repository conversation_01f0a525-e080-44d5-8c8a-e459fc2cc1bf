import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { withAPIAuth, APIContext } from '@/lib/api/auth';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * GET /api/v1/clients/[id]
 * Retrieve a specific client by ID
 */
async function handleGET(
  request: NextRequest, 
  context: APIContext,
  { params }: { params: { id: string } }
): Promise<NextResponse> {
  try {
    const { data: client, error } = await supabase
      .from('active_clients')
      .select(`
        id,
        first_name,
        last_name,
        email,
        phone,
        dob,
        birth_date,
        gender,
        address,
        city,
        state,
        zip,
        county,
        status,
        source,
        agent_name,
        agent_npn,
        plan_name,
        carrier,
        metal_level,
        premium,
        gross_premium,
        policy_number,
        notes,
        created_at,
        updated_at,
        policies (
          id,
          plan_name,
          carrier,
          type,
          status,
          premium,
          gross_premium,
          start_date,
          end_date,
          policy_number,
          member_count,
          monthly_commission,
          annual_commission,
          created_at
        ),
        client_notes (
          id,
          note,
          created_at,
          updated_at
        )
      `)
      .eq('id', params.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Client not found' },
          { status: 404 }
        );
      }
      
      console.error('Error fetching client:', error);
      return NextResponse.json(
        { error: 'Failed to fetch client', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: client
    });

  } catch (error) {
    console.error('Client detail API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/v1/clients/[id]
 * Update a specific client
 */
async function handlePUT(
  request: NextRequest,
  context: APIContext,
  { params }: { params: { id: string } }
): Promise<NextResponse> {
  try {
    const body = await request.json();

    // Remove fields that shouldn't be updated via API
    const { id, created_at, updated_at, ...updateData } = body;

    const { data: client, error } = await supabase
      .from('active_clients')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Client not found' },
          { status: 404 }
        );
      }
      
      console.error('Error updating client:', error);
      return NextResponse.json(
        { error: 'Failed to update client', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: client,
      message: 'Client updated successfully'
    });

  } catch (error) {
    console.error('Update client API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/v1/clients/[id]
 * Delete a specific client
 */
async function handleDELETE(
  request: NextRequest,
  context: APIContext,
  { params }: { params: { id: string } }
): Promise<NextResponse> {
  try {
    const { error } = await supabase
      .from('active_clients')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Error deleting client:', error);
      return NextResponse.json(
        { error: 'Failed to delete client', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Client deleted successfully'
    });

  } catch (error) {
    console.error('Delete client API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Create wrapper functions that handle the params
function createHandler(handler: any, permission: 'read' | 'write' | 'delete') {
  return withAPIAuth(async (request: NextRequest, context: APIContext) => {
    // Extract params from the URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];
    
    return handler(request, context, { params: { id } });
  }, permission);
}

// Export the wrapped handlers
export const GET = createHandler(handleGET, 'read');
export const PUT = createHandler(handlePUT, 'write');
export const DELETE = createHandler(handleDELETE, 'delete');
