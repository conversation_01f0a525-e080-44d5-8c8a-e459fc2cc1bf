// app/(dashboard)/calendar/page.tsx
'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PhoneLinkActions } from '@/components/ui/windows-phone-link';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { 
  Calendar as CalendarGrid, 
  Plus, 
  ChevronLeft, 
  ChevronRight,
  Clock,
  MapPin,
  Users,
  Phone,
  Trash2,
  Download,
  AlertCircle,
  CheckCircle,
  Settings,
  RefreshCw,
  Search,
  Filter,
  Mail,
  FileText,
  DollarSign,
  Video,
  Sparkles,
  User2,
  UserPlus
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  format, 
  startOfMonth, 
  endOfMonth, 
  eachDayOfInterval, 
  isSameMonth, 
  isSameDay, 
  addMonths, 
  subMonths, 
  addWeeks,
  subWeeks,
  parseISO, 
  addDays, 
  subDays, 
  startOfWeek, 
  endOfWeek,
  isToday,
  isPast,
  differenceInMinutes,
  parse,
  differenceInHours
} from 'date-fns';
import { supabase } from '@/lib/supabaseClient';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@clerk/nextjs';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { TodaysPriorities } from '@/components/calendar/TodaysPriorities';

// Types
interface CalendarEvent {
  id: string;
  title: string;
  date: Date;
  time: string;
  endTime?: string;
  type: 'meeting' | 'follow-up' | 'renewal' | 'enrollment' | 'appointment' | 'other';
  client?: string;
  notes?: string;
  location?: string;
  phone?: string;
  email?: string;
  lead_id?: string;
  task_id?: string;
  is_imported?: boolean;
  isGoogleEvent?: boolean;
  status?: string;
  attendees?: any[];
  description?: string;
  value?: number;
  priority?: 'low' | 'medium' | 'high';
  isVirtual?: boolean;
}

interface EventStats {
  total: number;
  today: number;
  thisWeek: number;
  appointments: number;
  followUps: number;
  renewals: number;
}

// Constants
const EVENT_TYPES = {
  meeting: { label: 'Meeting', icon: Users, color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' },
  'follow-up': { label: 'Follow-up', icon: Phone, color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' },
  renewal: { label: 'Renewal', icon: RefreshCw, color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' },
  enrollment: { label: 'Enrollment', icon: FileText, color: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400' },
  appointment: { label: 'Appointment', icon: CalendarGrid, color: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400' },
  other: { label: 'Other', icon: Clock, color: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400' }
};

// Helper Functions
const getEventsForHour = (events: CalendarEvent[], date: Date, hour: number) => {
  return events.filter(event => {
    const eventDate = new Date(event.date);
    const eventHour = parseInt(event.time.split(':')[0]);
    return isSameDay(eventDate, date) && eventHour === hour;
  });
};

const getEventsForDayAndHour = (events: CalendarEvent[], day: Date, hour: number) => {
  return events.filter(event => {
    const eventDate = new Date(event.date);
    const eventHour = parseInt(event.time.split(':')[0]);
    return isSameDay(eventDate, day) && eventHour === hour;
  });
};

const getWeekDays = (date: Date) => {
  const start = startOfWeek(date);
  return Array.from({ length: 7 }, (_, i) => addDays(start, i));
};

const getEventColor = (type: string) => {
  const colors = {
    'follow-up': '#FEF3C7',
    'appointment': '#DBEAFE',
    'meeting': '#E0E7FF',
    'renewal': '#D1FAE5',
    'enrollment': '#EDE9FE',
    'other': '#F3F4F6'
  };
  return colors[type as keyof typeof colors] || colors.other;
};

const getDurationInHours = (event: CalendarEvent) => {
  if (!event.endTime) return 1;
  try {
    const start = parse(event.time, 'h:mm a', new Date());
    const end = parse(event.endTime, 'h:mm a', new Date());
    return differenceInHours(end, start) || 1;
  } catch {
    return 1;
  }
};

const getMinuteOffset = (time: string) => {
  try {
    const [, minutes] = time.split(':');
    return parseInt(minutes) || 0;
  } catch {
    return 0;
  }
};

export default function CalendarPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { userId } = useAuth();
  
  // State Management
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [showEventDialog, setShowEventDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);
  const [view, setView] = useState<'month' | 'week' | 'day'>('month');
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [importing, setImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importRange, setImportRange] = useState('30');
  const [calendarConnected, setCalendarConnected] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  
  const [newEvent, setNewEvent] = useState({
    title: '',
    date: selectedDate || new Date(),
    time: '',
    endTime: '',
    type: 'meeting' as CalendarEvent['type'],
    client: '',
    notes: '',
    location: '',
    phone: '',
    email: '',
    priority: 'medium' as CalendarEvent['priority'],
    value: ''
  });

  // Helper Functions
  const detectEventType = useCallback((title: string, description: string = '', location: string = ''): CalendarEvent['type'] => {
    const combined = (title + ' ' + description + ' ' + location).toLowerCase();
    
    if (combined.includes('follow up') || combined.includes('follow-up') || combined.includes('f/u')) {
      return 'follow-up';
    }
    if (combined.includes('enrollment') || combined.includes('enroll') || combined.includes('sign up')) {
      return 'enrollment';
    }
    if (combined.includes('renewal') || combined.includes('renew') || combined.includes('annual')) {
      return 'renewal';
    }
    if (combined.includes('appointment') || combined.includes('meeting') || 
        combined.includes('consultation') || combined.includes('virtual') ||
        combined.includes('virt ') || combined.includes('zoom') || 
        combined.includes('meet') || combined.includes('teams')) {
      return 'appointment';
    }
    return 'other';
  }, []);

  const isVirtualMeeting = useCallback((location: string = '', description: string = ''): boolean => {
    const combined = (location + ' ' + description).toLowerCase();
    return combined.includes('zoom') || combined.includes('meet') || 
           combined.includes('teams') || combined.includes('virtual') || 
           combined.includes('virt ') || combined.includes('online');
  }, []);

  const extractClientInfo = useCallback((event: any): { name?: string; phone?: string; email?: string } => {
    const info: { name?: string; phone?: string; email?: string } = {};
    
    // Extract from title
    const titleMatch = event.summary?.match(/(?:with|for)\s+[-–]\s*([^-–]+?)(?:\s*[-–]|$)/i);
    if (titleMatch) {
      info.name = titleMatch[1].trim();
    }
    
    // Extract from description
    const description = event.description || '';
    
    // Phone number extraction
    const phoneMatch = description.match(/(\d{3}[-.]?\d{3}[-.]?\d{4})/);
    if (phoneMatch) {
      info.phone = phoneMatch[1].replace(/[.-]/g, '-');
    }
    
    // Email extraction
    const emailMatch = description.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
    if (emailMatch) {
      info.email = emailMatch[1];
    }
    
    // Try JSON extraction
    try {
      const jsonMatch = description.match(/\{[^}]+\}/);
      if (jsonMatch) {
        const jsonData = JSON.parse(jsonMatch[0]);
        info.name = jsonData.name || info.name;
        info.phone = jsonData.phone || info.phone;
        info.email = jsonData.email || info.email;
      }
    } catch {
      // JSON parsing failed, continue with other extraction methods
    }
    
    // Extract from attendees
    if (event.attendees?.length > 0) {
      const clientAttendee = event.attendees.find((a: any) => !a.organizer);
      if (clientAttendee) {
        info.name = info.name || clientAttendee.displayName;
        info.email = info.email || clientAttendee.email;
      }
    }
    
    return info;
  }, []);

  const formatPhone = useCallback((phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return cleaned.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    }
    return phone;
  }, []);

  // Memoized Calculations
  const eventStats = useMemo((): EventStats => {
    const today = new Date();
    const weekStart = startOfWeek(today);
    const weekEnd = endOfWeek(today);
    
    return {
      total: events.length,
      today: events.filter(e => isSameDay(e.date, today)).length,
      thisWeek: events.filter(e => e.date >= weekStart && e.date <= weekEnd).length,
      appointments: events.filter(e => e.type === 'appointment').length,
      followUps: events.filter(e => e.type === 'follow-up').length,
      renewals: events.filter(e => e.type === 'renewal').length
    };
  }, [events]);

  const filteredEvents = useMemo(() => {
    return events.filter(event => {
      const matchesSearch = !searchQuery || 
        event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.client?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.notes?.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesFilter = filterType === 'all' || event.type === filterType;
      
      return matchesSearch && matchesFilter;
    });
  }, [events, searchQuery, filterType]);

  const getDaysInMonth = useMemo(() => {
    const start = startOfMonth(currentDate);
    const end = endOfMonth(currentDate);
    const days = eachDayOfInterval({ start, end });
    
    // Add padding days from previous month
    const startDay = start.getDay();
    for (let i = startDay - 1; i >= 0; i--) {
      days.unshift(subDays(start, i + 1));
    }
    
    // Add padding days from next month
    const endDay = end.getDay();
    for (let i = 1; i < 7 - endDay; i++) {
      days.push(addDays(end, i));
    }
    
    return days;
  }, [currentDate]);

  const getEventsForDay = useCallback((date: Date) => {
    return filteredEvents.filter(event => isSameDay(event.date, date))
      .sort((a, b) => {
        const timeA = parseInt(a.time.split(':')[0]);
        const timeB = parseInt(b.time.split(':')[0]);
        return timeA - timeB;
      });
  }, [filteredEvents]);

  const getUpcomingEvents = useMemo(() => {
    const now = new Date();
    return filteredEvents
      .filter(event => event.date >= now)
      .sort((a, b) => a.date.getTime() - b.date.getTime())
      .slice(0, 5);
  }, [filteredEvents]);

  // Initialize and check calendar connection
  useEffect(() => {
    setCalendarConnected(true); // Force connected state
  }, []);

  useEffect(() => {
    if (userId && calendarConnected) {
      fetchEvents();
    }
  }, [userId, calendarConnected, currentDate, view]);

  useEffect(() => {
    if (userId) {
      checkCalendarConnection();
    }
  }, [userId]);

  // Check for openImport parameter and auto-open import dialog
  useEffect(() => {
    const openImport = searchParams.get('openImport');
    if (openImport === 'true') {
      setShowImportDialog(true);
      // Clean up the URL parameter
      const url = new URL(window.location.href);
      url.searchParams.delete('openImport');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams]);

  const checkCalendarConnection = async () => {
    if (!userId) return;
    
    try {
      const { data: settings } = await supabase
        .from('agent_settings')
        .select('google_calendar_connected, google_calendar_token')
        .eq('clerk_user_id', userId)
        .single();

      if (settings) {
        const isConnected = settings.google_calendar_connected === true || !!settings.google_calendar_token;
        setCalendarConnected(isConnected);
      }
    } catch (error) {
      console.error('Error checking calendar connection:', error);
    }
  };

  const fetchEvents = async () => {
    if (!userId) return;
    
    setLoading(true);
    try {
      // Get date range for current view
      let startDate: Date;
      let endDate: Date;
      
      if (view === 'month') {
        startDate = startOfMonth(currentDate);
        endDate = endOfMonth(currentDate);
      } else if (view === 'week') {
        startDate = startOfWeek(currentDate);
        endDate = endOfWeek(currentDate);
      } else {
        startDate = new Date(currentDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999);
      }

      const formattedEvents: CalendarEvent[] = [];

      // First, fetch stored events from database
      try {
        const { data: storedEvents, error } = await supabase
          .from('processed_calendar_events')
          .select('*')
          .eq('clerk_user_id', userId)
          .gte('start_time', startDate.toISOString())
          .lte('start_time', endDate.toISOString());
          
        if (error) {
          console.error('Error fetching stored events:', error);
        } else if (storedEvents) {
          storedEvents.forEach((event: any) => {
            const eventDate = new Date(event.start_time);
            formattedEvents.push({
              id: `stored-${event.id}`,
              title: event.event_title || 'Stored Event',
              date: eventDate,
              time: event.start_time ? format(eventDate, 'h:mm a') : 'All day',
              endTime: event.end_time ? format(new Date(event.end_time), 'h:mm a') : undefined,
              type: (event.classification_type as CalendarEvent['type']) || 'appointment',
              location: event.location,
              description: event.event_description,
              isGoogleEvent: !!event.google_event_id,
              isVirtual: event.is_virtual || false,
              is_imported: true,
              status: event.event_status || 'confirmed'
            });
          });
        }
      } catch (error) {
        console.error('Error fetching stored events:', error);
      }

      // Fetch from Google Calendar if connected
      if (calendarConnected) {
        try {
          const response = await fetch('/api/calendar/events?' + new URLSearchParams({
            timeMin: startDate.toISOString(),
            timeMax: endDate.toISOString()
          }));
          
          if (response.ok) {
            const { events: googleEvents } = await response.json();
            
            if (googleEvents && Array.isArray(googleEvents)) {
              // Store events to database for persistence
              const eventsToStore = googleEvents.map((event: any) => ({
                clerk_user_id: userId,
                google_event_id: event.id,
                event_title: event.summary || 'Untitled Event',
                event_description: event.description || null,
                start_time: event.start?.dateTime || event.start?.date,
                end_time: event.end?.dateTime || event.end?.date,
                location: event.location || null,
                attendees: event.attendees ? JSON.stringify(event.attendees) : null,
                classification_type: detectEventType(
                  event.summary || '', 
                  event.description || '', 
                  event.location || ''
                ),
                event_status: event.status || 'confirmed',
                is_virtual: isVirtualMeeting(event.location || '', event.description || ''),
                processed_at: new Date().toISOString(),
                created_at: event.start?.dateTime || event.start?.date
              }));

              // Store events with conflict resolution (upsert)
              if (eventsToStore.length > 0) {
                try {
                  await supabase
                    .from('processed_calendar_events')
                    .upsert(eventsToStore, { 
                      onConflict: 'google_event_id,clerk_user_id'
                    });
                } catch (storeError) {
                  console.error('Error storing Google Calendar events:', storeError);
                }
              }

              googleEvents.forEach((event: any) => {
                const clientInfo = extractClientInfo(event);
                const eventType = detectEventType(
                  event.summary || '', 
                  event.description || '', 
                  event.location || ''
                );
                const isVirtual = isVirtualMeeting(event.location || '', event.description || '');
                
                formattedEvents.push({
                  id: `google-${event.id}`,
                  title: event.summary || 'Untitled Event',
                  date: new Date(event.start?.dateTime || event.start?.date),
                  time: event.start?.dateTime ? 
                    format(new Date(event.start.dateTime), 'h:mm a') : 'All day',
                  endTime: event.end?.dateTime ? 
                    format(new Date(event.end.dateTime), 'h:mm a') : undefined,
                  type: eventType,
                  location: event.location,
                  description: event.description,
                  attendees: event.attendees,
                  isGoogleEvent: true,
                  client: clientInfo.name,
                  phone: clientInfo.phone,
                  email: clientInfo.email,
                  isVirtual,
                  status: event.status || 'confirmed'
                });
              });
            }
          }
        } catch (error) {
          console.error('Error fetching Google Calendar events:', error);
        }
      }

      // Remove duplicates (prefer Google Calendar live events over stored events)
      const uniqueEvents = new Map<string, CalendarEvent>();
      formattedEvents.forEach(event => {
        const key = event.id.includes('google-') 
          ? event.id.replace('google-', '') 
          : event.id;
        
        // If we already have this event and the current one is from Google Calendar (live), prefer it
        if (uniqueEvents.has(key)) {
          const existing = uniqueEvents.get(key)!;
          if (event.isGoogleEvent && !existing.isGoogleEvent) {
            uniqueEvents.set(key, event);
          }
        } else {
          uniqueEvents.set(key, event);
        }
      });

      const deduplicatedEvents = Array.from(uniqueEvents.values());

      // Sort events by date and time
      deduplicatedEvents.sort((a, b) => {
        const dateCompare = a.date.getTime() - b.date.getTime();
        if (dateCompare !== 0) return dateCompare;
        
        // If same date, sort by time
        const timeA = parseInt(a.time.split(':')[0]);
        const timeB = parseInt(b.time.split(':')[0]);
        return timeA - timeB;
      });
      
      setEvents(deduplicatedEvents);
      
    } catch (error) {
      console.error('Error fetching events:', error);
      toast.error('Failed to load calendar events');
    } finally {
      setLoading(false);
    }
  };

  const handleSyncCalendar = async () => {
    if (!calendarConnected) {
      toast.error('Please connect your Google Calendar first');
      return;
    }

    setSyncing(true);
    try {
      await fetchEvents();
      toast.success('Calendar synced successfully');
    } catch {
      toast.error('Failed to sync calendar');
    } finally {
      setSyncing(false);
    }
  };

  const handleImportCalendar = async () => {
    if (!calendarConnected) {
      toast.error('Please connect your Google Calendar first');
      return;
    }

    setImporting(true);
    setImportProgress(10);
    
    try {
      const days = parseInt(importRange);
      const timeMin = subDays(new Date(), days).toISOString();
      const timeMax = addDays(new Date(), days).toISOString();

      setImportProgress(30);

      const response = await fetch('/api/calendar/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          timeMin,
          timeMax,
          skipPast: true,
          aiParsing: true,
          defaultStatus: 'New Lead',
          defaultSource: 'Google Calendar'
        })
      });

      setImportProgress(70);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Import failed');
      }

      const result = await response.json();
      
      setImportProgress(90);

      // Show detailed results
      toast.success(
        <div>
          <p className="font-semibold">Import completed!</p>
          <p className="text-sm mt-1">
            • {result.imported} events processed<br/>
            • {result.leads} new leads created<br/>
            • {result.appointments} appointments scheduled<br/>
            • {result.followUps} follow-ups created<br/>
            {result.duplicates > 0 && `• ${result.duplicates} duplicates skipped`}
          </p>
        </div>,
        { duration: 8000 }
      );

      setShowImportDialog(false);
      await fetchEvents();

      // Offer to view new leads
      if (result.leads > 0) {
        setTimeout(() => {
          toast(
            <div>
              <p>New leads are ready in your pipeline!</p>
              <Button 
                size="sm" 
                className="mt-2"
                onClick={() => router.push('/leads')}
              >
                View Leads
              </Button>
            </div>,
            { duration: 5000 }
          );
        }, 2000);
      }

    } catch (error: any) {
      console.error('Import error:', error);
      toast.error(error.message || 'Failed to import calendar events');
    } finally {
      setImporting(false);
      setImportProgress(0);
    }
  };

  const handleAddEvent = async () => {
    if (!newEvent.title || !newEvent.time) {
      toast.error('Please provide a title and time');
      return;
    }
    
    try {
      const eventDate = new Date(newEvent.date);
      const [hours, minutes] = newEvent.time.split(':');
      eventDate.setHours(parseInt(hours), parseInt(minutes));

      const newCalendarEvent: CalendarEvent = {
        id: `local-${Date.now()}`,
        title: newEvent.title,
        date: eventDate,
        time: format(eventDate, 'h:mm a'),
        endTime: newEvent.endTime ? format(parseISO(`2000-01-01T${newEvent.endTime}`), 'h:mm a') : undefined,
        type: newEvent.type,
        client: newEvent.client,
        notes: newEvent.notes,
        location: newEvent.location,
        phone: newEvent.phone,
        email: newEvent.email,
        priority: newEvent.priority as CalendarEvent['priority'],
        value: newEvent.value ? parseFloat(newEvent.value) : undefined,
        isVirtual: isVirtualMeeting(newEvent.location)
      };
      
      setEvents([...events, newCalendarEvent]);
      toast.success('Event added successfully');
      
      // Reset form
      setShowEventDialog(false);
      setNewEvent({
        title: '',
        date: selectedDate || new Date(),
        time: '',
        endTime: '',
        type: 'meeting',
        client: '',
        notes: '',
        location: '',
        phone: '',
        email: '',
        priority: 'medium',
        value: ''
      });

    } catch (error) {
      console.error('Error adding event:', error);
      toast.error('Failed to add event');
    }
  };

  const handleQuickCreate = async (date: Date, time: string) => {
    const eventDate = new Date(date);
    const [hours] = time.split(':');
    eventDate.setHours(parseInt(hours), 0);
    
    setNewEvent({
      ...newEvent,
      date: eventDate,
      time: time.substring(0, 5)
    });
    setShowEventDialog(true);
  };

  const handleDeleteEvent = async (eventId: string) => {
    try {
      if (eventId.startsWith('google-')) {
        toast.info('Google Calendar events must be deleted from Google Calendar');
        return;
      }

      setEvents(events.filter(e => e.id !== eventId));
      toast.success('Event removed');
      setSelectedEvent(null);
    } catch (error) {
      console.error('Error deleting event:', error);
      toast.error('Failed to delete event');
    }
  };

  const exportEvents = () => {
    const csvContent = [
      ['Title', 'Date', 'Time', 'Type', 'Client', 'Phone', 'Email', 'Location', 'Notes'].join(','),
      ...filteredEvents.map(event => [
        event.title,
        format(event.date, 'MM/dd/yyyy'),
        event.time,
        event.type,
        event.client || '',
        event.phone || '',
        event.email || '',
        event.location || '',
        event.notes || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `calendar-events-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    toast.success('Events exported successfully');
  };

  // Fix the navigation to work properly for each view
  const handleNavigate = (direction: 'prev' | 'next') => {
    switch (view) {
      case 'day':
        setCurrentDate(direction === 'prev' 
          ? subDays(currentDate, 1) 
          : addDays(currentDate, 1)
        );
        break;
      case 'week':
        setCurrentDate(direction === 'prev'
          ? subWeeks(currentDate, 1)
          : addWeeks(currentDate, 1)
        );
        break;
      case 'month':
        setCurrentDate(direction === 'prev'
          ? subMonths(currentDate, 1)
          : addMonths(currentDate, 1)
        );
        break;
    }
  };

  // Get proper title for current view
  const getViewTitle = () => {
    switch (view) {
      case 'day':
        return format(currentDate, 'EEEE, MMMM d, yyyy');
      case 'week':
        const weekStart = startOfWeek(currentDate);
        const weekEnd = endOfWeek(currentDate);
        return `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d, yyyy')}`;
      case 'month':
        return format(currentDate, 'MMMM yyyy');
      default:
        return format(currentDate, 'MMMM yyyy');
    }
  };

  // Handle clicking on calendar days
  const handleDayClick = (day: Date) => {
    setSelectedDate(day);
    setCurrentDate(day);
    setView('day');
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Calendar</h1>
          <p className="text-muted-foreground">
            Manage appointments and sync with Google Calendar
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          {!calendarConnected && (
            <Link href="/settings?tab=integrations">
              <Button variant="outline" className="text-orange-600 border-orange-600">
                <Settings className="mr-2 h-4 w-4" />
                Connect Calendar
              </Button>
            </Link>
          )}
          {calendarConnected && (
            <>
              <Button 
                variant="outline" 
                onClick={handleSyncCalendar}
                disabled={syncing}
              >
                <RefreshCw className={cn("mr-2 h-4 w-4", syncing && "animate-spin")} />
                Sync
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setShowImportDialog(true)}
              >
                <Download className="mr-2 h-4 w-4" />
                Import
              </Button>
            </>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                More
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={exportEvents}>
                <Download className="mr-2 h-4 w-4" />
                Export Events
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setView('month')}>
                <CalendarGrid className="mr-2 h-4 w-4" />
                Month View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setView('week')}>
                <CalendarGrid className="mr-2 h-4 w-4" />
                Week View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setView('day')}>
                <CalendarGrid className="mr-2 h-4 w-4" />
                Day View
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={() => { setSelectedDate(new Date()); setShowEventDialog(true); }}>
            <Plus className="mr-2 h-4 w-4" />
            Add Event
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Events</p>
                <p className="text-2xl font-bold">{eventStats.total}</p>
              </div>
              <CalendarGrid className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Today</p>
                <p className="text-2xl font-bold">{eventStats.today}</p>
              </div>
              <Clock className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">This Week</p>
                <p className="text-2xl font-bold">{eventStats.thisWeek}</p>
              </div>
              <CalendarGrid className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Appointments</p>
                <p className="text-2xl font-bold">{eventStats.appointments}</p>
              </div>
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Follow-ups</p>
                <p className="text-2xl font-bold">{eventStats.followUps}</p>
              </div>
              <Phone className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Renewals</p>
                <p className="text-2xl font-bold">{eventStats.renewals}</p>
              </div>
              <RefreshCw className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                className="px-3 py-2 border rounded-md"
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
              >
                <option value="all">All Types</option>
                {Object.entries(EVENT_TYPES).map(([key, type]) => (
                  <option key={key} value={key}>{type.label}</option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Calendar not connected warning */}
      {!calendarConnected && (
        <Card className="border-amber-200 bg-amber-50 dark:bg-amber-950/20">
          <CardContent className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-amber-600" />
              <div>
                <p className="font-medium">Google Calendar not connected</p>
                <p className="text-sm text-muted-foreground">
                  Connect your calendar to import events and sync appointments
                </p>
              </div>
            </div>
            <Link href="/settings?tab=integrations">
              <Button size="sm">Connect Now</Button>
            </Link>
          </CardContent>
        </Card>
      )}

      {/* Today's Priorities - Show only in day view */}
      {view === 'day' && userId && <TodaysPriorities userId={userId} />}

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        {/* Calendar View */}
        <Card className="xl:col-span-3">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2">
                <CalendarGrid className="h-5 w-5" />
                {getViewTitle()}
              </CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleNavigate('prev')}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentDate(new Date())}
                >
                  Today
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleNavigate('next')}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                <div className="grid grid-cols-7 gap-2">
                  {Array.from({ length: 35 }).map((_, i) => (
                    <Skeleton key={i} className="h-24 w-full" />
                  ))}
                </div>
              </div>
            ) : (
              <Tabs value={view} onValueChange={(v) => setView(v as 'month' | 'week' | 'day')}>
                <TabsList className="mb-4">
                  <TabsTrigger value="month">Month</TabsTrigger>
                  <TabsTrigger value="week">Week</TabsTrigger>
                  <TabsTrigger value="day">Day</TabsTrigger>
                </TabsList>
                
                <TabsContent value="month" className="mt-0">
                  {/* Calendar Grid */}
                  <div className="grid grid-cols-7 gap-px bg-muted rounded-lg overflow-hidden">
                    {/* Day Headers */}
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                      <div key={day} className="bg-background p-2 text-center text-sm font-medium">
                        {day}
                      </div>
                    ))}
                    
                    {/* Calendar Days */}
                    {getDaysInMonth.map((day) => {
                      const dayEvents = getEventsForDay(day);
                      const isCurrentDay = isToday(day);
                      const isSelected = selectedDate && isSameDay(day, selectedDate);
                      const isPastDay = isPast(day) && !isCurrentDay;
                      
                      return (
                        <div
                          key={day.toString()}
                          className={cn(
                            "min-h-[120px] bg-background p-2 cursor-pointer hover:bg-muted/50 transition-colors relative group",
                            isCurrentDay && "bg-blue-50 dark:bg-blue-950/20 ring-2 ring-blue-500",
                            isSelected && !isCurrentDay && "ring-2 ring-primary",
                            !isSameMonth(day, currentDate) && "opacity-50",
                            isPastDay && "bg-gray-50 dark:bg-gray-950/20"
                          )}
                          onClick={() => handleDayClick(day)}
                          onDoubleClick={() => handleQuickCreate(day, '09:00')}
                        >
                          <div className="flex justify-between items-start mb-1">
                            <span className={cn(
                              "text-sm font-medium",
                              !isSameMonth(day, currentDate) && "text-muted-foreground",
                              isCurrentDay && "text-blue-600 dark:text-blue-400"
                            )}>
                              {format(day, 'd')}
                            </span>
                            {dayEvents.length > 0 && (
                              <Badge variant="secondary" className="text-xs">
                                {dayEvents.length}
                              </Badge>
                            )}
                          </div>
                          
                          {/* Event Pills */}
                          <div className="space-y-1">
                            {dayEvents.slice(0, 3).map(event => {
                              const EventIcon = EVENT_TYPES[event.type].icon;
                              return (
                                <div
                                  key={event.id}
                                  className={cn(
                                    "text-xs px-2 py-1 rounded cursor-pointer flex items-center gap-1 transition-all hover:scale-105",
                                    EVENT_TYPES[event.type].color
                                  )}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSelectedEvent(event);
                                  }}
                                >
                                  <EventIcon className="h-3 w-3 flex-shrink-0" />
                                  {event.isGoogleEvent && <CalendarGrid className="h-3 w-3 flex-shrink-0" />}
                                  {event.isVirtual && <Video className="h-3 w-3 flex-shrink-0" />}
                                  <span className="truncate">
                                    {event.time} - {event.title}
                                  </span>
                                </div>
                              );
                            })}
                            {dayEvents.length > 3 && (
                              <div className="text-xs text-muted-foreground text-center">
                                +{dayEvents.length - 3} more
                              </div>
                            )}
                          </div>
                          
                          {/* Quick Create on Hover */}
                          <div className="absolute inset-0 bg-black/5 dark:bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                            <Plus className="h-6 w-6 text-muted-foreground" />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </TabsContent>
                
                <TabsContent value="week" className="mt-0">
                  <div className="grid grid-cols-1 lg:grid-cols-8 gap-0 border rounded-lg overflow-hidden">
                    {/* Mobile week navigation */}
                    <div className="lg:hidden mb-4 p-4 border rounded-lg">
                      <h3 className="font-semibold mb-3">Week Navigation</h3>
                      <div className="grid grid-cols-7 gap-1">
                        {getWeekDays(currentDate).map((day) => (
                          <Button
                            key={day.toString()}
                            variant={isSameDay(day, selectedDate || currentDate) ? "default" : "outline"}
                            size="sm"
                            className="flex flex-col p-2 h-auto"
                            onClick={() => handleDayClick(day)}
                          >
                            <div className="text-xs">{format(day, 'EEE')}</div>
                            <div className="text-sm font-semibold">{format(day, 'd')}</div>
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Time column - hidden on mobile */}
                    <div className="hidden lg:block border-r bg-muted/30">
                      <div className="h-12 border-b"></div> {/* Header spacer */}
                      {Array.from({ length: 24 }, (_, hour) => (
                        <div key={hour} className="h-16 text-xs text-muted-foreground pr-2 text-right pt-1 border-b">
                          {format(new Date().setHours(hour, 0), 'h a')}
                        </div>
                      ))}
                    </div>

                    {/* Days columns */}
                    <div className="lg:contents grid grid-cols-1 gap-4 lg:gap-0">
                      {getWeekDays(currentDate).map((day) => (
                        <div key={day.toString()} className="border-r last:border-r-0">
                          {/* Day header */}
                          <div className={cn(
                            "h-12 border-b p-2 text-center cursor-pointer hover:bg-muted/50 transition-colors",
                            isToday(day) && "bg-blue-50 dark:bg-blue-950/20"
                          )}
                          onClick={() => handleDayClick(day)}>
                            <div className="text-xs font-medium">{format(day, 'EEE')}</div>
                            <div className={cn(
                              "text-lg font-semibold",
                              isToday(day) && "text-blue-600"
                            )}>
                              {format(day, 'd')}
                            </div>
                          </div>

                          {/* Hour slots - only show on desktop */}
                          <div className="relative hidden lg:block">
                            {Array.from({ length: 24 }, (_, hour) => {
                              const hourEvents = getEventsForDayAndHour(filteredEvents, day, hour);
                              return (
                                <div
                                  key={hour}
                                  className="h-16 border-b hover:bg-muted/50 cursor-pointer relative group"
                                  onClick={() => handleQuickCreate(day, `${hour}:00`)}
                                >
                                  {/* Events */}
                                  {hourEvents.map((event, index) => (
                                    <div
                                      key={event.id}
                                      className="absolute left-1 right-1 p-1 rounded text-xs cursor-pointer shadow-sm border transition-all hover:shadow-md"
                                      style={{
                                        top: `${getMinuteOffset(event.time) / 4}px`,
                                        height: `${Math.min(getDurationInHours(event) * 16, 60)}px`,
                                        backgroundColor: getEventColor(event.type),
                                        zIndex: 10 + index
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setSelectedEvent(event);
                                      }}
                                    >
                                      <div className="font-medium truncate">{event.title}</div>
                                      <div className="text-xs opacity-75 truncate">{event.time}</div>
                                    </div>
                                  ))}
                                </div>
                              );
                            })}
                          </div>

                          {/* Mobile day events list */}
                          <div className="lg:hidden p-2 space-y-1">
                            {filteredEvents
                              .filter(event => isSameDay(new Date(event.date), day))
                              .slice(0, 3)
                              .map(event => (
                                <div
                                  key={event.id}
                                  className="p-2 rounded text-xs cursor-pointer border"
                                  style={{ backgroundColor: getEventColor(event.type) }}
                                  onClick={() => setSelectedEvent(event)}
                                >
                                  <div className="font-medium truncate">{event.title}</div>
                                  <div className="opacity-75">{event.time}</div>
                                </div>
                              ))}
                            {filteredEvents.filter(event => isSameDay(new Date(event.date), day)).length > 3 && (
                              <div className="text-xs text-muted-foreground text-center py-1">
                                +{filteredEvents.filter(event => isSameDay(new Date(event.date), day)).length - 3} more
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="day" className="mt-0">
                  <div className="grid grid-cols-[80px_1fr] gap-0 border rounded-lg overflow-hidden">
                    {/* Time column */}
                    <div className="border-r bg-muted/30">
                      <div className="h-12 border-b"></div> {/* Header spacer */}
                      {Array.from({ length: 24 }, (_, hour) => (
                        <div key={hour} className="h-16 text-xs text-muted-foreground pr-2 text-right pt-1 border-b">
                          {format(new Date().setHours(hour, 0), 'h a')}
                        </div>
                      ))}
                    </div>

                    {/* Events column */}
                    <div className="relative">
                      {/* Day header */}
                      <div className={cn(
                        "h-12 border-b p-3 bg-background",
                        isToday(selectedDate || currentDate) && "bg-blue-50 dark:bg-blue-950/20"
                      )}>
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-sm font-medium">
                              {format(selectedDate || currentDate, 'EEEE')}
                            </div>
                            <div className={cn(
                              "text-lg font-semibold",
                              isToday(selectedDate || currentDate) && "text-blue-600"
                            )}>
                              {format(selectedDate || currentDate, 'MMMM d, yyyy')}
                            </div>
                          </div>
                          <Badge variant="outline">
                            {getEventsForDay(selectedDate || currentDate).length} events
                          </Badge>
                        </div>
                      </div>

                      {/* Hour slots */}
                      {Array.from({ length: 24 }, (_, hour) => {
                        const hourEvents = getEventsForHour(filteredEvents, selectedDate || currentDate, hour);
                        return (
                          <div
                            key={hour}
                            className="h-16 border-b hover:bg-muted/50 cursor-pointer relative group"
                            onClick={() => handleQuickCreate(selectedDate || currentDate, `${hour}:00`)}
                          >
                            {/* Quick create hint */}
                            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                              <div className="text-xs text-muted-foreground bg-background px-2 py-1 rounded border shadow-sm">
                                Click to create event at {format(new Date().setHours(hour, 0), 'h a')}
                              </div>
                            </div>

                            {/* Events for this hour */}
                            {hourEvents.map((event, index) => (
                              <div
                                key={event.id}
                                className="absolute left-2 right-2 p-2 rounded-md cursor-pointer shadow-sm border transition-all hover:shadow-md"
                                style={{
                                  top: `${getMinuteOffset(event.time)}px`,
                                  height: `${Math.min(getDurationInHours(event) * 64, 60)}px`,
                                  backgroundColor: getEventColor(event.type),
                                  zIndex: 10 + index
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedEvent(event);
                                }}
                              >
                                <div className="text-xs font-medium truncate">{event.time}</div>
                                <div className="text-sm truncate font-medium">{event.title}</div>
                                {event.client && (
                                  <div className="text-xs text-muted-foreground truncate">{event.client}</div>
                                )}
                                {event.location && (
                                  <div className="text-xs text-muted-foreground truncate flex items-center gap-1">
                                    <MapPin className="h-3 w-3" />
                                    {event.location}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            )}

            {/* Event Type Legend */}
            <div className="mt-4 p-4 border rounded-lg bg-muted/30">
              <h3 className="text-sm font-semibold mb-3">Event Types</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
                {Object.entries(EVENT_TYPES).map(([type, config]) => {
                  const Icon = config.icon;
                  return (
                    <div key={type} className="flex items-center gap-2 text-xs">
                      <div
                        className="w-3 h-3 rounded"
                        style={{ backgroundColor: getEventColor(type) }}
                      />
                      <Icon className="h-3 w-3" />
                      <span className="truncate">{config.label}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Events Sidebar */}
        <Card className="xl:col-span-1">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              Upcoming Events
            </CardTitle>
            <CardDescription>Your next appointments</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {getUpcomingEvents.length === 0 ? (
              <div className="text-center py-8">
                <CalendarGrid className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
                <p className="text-sm text-muted-foreground">
                  {calendarConnected 
                    ? 'No upcoming events scheduled' 
                    : 'Connect your calendar to see events'}
                </p>
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="mt-3"
                  onClick={() => setShowEventDialog(true)}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Schedule Event
                </Button>
              </div>
            ) : (
              <>
                {getUpcomingEvents.map(event => {
                  const EventIcon = EVENT_TYPES[event.type].icon;
                  const isOverdue = isPast(event.date) && !isToday(event.date);
                  
                  return (
                    <div
                      key={event.id}
                      className={cn(
                        "p-3 rounded-lg border hover:shadow-md cursor-pointer transition-all",
                        isOverdue && "border-red-200 bg-red-50 dark:bg-red-950/20"
                      )}
                      onClick={() => setSelectedEvent(event)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center gap-2">
                          <EventIcon className="h-4 w-4 text-muted-foreground" />
                          {event.isGoogleEvent && (
                            <CalendarGrid className="h-3 w-3 text-muted-foreground" />
                          )}
                          {event.isVirtual && (
                            <Video className="h-3 w-3 text-blue-600" />
                          )}
                        </div>
                        <Badge 
                          variant="secondary" 
                          className={cn("text-xs", EVENT_TYPES[event.type].color)}
                        >
                          {EVENT_TYPES[event.type].label}
                        </Badge>
                      </div>
                      
                      <h4 className="font-medium text-sm mb-1 line-clamp-2">
                        {event.title}
                      </h4>
                      
                      <div className="space-y-1 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <CalendarGrid className="h-3 w-3" />
                          <span className={cn(isOverdue && "text-red-600 font-medium")}>
                            {isToday(event.date) 
                              ? 'Today' 
                              : format(event.date, 'MMM d, yyyy')}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{event.time}</span>
                          {event.endTime && <span>- {event.endTime}</span>}
                        </div>
                        {event.client && (
                          <div className="flex items-center gap-1">
                            <User2 className="h-3 w-3" />
                            <span className="truncate">{event.client}</span>
                          </div>
                        )}
                        {event.location && (
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            <span className="truncate">{event.location}</span>
                          </div>
                        )}
                        {event.value && (
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3" />
                            <span>${event.value.toLocaleString()}</span>
                          </div>
                        )}
                        {event.priority && event.priority !== 'medium' && (
                          <Badge 
                            variant={event.priority === 'high' ? 'destructive' : 'secondary'}
                            className="text-xs"
                          >
                            {event.priority} priority
                          </Badge>
                        )}
                      </div>
                    </div>
                  );
                })}
                
                <Separator className="my-4" />
                
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => router.push('/tasks')}
                >
                  View All Tasks
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Import Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Import Google Calendar Events
            </DialogTitle>
            <DialogDescription>
              Import events and convert them to leads or tasks using AI
            </DialogDescription>
          </DialogHeader>
          
          {importing && importProgress > 0 ? (
            <div className="space-y-4 py-4">
              <Progress value={importProgress} className="h-2" />
              <p className="text-sm text-center text-muted-foreground">
                {importProgress < 30 && "Connecting to Google Calendar..."}
                {importProgress >= 30 && importProgress < 70 && "Analyzing events with AI..."}
                {importProgress >= 70 && "Creating leads and tasks..."}
              </p>
            </div>
          ) : (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label>Import Range</Label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={importRange}
                  onChange={(e) => setImportRange(e.target.value)}
                >
                  <option value="7">Past 7 days & Next 7 days</option>
                  <option value="14">Past 14 days & Next 14 days</option>
                  <option value="30">Past 30 days & Next 30 days</option>
                  <option value="60">Past 60 days & Next 60 days</option>
                  <option value="90">Past 90 days & Next 90 days</option>
                </select>
              </div>

              <div className="rounded-lg bg-blue-50 dark:bg-blue-950/20 p-4 space-y-2">
                <p className="text-sm font-medium flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-blue-600" />
                  AI-Powered Import Features:
                </p>
                <ul className="text-sm text-muted-foreground space-y-1 ml-6">
                  <li>• Automatically extracts contact info from descriptions</li>
                  <li>• Detects follow-ups and appointments</li>
                  <li>• Identifies virtual meetings</li>
                  <li>• Parses phone numbers and emails</li>
                  <li>• Creates prioritized tasks</li>
                  <li>• Prevents duplicate imports</li>
                </ul>
              </div>

              <div className="rounded-lg bg-amber-50 dark:bg-amber-950/20 p-4">
                <p className="text-sm flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                  <span>
                    Events with structured data will be parsed automatically.
                    The AI extracts names, contacts, and creates actionable items.
                  </span>
                </p>
              </div>
            </div>
          )}
          
          <DialogFooter>
            {!importing && (
              <>
                <Button variant="outline" onClick={() => setShowImportDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleImportCalendar}>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Import Events
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add/Edit Event Dialog */}
      <Dialog open={showEventDialog} onOpenChange={setShowEventDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedEvent ? 'Edit Event' : 'Schedule New Event'}
            </DialogTitle>
            <DialogDescription>
              Add details for your appointment or task
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Title*</Label>
                <Input
                  value={newEvent.title}
                  onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
                  placeholder="e.g., Annual Review - Smith Family"
                />
              </div>
              
              <div className="space-y-2">
                <Label>Type</Label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={newEvent.type}
                  onChange={(e) => setNewEvent({ ...newEvent, type: e.target.value as CalendarEvent['type'] })}
                >
                  {Object.entries(EVENT_TYPES).map(([key, type]) => (
                    <option key={key} value={key}>{type.label}</option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Date*</Label>
                <Input
                  type="date"
                  value={format(newEvent.date, 'yyyy-MM-dd')}
                  onChange={(e) => setNewEvent({ ...newEvent, date: parseISO(e.target.value) })}
                />
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-2">
                  <Label>Start Time*</Label>
                  <Input
                    type="time"
                    value={newEvent.time}
                    onChange={(e) => setNewEvent({ ...newEvent, time: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label>End Time</Label>
                  <Input
                    type="time"
                    value={newEvent.endTime}
                    onChange={(e) => setNewEvent({ ...newEvent, endTime: e.target.value })}
                  />
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Client Name</Label>
                <Input
                  value={newEvent.client}
                  onChange={(e) => setNewEvent({ ...newEvent, client: e.target.value })}
                  placeholder="John Smith"
                />
              </div>
              
              <div className="space-y-2">
                <Label>Phone</Label>
                <Input
                  value={newEvent.phone}
                  onChange={(e) => setNewEvent({ ...newEvent, phone: formatPhone(e.target.value) })}
                  placeholder="(*************"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Email</Label>
                <Input
                  type="email"
                  value={newEvent.email}
                  onChange={(e) => setNewEvent({ ...newEvent, email: e.target.value })}
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div className="space-y-2">
                <Label>Location</Label>
                <Input
                  value={newEvent.location}
                  onChange={(e) => setNewEvent({ ...newEvent, location: e.target.value })}
                  placeholder="Office, Zoom, Client's home"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Priority</Label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={newEvent.priority}
                  onChange={(e) => setNewEvent({ ...newEvent, priority: e.target.value as CalendarEvent['priority'] })}
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>
              
              <div className="space-y-2">
                <Label>Estimated Value</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="number"
                    value={newEvent.value}
                    onChange={(e) => setNewEvent({ ...newEvent, value: e.target.value })}
                    placeholder="0.00"
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Notes</Label>
              <textarea
                className="w-full p-2 border rounded-md min-h-[100px]"
                rows={4}
                value={newEvent.notes}
                onChange={(e) => setNewEvent({ ...newEvent, notes: e.target.value })}
                placeholder="Additional notes or details..."
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEventDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddEvent}>
              <CheckCircle className="mr-2 h-4 w-4" />
              {selectedEvent ? 'Update Event' : 'Add Event'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Event Detail Dialog */}
      {selectedEvent && (
        <Dialog open={!!selectedEvent} onOpenChange={() => setSelectedEvent(null)}>
          <DialogContent className="max-h-[90vh] overflow-hidden flex flex-col max-w-2xl">
            <DialogHeader>
              <div className="flex items-start justify-between pr-6">
                <div className="space-y-2">
                  <DialogTitle className="flex items-center gap-2 text-xl">
                    {selectedEvent.isGoogleEvent && (
                      <CalendarGrid className="h-5 w-5 text-muted-foreground" />
                    )}
                    {selectedEvent.isVirtual && (
                      <Video className="h-5 w-5 text-blue-600" />
                    )}
                    <span className="line-clamp-2">{selectedEvent.title}</span>
                  </DialogTitle>
                  <div className="flex items-center gap-2">
                    <Badge className={cn("text-xs", EVENT_TYPES[selectedEvent.type].color)}>
                      {EVENT_TYPES[selectedEvent.type].label}
                    </Badge>
                    {selectedEvent.priority && selectedEvent.priority !== 'medium' && (
                      <Badge 
                        variant={selectedEvent.priority === 'high' ? 'destructive' : 'secondary'}
                        className="text-xs"
                      >
                        {selectedEvent.priority} priority
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </DialogHeader>

            <div className="flex-1 overflow-y-auto px-6 pb-6 space-y-4">
              <div>
                <h3 className="font-semibold">Date & Time</h3>
                <p className="flex items-center gap-2 text-sm">
                  <CalendarGrid className="h-4 w-4 text-muted-foreground" />
                  {format(selectedEvent.date, 'EEEE, MMMM d, yyyy')}
                </p>
                <p className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  {selectedEvent.time}
                  {selectedEvent.endTime && ` - ${selectedEvent.endTime}`}
                </p>
              </div>
              
              <Separator />
              
              {(selectedEvent.client || selectedEvent.phone || selectedEvent.email) && (
                <div>
                  <h3 className="font-semibold">Contact</h3>
                  {selectedEvent.client && <p>{selectedEvent.client}</p>}
                  {selectedEvent.phone && <p>{formatPhone(selectedEvent.phone)}</p>}
                  {selectedEvent.email && <p>{selectedEvent.email}</p>}
                </div>
              )}
              
              {selectedEvent.location && (
                <div>
                  <h3 className="font-semibold">Location</h3>
                  <p>{selectedEvent.location}</p>
                </div>
              )}
              
              {selectedEvent.value && (
                <div>
                  <h3 className="font-semibold">Estimated Value</h3>
                  <p>${selectedEvent.value.toLocaleString()}</p>
                </div>
              )}
              
              {(selectedEvent.notes || selectedEvent.description) && (
                <div>
                  <h3 className="font-semibold">Notes</h3>
                  <p className="whitespace-pre-wrap break-words">
                    {selectedEvent.notes || selectedEvent.description}
                  </p>
                </div>
              )}
              
              {/* Source Info */}
              <div className="pt-2 space-y-1">
                {selectedEvent.isGoogleEvent && (
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <CalendarGrid className="h-3 w-3" />
                    This event is from your Google Calendar
                  </p>
                )}
                {selectedEvent.is_imported && (
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <Download className="h-3 w-3" />
                    This event was imported from Google Calendar
                  </p>
                )}
              </div>
            </div>
            
            <DialogFooter className="flex-shrink-0 border-t pt-4 flex-col sm:flex-row gap-2">
              <div className="flex gap-2 w-full">
                {!selectedEvent.isGoogleEvent && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDeleteEvent(selectedEvent.id)}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete
                  </Button>
                )}
                {selectedEvent.client && !selectedEvent.lead_id && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Create lead from event
                      router.push(`/leads/new?name=${encodeURIComponent(selectedEvent.client || '')}&phone=${encodeURIComponent(selectedEvent.phone || '')}&email=${encodeURIComponent(selectedEvent.email || '')}`);
                    }}
                  >
                    <UserPlus className="h-4 w-4 mr-1" />
                    Create Lead
                  </Button>
                )}
                {selectedEvent.task_id && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push(`/tasks/${selectedEvent.task_id}`)}
                  >
                    View Task
                  </Button>
                )}
              </div>
              <Button variant="outline" onClick={() => setSelectedEvent(null)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}