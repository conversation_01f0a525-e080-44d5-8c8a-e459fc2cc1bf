"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gtoken";
exports.ids = ["vendor-chunks/gtoken"];
exports.modules = {

/***/ "(rsc)/./node_modules/gtoken/build/src/index.js":
/*!************************************************!*\
  !*** ./node_modules/gtoken/build/src/index.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Distributed under MIT license.\n * See file LICENSE for detail or copy at https://opensource.org/licenses/MIT\n */\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar _GoogleToken_instances, _GoogleToken_inFlightRequest, _GoogleToken_getTokenAsync, _GoogleToken_getTokenAsyncInner, _GoogleToken_ensureEmail, _GoogleToken_revokeTokenAsync, _GoogleToken_configure, _GoogleToken_requestToken;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GoogleToken = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/src/index.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst util_1 = __webpack_require__(/*! util */ \"util\");\nconst readFile = fs.readFile\n    ? (0, util_1.promisify)(fs.readFile)\n    : async () => {\n        // if running in the web-browser, fs.readFile may not have been shimmed.\n        throw new ErrorWithCode('use key rather than keyFile.', 'MISSING_CREDENTIALS');\n    };\nconst GOOGLE_TOKEN_URL = 'https://www.googleapis.com/oauth2/v4/token';\nconst GOOGLE_REVOKE_TOKEN_URL = 'https://accounts.google.com/o/oauth2/revoke?token=';\nclass ErrorWithCode extends Error {\n    constructor(message, code) {\n        super(message);\n        this.code = code;\n    }\n}\nclass GoogleToken {\n    get accessToken() {\n        return this.rawToken ? this.rawToken.access_token : undefined;\n    }\n    get idToken() {\n        return this.rawToken ? this.rawToken.id_token : undefined;\n    }\n    get tokenType() {\n        return this.rawToken ? this.rawToken.token_type : undefined;\n    }\n    get refreshToken() {\n        return this.rawToken ? this.rawToken.refresh_token : undefined;\n    }\n    /**\n     * Create a GoogleToken.\n     *\n     * @param options  Configuration object.\n     */\n    constructor(options) {\n        _GoogleToken_instances.add(this);\n        this.transporter = {\n            request: opts => (0, gaxios_1.request)(opts),\n        };\n        _GoogleToken_inFlightRequest.set(this, void 0);\n        __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_configure).call(this, options);\n    }\n    /**\n     * Returns whether the token has expired.\n     *\n     * @return true if the token has expired, false otherwise.\n     */\n    hasExpired() {\n        const now = new Date().getTime();\n        if (this.rawToken && this.expiresAt) {\n            return now >= this.expiresAt;\n        }\n        else {\n            return true;\n        }\n    }\n    /**\n     * Returns whether the token will expire within eagerRefreshThresholdMillis\n     *\n     * @return true if the token will be expired within eagerRefreshThresholdMillis, false otherwise.\n     */\n    isTokenExpiring() {\n        var _a;\n        const now = new Date().getTime();\n        const eagerRefreshThresholdMillis = (_a = this.eagerRefreshThresholdMillis) !== null && _a !== void 0 ? _a : 0;\n        if (this.rawToken && this.expiresAt) {\n            return this.expiresAt <= now + eagerRefreshThresholdMillis;\n        }\n        else {\n            return true;\n        }\n    }\n    getToken(callback, opts = {}) {\n        if (typeof callback === 'object') {\n            opts = callback;\n            callback = undefined;\n        }\n        opts = Object.assign({\n            forceRefresh: false,\n        }, opts);\n        if (callback) {\n            const cb = callback;\n            __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_getTokenAsync).call(this, opts).then(t => cb(null, t), callback);\n            return;\n        }\n        return __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_getTokenAsync).call(this, opts);\n    }\n    /**\n     * Given a keyFile, extract the key and client email if available\n     * @param keyFile Path to a json, pem, or p12 file that contains the key.\n     * @returns an object with privateKey and clientEmail properties\n     */\n    async getCredentials(keyFile) {\n        const ext = path.extname(keyFile);\n        switch (ext) {\n            case '.json': {\n                const key = await readFile(keyFile, 'utf8');\n                const body = JSON.parse(key);\n                const privateKey = body.private_key;\n                const clientEmail = body.client_email;\n                if (!privateKey || !clientEmail) {\n                    throw new ErrorWithCode('private_key and client_email are required.', 'MISSING_CREDENTIALS');\n                }\n                return { privateKey, clientEmail };\n            }\n            case '.der':\n            case '.crt':\n            case '.pem': {\n                const privateKey = await readFile(keyFile, 'utf8');\n                return { privateKey };\n            }\n            case '.p12':\n            case '.pfx': {\n                throw new ErrorWithCode('*.p12 certificates are not supported after v6.1.2. ' +\n                    'Consider utilizing *.json format or converting *.p12 to *.pem using the OpenSSL CLI.', 'UNKNOWN_CERTIFICATE_TYPE');\n            }\n            default:\n                throw new ErrorWithCode('Unknown certificate type. Type is determined based on file extension. ' +\n                    'Current supported extensions are *.json, and *.pem.', 'UNKNOWN_CERTIFICATE_TYPE');\n        }\n    }\n    revokeToken(callback) {\n        if (callback) {\n            __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_revokeTokenAsync).call(this).then(() => callback(), callback);\n            return;\n        }\n        return __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_revokeTokenAsync).call(this);\n    }\n}\nexports.GoogleToken = GoogleToken;\n_GoogleToken_inFlightRequest = new WeakMap(), _GoogleToken_instances = new WeakSet(), _GoogleToken_getTokenAsync = async function _GoogleToken_getTokenAsync(opts) {\n    if (__classPrivateFieldGet(this, _GoogleToken_inFlightRequest, \"f\") && !opts.forceRefresh) {\n        return __classPrivateFieldGet(this, _GoogleToken_inFlightRequest, \"f\");\n    }\n    try {\n        return await (__classPrivateFieldSet(this, _GoogleToken_inFlightRequest, __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_getTokenAsyncInner).call(this, opts), \"f\"));\n    }\n    finally {\n        __classPrivateFieldSet(this, _GoogleToken_inFlightRequest, undefined, \"f\");\n    }\n}, _GoogleToken_getTokenAsyncInner = async function _GoogleToken_getTokenAsyncInner(opts) {\n    if (this.isTokenExpiring() === false && opts.forceRefresh === false) {\n        return Promise.resolve(this.rawToken);\n    }\n    if (!this.key && !this.keyFile) {\n        throw new Error('No key or keyFile set.');\n    }\n    if (!this.key && this.keyFile) {\n        const creds = await this.getCredentials(this.keyFile);\n        this.key = creds.privateKey;\n        this.iss = creds.clientEmail || this.iss;\n        if (!creds.clientEmail) {\n            __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_ensureEmail).call(this);\n        }\n    }\n    return __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_requestToken).call(this);\n}, _GoogleToken_ensureEmail = function _GoogleToken_ensureEmail() {\n    if (!this.iss) {\n        throw new ErrorWithCode('email is required.', 'MISSING_CREDENTIALS');\n    }\n}, _GoogleToken_revokeTokenAsync = async function _GoogleToken_revokeTokenAsync() {\n    if (!this.accessToken) {\n        throw new Error('No token to revoke.');\n    }\n    const url = GOOGLE_REVOKE_TOKEN_URL + this.accessToken;\n    await this.transporter.request({\n        url,\n        retry: true,\n    });\n    __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_configure).call(this, {\n        email: this.iss,\n        sub: this.sub,\n        key: this.key,\n        keyFile: this.keyFile,\n        scope: this.scope,\n        additionalClaims: this.additionalClaims,\n    });\n}, _GoogleToken_configure = function _GoogleToken_configure(options = {}) {\n    this.keyFile = options.keyFile;\n    this.key = options.key;\n    this.rawToken = undefined;\n    this.iss = options.email || options.iss;\n    this.sub = options.sub;\n    this.additionalClaims = options.additionalClaims;\n    if (typeof options.scope === 'object') {\n        this.scope = options.scope.join(' ');\n    }\n    else {\n        this.scope = options.scope;\n    }\n    this.eagerRefreshThresholdMillis = options.eagerRefreshThresholdMillis;\n    if (options.transporter) {\n        this.transporter = options.transporter;\n    }\n}, _GoogleToken_requestToken = \n/**\n * Request the token from Google.\n */\nasync function _GoogleToken_requestToken() {\n    var _a, _b;\n    const iat = Math.floor(new Date().getTime() / 1000);\n    const additionalClaims = this.additionalClaims || {};\n    const payload = Object.assign({\n        iss: this.iss,\n        scope: this.scope,\n        aud: GOOGLE_TOKEN_URL,\n        exp: iat + 3600,\n        iat,\n        sub: this.sub,\n    }, additionalClaims);\n    const signedJWT = jws.sign({\n        header: { alg: 'RS256' },\n        payload,\n        secret: this.key,\n    });\n    try {\n        const r = await this.transporter.request({\n            method: 'POST',\n            url: GOOGLE_TOKEN_URL,\n            data: {\n                grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',\n                assertion: signedJWT,\n            },\n            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\n            responseType: 'json',\n            retryConfig: {\n                httpMethodsToRetry: ['POST'],\n            },\n        });\n        this.rawToken = r.data;\n        this.expiresAt =\n            r.data.expires_in === null || r.data.expires_in === undefined\n                ? undefined\n                : (iat + r.data.expires_in) * 1000;\n        return this.rawToken;\n    }\n    catch (e) {\n        this.rawToken = undefined;\n        this.tokenExpires = undefined;\n        const body = e.response && ((_a = e.response) === null || _a === void 0 ? void 0 : _a.data)\n            ? (_b = e.response) === null || _b === void 0 ? void 0 : _b.data\n            : {};\n        if (body.error) {\n            const desc = body.error_description\n                ? `: ${body.error_description}`\n                : '';\n            e.message = `${body.error}${desc}`;\n        }\n        throw e;\n    }\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gtoken/build/src/index.js\n");

/***/ })

};
;