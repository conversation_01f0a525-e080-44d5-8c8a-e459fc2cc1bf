# CRM API Documentation

## Overview

The CRM API provides programmatic access to your insurance CRM data, perfect for integrations with Make.com, Zapier, or custom applications.

## Base URL

```
https://your-domain.com/api/v1
```

## Authentication

All API requests require authentication using an API key. Include your API key in the request headers:

```
X-API-Key: your_api_key_here
```

Or as a Bearer token:

```
Authorization: Bearer your_api_key_here
```

## Rate Limiting

API requests are rate-limited based on your API key configuration (default: 1000 requests/hour).

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Maximum requests per hour
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when rate limit resets

## Response Format

All API responses follow this format:

```json
{
  "success": true,
  "data": {...},
  "message": "Optional success message",
  "pagination": {
    "limit": 100,
    "offset": 0,
    "total": 250,
    "hasMore": true
  }
}
```

Error responses:

```json
{
  "error": "Error message",
  "details": "Additional error details"
}
```

## Endpoints

### Clients

#### GET /api/v1/clients
Retrieve all clients

**Query Parameters:**
- `limit` (integer): Number of results (default: 100, max: 1000)
- `offset` (integer): Pagination offset (default: 0)
- `status` (string): Filter by status (Active, Inactive)
- `state` (string): Filter by state
- `carrier` (string): Filter by insurance carrier
- `search` (string): Search by name, email, or phone

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "phone": "555-0123",
      "state": "FL",
      "status": "Active",
      "policies": [...]
    }
  ],
  "pagination": {...}
}
```

#### POST /api/v1/clients
Create a new client

**Required Fields:**
- `first_name` (string)
- `last_name` (string)

**Optional Fields:**
- `email` (string)
- `phone` (string)
- `address` (string)
- `city` (string)
- `state` (string)
- `zip` (string)
- `status` (string, default: "Active")
- `policy` (object): Policy data to create with client

#### GET /api/v1/clients/{id}
Retrieve a specific client

#### PUT /api/v1/clients/{id}
Update a specific client

#### DELETE /api/v1/clients/{id}
Delete a specific client (requires delete permission)

### Leads

#### GET /api/v1/leads
Retrieve all leads

**Query Parameters:**
- `limit`, `offset`: Pagination
- `status` (string): Filter by status
- `stage` (string): Filter by pipeline stage
- `source` (string): Filter by lead source
- `search` (string): Search by name, email, or phone

#### POST /api/v1/leads
Create a new lead

**Required Fields:**
- `full_name` (string)

**Optional Fields:**
- `first_name` (string)
- `last_name` (string)
- `email` (string)
- `phone` (string)
- `status` (string, default: "New Lead")
- `stage` (string, default: "new_lead")
- `source` (string, default: "API")
- `estimated_value` (number)
- `priority` (string: low, medium, high)
- `notes` (string)

### Policies

#### GET /api/v1/policies
Retrieve all policies

**Query Parameters:**
- `limit`, `offset`: Pagination
- `status` (string): Filter by status
- `carrier` (string): Filter by carrier
- `state` (string): Filter by state
- `type` (string): Filter by policy type
- `client_id` (string): Filter by client ID

#### POST /api/v1/policies
Create a new policy

**Required Fields:**
- `client_id` (string): UUID of the client
- `carrier` (string): Insurance carrier name
- `state` (string): State abbreviation

**Optional Fields:**
- `plan_name` (string)
- `type` (string, default: "Health")
- `status` (string, default: "Active")
- `premium` (number)
- `gross_premium` (number)
- `member_count` (integer, default: 1)
- `policy_number` (string)
- `start_date` (date)
- `end_date` (date)

### Activities

#### GET /api/v1/activities
Retrieve all activities

**Query Parameters:**
- `limit`, `offset`: Pagination
- `type` (string): Filter by activity type
- `client_id` (string): Filter by client
- `lead_id` (string): Filter by lead
- `start_date` (date): Filter activities after date
- `end_date` (date): Filter activities before date

#### POST /api/v1/activities
Create a new activity

**Required Fields:**
- `type` (string): call, email, sms, meeting, note, task, appointment
- `title` (string): Activity title

**Optional Fields:**
- `client_id` (string): Associated client UUID
- `lead_id` (string): Associated lead UUID
- `description` (string)
- `phone_number` (string)
- `email` (string)
- `metadata` (object): Additional data

## Error Codes

- `400` - Bad Request: Invalid parameters or missing required fields
- `401` - Unauthorized: Invalid or missing API key
- `403` - Forbidden: Insufficient permissions
- `404` - Not Found: Resource not found
- `429` - Too Many Requests: Rate limit exceeded
- `500` - Internal Server Error: Server error

## Make.com Integration Examples

### Create Lead from Form Submission

```json
POST /api/v1/leads
{
  "full_name": "Jane Smith",
  "email": "<EMAIL>",
  "phone": "555-0456",
  "source": "Website Form",
  "estimated_value": 2400,
  "notes": "Interested in family health plan"
}
```

### Log Call Activity

```json
POST /api/v1/activities
{
  "type": "call",
  "title": "Follow-up call",
  "description": "Discussed plan options",
  "lead_id": "lead-uuid-here",
  "phone_number": "555-0456"
}
```

### Create Client with Policy

```json
POST /api/v1/clients
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone": "555-0123",
  "state": "FL",
  "policy": {
    "carrier": "Aetna",
    "plan_name": "Gold Plan",
    "premium": 450.00,
    "member_count": 2
  }
}
```

## Security Best Practices

1. **Keep API keys secure**: Never expose API keys in client-side code
2. **Use HTTPS**: All API requests must use HTTPS
3. **Rotate keys regularly**: Generate new API keys periodically
4. **Limit permissions**: Only grant necessary permissions (read/write/delete)
5. **Monitor usage**: Check API usage logs regularly
6. **Set appropriate rate limits**: Configure rate limits based on your needs

## Webhooks (Coming Soon)

Webhooks allow you to receive real-time notifications when data changes in your CRM.

**Supported Events:**
- `client.created` - New client added
- `client.updated` - Client information changed
- `lead.created` - New lead added
- `lead.status_changed` - Lead status/stage changed
- `policy.created` - New policy added
- `activity.created` - New activity logged

## Support

For API support or questions:
- Check the CRM settings page for API key management
- Review API usage logs for debugging
- Contact support for integration assistance
