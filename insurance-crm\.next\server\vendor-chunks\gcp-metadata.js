"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gcp-metadata";
exports.ids = ["vendor-chunks/gcp-metadata"];
exports.modules = {

/***/ "(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js":
/*!**************************************************************!*\
  !*** ./node_modules/gcp-metadata/build/src/gcp-residency.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GCE_LINUX_BIOS_PATHS = void 0;\nexports.isGoogleCloudServerless = isGoogleCloudServerless;\nexports.isGoogleComputeEngineLinux = isGoogleComputeEngineLinux;\nexports.isGoogleComputeEngineMACAddress = isGoogleComputeEngineMACAddress;\nexports.isGoogleComputeEngine = isGoogleComputeEngine;\nexports.detectGCPResidency = detectGCPResidency;\nconst fs_1 = __webpack_require__(/*! fs */ \"fs\");\nconst os_1 = __webpack_require__(/*! os */ \"os\");\n/**\n * Known paths unique to Google Compute Engine Linux instances\n */\nexports.GCE_LINUX_BIOS_PATHS = {\n    BIOS_DATE: '/sys/class/dmi/id/bios_date',\n    BIOS_VENDOR: '/sys/class/dmi/id/bios_vendor',\n};\nconst GCE_MAC_ADDRESS_REGEX = /^42:01/;\n/**\n * Determines if the process is running on a Google Cloud Serverless environment (Cloud Run or Cloud Functions instance).\n *\n * Uses the:\n * - {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n * - {@link https://cloud.google.com/functions/docs/env-var Cloud Functions environment variables}.\n *\n * @returns {boolean} `true` if the process is running on GCP serverless, `false` otherwise.\n */\nfunction isGoogleCloudServerless() {\n    /**\n     * `CLOUD_RUN_JOB` is used for Cloud Run Jobs\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     *\n     * `FUNCTION_NAME` is used in older Cloud Functions environments:\n     * - See {@link https://cloud.google.com/functions/docs/env-var Python 3.7 and Go 1.11}.\n     *\n     * `K_SERVICE` is used in Cloud Run and newer Cloud Functions environments:\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     * - See {@link https://cloud.google.com/functions/docs/env-var Cloud Functions newer runtimes}.\n     */\n    const isGFEnvironment = process.env.CLOUD_RUN_JOB ||\n        process.env.FUNCTION_NAME ||\n        process.env.K_SERVICE;\n    return !!isGFEnvironment;\n}\n/**\n * Determines if the process is running on a Linux Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on Linux GCE, `false` otherwise.\n */\nfunction isGoogleComputeEngineLinux() {\n    if ((0, os_1.platform)() !== 'linux')\n        return false;\n    try {\n        // ensure this file exist\n        (0, fs_1.statSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_DATE);\n        // ensure this file exist and matches\n        const biosVendor = (0, fs_1.readFileSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_VENDOR, 'utf8');\n        return /Google/.test(biosVendor);\n    }\n    catch (_a) {\n        return false;\n    }\n}\n/**\n * Determines if the process is running on a Google Compute Engine instance with a known\n * MAC address.\n *\n * @returns {boolean} `true` if the process is running on GCE (as determined by MAC address), `false` otherwise.\n */\nfunction isGoogleComputeEngineMACAddress() {\n    const interfaces = (0, os_1.networkInterfaces)();\n    for (const item of Object.values(interfaces)) {\n        if (!item)\n            continue;\n        for (const { mac } of item) {\n            if (GCE_MAC_ADDRESS_REGEX.test(mac)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n/**\n * Determines if the process is running on a Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on GCE, `false` otherwise.\n */\nfunction isGoogleComputeEngine() {\n    return isGoogleComputeEngineLinux() || isGoogleComputeEngineMACAddress();\n}\n/**\n * Determines if the process is running on Google Cloud Platform.\n *\n * @returns {boolean} `true` if the process is running on GCP, `false` otherwise.\n */\nfunction detectGCPResidency() {\n    return isGoogleCloudServerless() || isGoogleComputeEngine();\n}\n//# sourceMappingURL=gcp-residency.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gcp-metadata/build/src/index.js":
/*!******************************************************!*\
  !*** ./node_modules/gcp-metadata/build/src/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.gcpResidencyCache = exports.METADATA_SERVER_DETECTION = exports.HEADERS = exports.HEADER_VALUE = exports.HEADER_NAME = exports.SECONDARY_HOST_ADDRESS = exports.HOST_ADDRESS = exports.BASE_PATH = void 0;\nexports.instance = instance;\nexports.project = project;\nexports.universe = universe;\nexports.bulk = bulk;\nexports.isAvailable = isAvailable;\nexports.resetIsAvailableCache = resetIsAvailableCache;\nexports.getGCPResidency = getGCPResidency;\nexports.setGCPResidency = setGCPResidency;\nexports.requestTimeout = requestTimeout;\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/src/index.js\");\nconst jsonBigint = __webpack_require__(/*! json-bigint */ \"(rsc)/./node_modules/json-bigint/index.js\");\nconst gcp_residency_1 = __webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\");\nconst logger = __webpack_require__(/*! google-logging-utils */ \"(rsc)/./node_modules/google-logging-utils/build/src/index.js\");\nexports.BASE_PATH = '/computeMetadata/v1';\nexports.HOST_ADDRESS = 'http://***************';\nexports.SECONDARY_HOST_ADDRESS = 'http://metadata.google.internal.';\nexports.HEADER_NAME = 'Metadata-Flavor';\nexports.HEADER_VALUE = 'Google';\nexports.HEADERS = Object.freeze({ [exports.HEADER_NAME]: exports.HEADER_VALUE });\nconst log = logger.log('gcp metadata');\n/**\n * Metadata server detection override options.\n *\n * Available via `process.env.METADATA_SERVER_DETECTION`.\n */\nexports.METADATA_SERVER_DETECTION = Object.freeze({\n    'assume-present': \"don't try to ping the metadata server, but assume it's present\",\n    none: \"don't try to ping the metadata server, but don't try to use it either\",\n    'bios-only': \"treat the result of a BIOS probe as canonical (don't fall back to pinging)\",\n    'ping-only': 'skip the BIOS probe, and go straight to pinging',\n});\n/**\n * Returns the base URL while taking into account the GCE_METADATA_HOST\n * environment variable if it exists.\n *\n * @returns The base URL, e.g., http://***************/computeMetadata/v1.\n */\nfunction getBaseUrl(baseUrl) {\n    if (!baseUrl) {\n        baseUrl =\n            process.env.GCE_METADATA_IP ||\n                process.env.GCE_METADATA_HOST ||\n                exports.HOST_ADDRESS;\n    }\n    // If no scheme is provided default to HTTP:\n    if (!/^https?:\\/\\//.test(baseUrl)) {\n        baseUrl = `http://${baseUrl}`;\n    }\n    return new URL(exports.BASE_PATH, baseUrl).href;\n}\n// Accepts an options object passed from the user to the API. In previous\n// versions of the API, it referred to a `Request` or an `Axios` request\n// options object.  Now it refers to an object with very limited property\n// names. This is here to help ensure users don't pass invalid options when\n// they  upgrade from 0.4 to 0.5 to 0.8.\nfunction validate(options) {\n    Object.keys(options).forEach(key => {\n        switch (key) {\n            case 'params':\n            case 'property':\n            case 'headers':\n                break;\n            case 'qs':\n                throw new Error(\"'qs' is not a valid configuration option. Please use 'params' instead.\");\n            default:\n                throw new Error(`'${key}' is not a valid configuration option.`);\n        }\n    });\n}\nasync function metadataAccessor(type, options = {}, noResponseRetries = 3, fastFail = false) {\n    let metadataKey = '';\n    let params = {};\n    let headers = {};\n    if (typeof type === 'object') {\n        const metadataAccessor = type;\n        metadataKey = metadataAccessor.metadataKey;\n        params = metadataAccessor.params || params;\n        headers = metadataAccessor.headers || headers;\n        noResponseRetries = metadataAccessor.noResponseRetries || noResponseRetries;\n        fastFail = metadataAccessor.fastFail || fastFail;\n    }\n    else {\n        metadataKey = type;\n    }\n    if (typeof options === 'string') {\n        metadataKey += `/${options}`;\n    }\n    else {\n        validate(options);\n        if (options.property) {\n            metadataKey += `/${options.property}`;\n        }\n        headers = options.headers || headers;\n        params = options.params || params;\n    }\n    const requestMethod = fastFail ? fastFailMetadataRequest : gaxios_1.request;\n    const req = {\n        url: `${getBaseUrl()}/${metadataKey}`,\n        headers: { ...exports.HEADERS, ...headers },\n        retryConfig: { noResponseRetries },\n        params,\n        responseType: 'text',\n        timeout: requestTimeout(),\n    };\n    log.info('instance request %j', req);\n    const res = await requestMethod(req);\n    log.info('instance metadata is %s', res.data);\n    // NOTE: node.js converts all incoming headers to lower case.\n    if (res.headers[exports.HEADER_NAME.toLowerCase()] !== exports.HEADER_VALUE) {\n        throw new Error(`Invalid response from metadata service: incorrect ${exports.HEADER_NAME} header. Expected '${exports.HEADER_VALUE}', got ${res.headers[exports.HEADER_NAME.toLowerCase()] ? `'${res.headers[exports.HEADER_NAME.toLowerCase()]}'` : 'no header'}`);\n    }\n    if (typeof res.data === 'string') {\n        try {\n            return jsonBigint.parse(res.data);\n        }\n        catch (_a) {\n            /* ignore */\n        }\n    }\n    return res.data;\n}\nasync function fastFailMetadataRequest(options) {\n    var _a;\n    const secondaryOptions = {\n        ...options,\n        url: (_a = options.url) === null || _a === void 0 ? void 0 : _a.toString().replace(getBaseUrl(), getBaseUrl(exports.SECONDARY_HOST_ADDRESS)),\n    };\n    // We race a connection between DNS/IP to metadata server. There are a couple\n    // reasons for this:\n    //\n    // 1. the DNS is slow in some GCP environments; by checking both, we might\n    //    detect the runtime environment signficantly faster.\n    // 2. we can't just check the IP, which is tarpitted and slow to respond\n    //    on a user's local machine.\n    //\n    // Additional logic has been added to make sure that we don't create an\n    // unhandled rejection in scenarios where a failure happens sometime\n    // after a success.\n    //\n    // Note, however, if a failure happens prior to a success, a rejection should\n    // occur, this is for folks running locally.\n    //\n    let responded = false;\n    const r1 = (0, gaxios_1.request)(options)\n        .then(res => {\n        responded = true;\n        return res;\n    })\n        .catch(err => {\n        if (responded) {\n            return r2;\n        }\n        else {\n            responded = true;\n            throw err;\n        }\n    });\n    const r2 = (0, gaxios_1.request)(secondaryOptions)\n        .then(res => {\n        responded = true;\n        return res;\n    })\n        .catch(err => {\n        if (responded) {\n            return r1;\n        }\n        else {\n            responded = true;\n            throw err;\n        }\n    });\n    return Promise.race([r1, r2]);\n}\n/**\n * Obtain metadata for the current GCE instance.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const serviceAccount: {} = await instance('service-accounts/');\n * const serviceAccountEmail: string = await instance('service-accounts/default/email');\n * ```\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction instance(options) {\n    return metadataAccessor('instance', options);\n}\n/**\n * Obtain metadata for the current GCP project.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const projectId: string = await project('project-id');\n * const numericProjectId: number = await project('numeric-project-id');\n * ```\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction project(options) {\n    return metadataAccessor('project', options);\n}\n/**\n * Obtain metadata for the current universe.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const universeDomain: string = await universe('universe-domain');\n * ```\n */\nfunction universe(options) {\n    return metadataAccessor('universe', options);\n}\n/**\n * Retrieve metadata items in parallel.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const data = await bulk([\n *   {\n *     metadataKey: 'instance',\n *   },\n *   {\n *     metadataKey: 'project/project-id',\n *   },\n * ] as const);\n *\n * // data.instance;\n * // data['project/project-id'];\n * ```\n *\n * @param properties The metadata properties to retrieve\n * @returns The metadata in `metadatakey:value` format\n */\nasync function bulk(properties) {\n    const r = {};\n    await Promise.all(properties.map(item => {\n        return (async () => {\n            const res = await metadataAccessor(item);\n            const key = item.metadataKey;\n            r[key] = res;\n        })();\n    }));\n    return r;\n}\n/*\n * How many times should we retry detecting GCP environment.\n */\nfunction detectGCPAvailableRetries() {\n    return process.env.DETECT_GCP_RETRIES\n        ? Number(process.env.DETECT_GCP_RETRIES)\n        : 0;\n}\nlet cachedIsAvailableResponse;\n/**\n * Determine if the metadata server is currently available.\n */\nasync function isAvailable() {\n    if (process.env.METADATA_SERVER_DETECTION) {\n        const value = process.env.METADATA_SERVER_DETECTION.trim().toLocaleLowerCase();\n        if (!(value in exports.METADATA_SERVER_DETECTION)) {\n            throw new RangeError(`Unknown \\`METADATA_SERVER_DETECTION\\` env variable. Got \\`${value}\\`, but it should be \\`${Object.keys(exports.METADATA_SERVER_DETECTION).join('`, `')}\\`, or unset`);\n        }\n        switch (value) {\n            case 'assume-present':\n                return true;\n            case 'none':\n                return false;\n            case 'bios-only':\n                return getGCPResidency();\n            case 'ping-only':\n            // continue, we want to ping the server\n        }\n    }\n    try {\n        // If a user is instantiating several GCP libraries at the same time,\n        // this may result in multiple calls to isAvailable(), to detect the\n        // runtime environment. We use the same promise for each of these calls\n        // to reduce the network load.\n        if (cachedIsAvailableResponse === undefined) {\n            cachedIsAvailableResponse = metadataAccessor('instance', undefined, detectGCPAvailableRetries(), \n            // If the default HOST_ADDRESS has been overridden, we should not\n            // make an effort to try SECONDARY_HOST_ADDRESS (as we are likely in\n            // a non-GCP environment):\n            !(process.env.GCE_METADATA_IP || process.env.GCE_METADATA_HOST));\n        }\n        await cachedIsAvailableResponse;\n        return true;\n    }\n    catch (e) {\n        const err = e;\n        if (process.env.DEBUG_AUTH) {\n            console.info(err);\n        }\n        if (err.type === 'request-timeout') {\n            // If running in a GCP environment, metadata endpoint should return\n            // within ms.\n            return false;\n        }\n        if (err.response && err.response.status === 404) {\n            return false;\n        }\n        else {\n            if (!(err.response && err.response.status === 404) &&\n                // A warning is emitted if we see an unexpected err.code, or err.code\n                // is not populated:\n                (!err.code ||\n                    ![\n                        'EHOSTDOWN',\n                        'EHOSTUNREACH',\n                        'ENETUNREACH',\n                        'ENOENT',\n                        'ENOTFOUND',\n                        'ECONNREFUSED',\n                    ].includes(err.code))) {\n                let code = 'UNKNOWN';\n                if (err.code)\n                    code = err.code;\n                process.emitWarning(`received unexpected error = ${err.message} code = ${code}`, 'MetadataLookupWarning');\n            }\n            // Failure to resolve the metadata service means that it is not available.\n            return false;\n        }\n    }\n}\n/**\n * reset the memoized isAvailable() lookup.\n */\nfunction resetIsAvailableCache() {\n    cachedIsAvailableResponse = undefined;\n}\n/**\n * A cache for the detected GCP Residency.\n */\nexports.gcpResidencyCache = null;\n/**\n * Detects GCP Residency.\n * Caches results to reduce costs for subsequent calls.\n *\n * @see setGCPResidency for setting\n */\nfunction getGCPResidency() {\n    if (exports.gcpResidencyCache === null) {\n        setGCPResidency();\n    }\n    return exports.gcpResidencyCache;\n}\n/**\n * Sets the detected GCP Residency.\n * Useful for forcing metadata server detection behavior.\n *\n * Set `null` to autodetect the environment (default behavior).\n * @see getGCPResidency for getting\n */\nfunction setGCPResidency(value = null) {\n    exports.gcpResidencyCache = value !== null ? value : (0, gcp_residency_1.detectGCPResidency)();\n}\n/**\n * Obtain the timeout for requests to the metadata server.\n *\n * In certain environments and conditions requests can take longer than\n * the default timeout to complete. This function will determine the\n * appropriate timeout based on the environment.\n *\n * @returns {number} a request timeout duration in milliseconds.\n */\nfunction requestTimeout() {\n    return getGCPResidency() ? 0 : 3000;\n}\n__exportStar(__webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gcp-metadata/build/src/index.js\n");

/***/ })

};
;