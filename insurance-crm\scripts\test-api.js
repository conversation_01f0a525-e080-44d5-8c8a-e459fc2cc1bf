#!/usr/bin/env node

/**
 * API Test Script
 * 
 * This script tests the CRM API endpoints to ensure they're working correctly.
 * Replace the API_KEY and BASE_URL with your actual values.
 */

const API_KEY = 'your_api_key_here';
const BASE_URL = 'http://localhost:3000/api/v1';

// Helper function to make API requests
async function apiRequest(endpoint, method = 'GET', data = null) {
  const url = `${BASE_URL}${endpoint}`;
  
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY
    }
  };
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    console.log(`\n🔄 ${method} ${endpoint}`);
    if (data) {
      console.log('📤 Request data:', JSON.stringify(data, null, 2));
    }
    
    const response = await fetch(url, options);
    const result = await response.json();
    
    console.log(`📊 Status: ${response.status}`);
    console.log('📥 Response:', JSON.stringify(result, null, 2));
    
    return { status: response.status, data: result };
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    return { status: 500, error: error.message };
  }
}

async function runTests() {
  console.log('🚀 Starting CRM API Tests');
  console.log('=' .repeat(50));
  
  // Test 1: Get all clients
  console.log('\n📋 Test 1: Get all clients');
  await apiRequest('/clients?limit=5');
  
  // Test 2: Create a new lead
  console.log('\n📋 Test 2: Create a new lead');
  const leadData = {
    full_name: 'Test Lead',
    email: '<EMAIL>',
    phone: '555-0123',
    source: 'API Test',
    estimated_value: 1200,
    notes: 'This is a test lead created via API'
  };
  const leadResult = await apiRequest('/leads', 'POST', leadData);
  
  // Test 3: Get all leads
  console.log('\n📋 Test 3: Get all leads');
  await apiRequest('/leads?limit=5');
  
  // Test 4: Create an activity (if lead was created successfully)
  if (leadResult.status === 201 && leadResult.data.success) {
    console.log('\n📋 Test 4: Create an activity');
    const activityData = {
      type: 'note',
      title: 'API Test Activity',
      description: 'This activity was created via API test',
      lead_id: leadResult.data.data.id
    };
    await apiRequest('/activities', 'POST', activityData);
  }
  
  // Test 5: Get all activities
  console.log('\n📋 Test 5: Get all activities');
  await apiRequest('/activities?limit=5');
  
  // Test 6: Get all policies
  console.log('\n📋 Test 6: Get all policies');
  await apiRequest('/policies?limit=5');
  
  // Test 7: Test authentication error
  console.log('\n📋 Test 7: Test invalid API key');
  const originalKey = API_KEY;
  const invalidOptions = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': 'invalid_key'
    }
  };
  
  try {
    const response = await fetch(`${BASE_URL}/clients`, invalidOptions);
    const result = await response.json();
    console.log(`📊 Status: ${response.status} (Expected: 401)`);
    console.log('📥 Response:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('❌ Request failed:', error.message);
  }
  
  console.log('\n✅ API Tests Complete');
  console.log('=' .repeat(50));
}

// Instructions for running the test
if (API_KEY === 'your_api_key_here') {
  console.log('⚠️  Please update the API_KEY and BASE_URL in this script before running tests.');
  console.log('\n📝 Steps to run tests:');
  console.log('1. Create an API key in your CRM settings');
  console.log('2. Replace API_KEY with your actual key');
  console.log('3. Update BASE_URL if needed (default: http://localhost:3000/api/v1)');
  console.log('4. Run: node scripts/test-api.js');
  process.exit(1);
}

// Run the tests
runTests().catch(console.error);
